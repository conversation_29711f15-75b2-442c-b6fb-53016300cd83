package br.com.alice.business.consumers

import br.com.alice.business.services.internal.BeneficiaryWithoutSubContractException
import br.com.alice.business.services.internal.ContractIsPostPayException
import br.com.alice.business.services.internal.IdentifyMemberActivatedOnPrePaidSubContractService
import br.com.alice.business.services.internal.MemberIsNotB2BException
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.BadRequestException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.returns
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ProductType
import br.com.alice.nullvs.events.NullvsMemberActivatedEvent
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import kotlin.test.Test

class NullvsMemberActivatedConsumerTest : ConsumerTest() {

    private val identifyMemberActivatedOnPrePaidSubContractService: IdentifyMemberActivatedOnPrePaidSubContractService =
        mockk()

    private val consumer = NullvsMemberActivatedConsumer(
        identifyMemberActivatedOnPrePaidSubContractService,
    )

    private val event = NullvsMemberActivatedEvent(
        memberId = RangeUUID.generate(),
        externalId = "000001",
    )

    companion object {
        @JvmStatic
        fun exceptions() = listOf(
            ContractIsPostPayException(),
            BeneficiaryWithoutSubContractException(),
            MemberIsNotB2BException(),
        )
    }

    @ParameterizedTest(name = "should return success when exception {0} happens to avoid dlq")
    @MethodSource("br.com.alice.business.consumers.NullvsMemberActivatedConsumerTest#exceptions")
    fun `should return success when exception happens to avoid dlq`(exception: BadRequestException) = runBlocking {
        coEvery {
            identifyMemberActivatedOnPrePaidSubContractService.findOutByMemberId(any())
        } returns exception

        val result = consumer.createFirstPaymentScheduleAfterActivation(event)

        ResultAssert.assertThat(result).isSuccessWithData(false)
    }

    @Test
    fun `#should return an exception when something is wrong`() = runBlocking {
        coEvery {
            identifyMemberActivatedOnPrePaidSubContractService.findOutByMemberId(any())
        } returns Exception("")

        val result = consumer.createFirstPaymentScheduleAfterActivation(event)

        ResultAssert.assertThat(result).isFailureOfType(Exception::class)
    }

    @Test
    fun `#should return success when everything is ok`() = runBlocking {
        val member = TestModelFactory.buildMember(
            id = event.payload.memberId,
            productType = ProductType.B2B
        )

        coEvery {
            identifyMemberActivatedOnPrePaidSubContractService.findOutByMemberId(any())
        } returns member

        val result = consumer.createFirstPaymentScheduleAfterActivation(event)

        ResultAssert.assertThat(result).isSuccessWithData(member)
    }
}
