package br.com.alice.business.services.internal

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.events.MemberActivatedOnPrePaidSubcontractEvent
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.PaymentModel
import br.com.alice.data.layer.models.ProductType
import br.com.alice.person.client.MemberService
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.util.UUID
import kotlin.test.Test

class IdentifyMemberActivatedOnPrePaidSubContractServiceTest {

    private val memberService: MemberService = mockk()
    private val beneficiaryService: BeneficiaryService = mockk()
    private val companyContractService: CompanyContractService = mockk()
    private val companySubContractService: CompanySubContractService = mockk()
    private val companyService: CompanyService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()

    private val service = IdentifyMemberActivatedOnPrePaidSubContractService(
        memberService,
        beneficiaryService,
        companyContractService,
        companySubContractService,
        companyService,
        kafkaProducerService
    )

    private val memberId = UUID.randomUUID()

    private val companySubContractId = UUID.randomUUID()

    private val member = TestModelFactory.buildMember(
        id = memberId,
        productType = ProductType.B2B,
    )

    private val beneficiary = TestModelFactory.buildBeneficiary(
        memberId = member.id,
        companySubContractId = companySubContractId
    )

    private val companyContract = TestModelFactory.buildCompanyContract()

    private val company = TestModelFactory.buildCompany(
        contractIds = listOf(companyContract.id),
    )

    private val companySubContract = TestModelFactory.buildCompanySubContract(
        id = companySubContractId,
        contractId = companyContract.id,
        companyId = company.id,
        paymentType = PaymentModel.PRE_PAY,
    )

    @Test
    fun `#findOutByMemberId should throw an exception when member is not B2B`() = runBlocking {
        coEvery { memberService.get(memberId) } returns member.copy(selectedProduct = member.selectedProduct.copy(type = ProductType.B2C))

        val result = service.findOutByMemberId(memberId)

        ResultAssert.assertThat(result).isFailureOfType(MemberIsNotB2BException::class)
        coVerifyOnce { memberService.get(memberId) }

        coVerifyNone {
            beneficiaryService.findByMemberId(any())
            companySubContractService.get(any())
            companyContractService.findBySubcontractId(any())
            companyService.get(any())
            kafkaProducerService.produce(any())
        }
    }

    @Test
    fun `#findOutByMemberId should throw an exception when beneficiary does not belong to companySubContractId`() =
        runBlocking {
            coEvery { memberService.get(memberId) } returns member
            coEvery { beneficiaryService.findByMemberId(memberId) } returns beneficiary.copy(companySubContractId = null)

            val result = service.findOutByMemberId(memberId)

            ResultAssert.assertThat(result).isFailureOfType(BeneficiaryWithoutSubContractException::class)
            coVerifyOnce {
                memberService.get(memberId)
                beneficiaryService.findByMemberId(memberId)
            }

            coVerifyNone {
                companySubContractService.get(any())
                companyContractService.findBySubcontractId(any())
                companyService.get(any())
                kafkaProducerService.produce(any())
            }
        }

    @Test
    fun `#findOutByMemberId should throw an exception when companySubContract is not PRE_PAY`() = runBlocking {
        coEvery { memberService.get(memberId) } returns member
        coEvery { beneficiaryService.findByMemberId(memberId) } returns beneficiary
        coEvery { companySubContractService.get(companySubContract.id) } returns companySubContract
        coEvery { companyContractService.findBySubcontractId(companySubContract.id) } returns companyContract.copy(
            paymentType = PaymentModel.POST_PAY
        )
        coEvery { companyService.get(company.id) } returns company

        val result = service.findOutByMemberId(memberId)

        ResultAssert.assertThat(result).isFailureOfType(ContractIsPostPayException::class)
        coVerifyOnce {
            memberService.get(memberId)
            beneficiaryService.findByMemberId(memberId)
            companySubContractService.get(companySubContract.id)
            companyContractService.findBySubcontractId(companySubContract.id)
            companyService.get(company.id)
        }

        coVerifyNone {
            kafkaProducerService.produce(any())
        }
    }

    @Test
    fun `#findOutByMemberId should return the member when companySubContract is PRE_PAY`() = runBlocking {
        coEvery { memberService.get(memberId) } returns member
        coEvery { beneficiaryService.findByMemberId(memberId) } returns beneficiary
        coEvery { companySubContractService.get(companySubContract.id) } returns companySubContract
        coEvery { companyContractService.findBySubcontractId(companySubContract.id) } returns companyContract
        coEvery { companyService.get(company.id) } returns company
        coEvery {
            kafkaProducerService.produce(match<MemberActivatedOnPrePaidSubcontractEvent> {
                it.payload.companyContract == companyContract &&
                        it.payload.companySubcontract == companySubContract &&
                        it.payload.company == company
            }, companySubContract.id.toString())
        } returns mockk()

        val result = service.findOutByMemberId(memberId)

        ResultAssert.assertThat(result).isSuccessWithData(member)
        coVerifyOnce {
            memberService.get(memberId)
            beneficiaryService.findByMemberId(memberId)
            companySubContractService.get(companySubContract.id)
            companyContractService.findBySubcontractId(companySubContract.id)
            companyService.get(company.id)
            kafkaProducerService.produce(match<MemberActivatedOnPrePaidSubcontractEvent> {
                it.payload.companyContract == companyContract &&
                        it.payload.companySubcontract == companySubContract &&
                        it.payload.company == company
            }, companySubContract.id.toString())
        }
    }
}
