package br.com.alice.business.consumers

import br.com.alice.business.services.internal.BeneficiaryWithoutSubContractException
import br.com.alice.business.services.internal.ContractIsPostPayException
import br.com.alice.business.services.internal.IdentifyMemberActivatedOnPrePaidSubContractService
import br.com.alice.business.services.internal.MemberIsNotB2BException
import br.com.alice.common.extensions.coFoldException
import br.com.alice.common.logging.logger
import br.com.alice.nullvs.events.NullvsMemberActivatedEvent
import com.github.kittinunf.result.success
import kotlinx.coroutines.coroutineScope

class NullvsMemberActivatedConsumer(
    private val identifyMemberActivatedOnPrePaidSubContractService: IdentifyMemberActivatedOnPrePaidSubContractService,
) : Consumer() {

    suspend fun createFirstPaymentScheduleAfterActivation(event: NullvsMemberActivatedEvent) =
        withSubscribersEnvironment {
            coroutineScope {
                val memberId = event.payload.memberId

                logger.info(
                    "Consuming NullvsMemberActivatedEvent to create first payment schedule",
                    "event_id" to event.messageId,
                    "member_id" to memberId,
                )

                identifyMemberActivatedOnPrePaidSubContractService.findOutByMemberId(memberId)
                    .coFoldException(
                        MemberIsNotB2BException::class,
                        ContractIsPostPayException::class,
                        BeneficiaryWithoutSubContractException::class,
                    ) {
                        logger.info(
                            "Skipping first payment schedule creation due to exception",
                            "event_id" to event.messageId,
                            "member_id" to memberId,
                            "message" to it.message,
                            "exception" to it
                        )

                        false.success()
                    }
            }
        }
}
