package br.com.alice.business.routes

import br.com.alice.appointment.event.AppointmentCompletedEvent
import br.com.alice.business.consumers.*
import br.com.alice.business.events.*
import br.com.alice.clinicalaccount.event.PersonClinicalAccountCreatedEvent
import br.com.alice.clinicalaccount.event.PersonClinicalAccountDeleteEvent
import br.com.alice.common.extensions.inject
import br.com.alice.common.kafka.interfaces.AdditionalProperties
import br.com.alice.common.kafka.internals.ConsumerJob
import br.com.alice.membership.events.MemberProductChangeAppliedEvent
import br.com.alice.membership.events.MemberProductChangeCanceledEvent
import br.com.alice.membership.events.MemberProductChangeRequestedEvent
import br.com.alice.membership.model.events.UserAuthenticatedEvent
import br.com.alice.moneyin.event.MemberInvoiceGeneratedEvent
import br.com.alice.moneyin.event.MemberInvoiceGroupPaidEvent
import br.com.alice.moneyin.event.PreActivationPaymentPaidEvent
import br.com.alice.nullvs.events.NullvsMemberActivatedEvent
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.person.model.events.MemberCancelledEvent
import br.com.alice.person.model.events.MemberUpdatedEvent
import br.com.alice.person.model.events.PersonUpdatedEvent
import br.com.alice.person.model.events.ProductChangedEvent
import br.com.alice.sales_channel.events.DealContractRequestedEvent
import br.com.alice.sales_channel.events.SignerAddedToContractEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCancelledEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCompletedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleCreatedEvent
import br.com.alice.schedule.model.events.AppointmentScheduleNoShowEvent
import br.com.alice.wanda.event.PersonHealthEventUpdatedEvent
import java.time.Duration

fun ConsumerJob.Configuration.kafkaRoutes() {
    val moveToPhaseConsumer by inject<MoveToPhaseConsumer>()
    consume("move-to-phase-move-to-specific-phase", MoveToPhaseEvent.name, moveToPhaseConsumer::moveToSpecificPhase)

    val beneficiaryCreatedConsumer by inject<BeneficiaryCreatedConsumer>()
    consume(
        "beneficiary-created-create-onboarding",
        BeneficiaryCreatedEvent.name,
        beneficiaryCreatedConsumer::createOnboarding
    )
    consume(
        "beneficiary-created-create-beneficiary-hubspot",
        BeneficiaryCreatedEvent.name,
        beneficiaryCreatedConsumer::createBeneficiaryHubspot,
        additionalProperties = AdditionalProperties(delay = Duration.ofSeconds(1)),
    )
    consume(
        "beneficiary-created-create-new-dependents",
        BeneficiaryCreatedEvent.name,
        beneficiaryCreatedConsumer::createNewDependents
    )

    consume(
        "beneficiary-created-created-member-product-price",
        BeneficiaryCreatedEvent.name,
        beneficiaryCreatedConsumer::createMemberProductPrice,
    )

    val beneficiaryOnboardingPhaseChangedConsumer by inject<BeneficiaryOnboardingPhaseChangedConsumer>()
    consume(
        "beneficiary-onboarding-phase-changed",
        BeneficiaryOnboardingPhaseChangedEvent.name,
        beneficiaryOnboardingPhaseChangedConsumer::updateHubspotDeal
    )
    consume(
        "beneficiary-onboarding-phase-changed-handle-health-declaration-appointment",
        BeneficiaryOnboardingPhaseChangedEvent.name,
        beneficiaryOnboardingPhaseChangedConsumer::handleHealthDeclarationAppointment
    )
    consume(
        "beneficiary-onboarding-phase-changed-handle-cpts-confirmation",
        BeneficiaryOnboardingPhaseChangedEvent.name,
        beneficiaryOnboardingPhaseChangedConsumer::handleCptsConfirmation
    )
    consume(
        "beneficiary-onboarding-phase-changed-log-info",
        BeneficiaryOnboardingPhaseChangedEvent.name,
        beneficiaryOnboardingPhaseChangedConsumer::logInfo
    )
    consume(
        "beneficiary-onboarding-phase-changed-handle-send-cpts-minor-person",
        BeneficiaryOnboardingPhaseChangedEvent.name,
        beneficiaryOnboardingPhaseChangedConsumer::handlePhaseChangeAfterHealthDeclarationAnswered
    )
    consume(
        "beneficiary-onboarding-phase-changed-generate-health-declaration-contract",
        BeneficiaryOnboardingPhaseChangedEvent.name,
        beneficiaryOnboardingPhaseChangedConsumer::generateContractB2B
    )
    consume(
        "beneficiary-onboarding-phase-changed-update-previous-phase",
        BeneficiaryOnboardingPhaseChangedEvent.name,
        beneficiaryOnboardingPhaseChangedConsumer::updatePreviousPhase
    )
    consume(
        "beneficiary-onboarding-phase-changed-activate-if-on-last-phase",
        BeneficiaryOnboardingPhaseChangedEvent.name,
        beneficiaryOnboardingPhaseChangedConsumer::activeBeneficiaryIfOnLastPhase
    )
    consume(
        "beneficiary-onboarding-phase-changed-handle-process-finished",
        BeneficiaryOnboardingPhaseChangedEvent.name,
        beneficiaryOnboardingPhaseChangedConsumer::handleProcessFinished
    )

    val memberInvoiceGeneratedConsumer by inject<MemberInvoiceGeneratedConsumer>()
    consume(
        "member-invoice-generated-store-report",
        MemberInvoiceGeneratedEvent.name,
        memberInvoiceGeneratedConsumer::storeReport
    )

    val beneficiaryCanceledConsumer by inject<BeneficiaryCanceledConsumer>()
    consume(
        "beneficiary-canceled-cancel-dependents",
        BeneficiaryCanceledEvent.name,
        beneficiaryCanceledConsumer::cancelDependentsFromBeneficiary
    )

    val userAuthenticatedConsumer by inject<UserAuthenticatedConsumer>()
    consume(
        "user-authenticated-start-beneficiary-onboarding",
        UserAuthenticatedEvent.name,
        userAuthenticatedConsumer::startBeneficiaryOnboarding
    )

    val appointmentScheduleConsumer by inject<AppointmentScheduleConsumer>()
    consume(
        "appointment-schedule-created-move-to-health-declaration-appointment-scheduled-phase",
        AppointmentScheduleCreatedEvent.name,
        appointmentScheduleConsumer::handleScheduleCreated
    )
    consume(
        "appointment-schedule-canceled-move-to-health-declaration-appointment-phase",
        AppointmentScheduleCancelledEvent.name,
        appointmentScheduleConsumer::handleScheduleCanceled
    )
    consume(
        "appointment-schedule-no-show-move-to-health-declaration-appointment-phase",
        AppointmentScheduleNoShowEvent.name,
        appointmentScheduleConsumer::handleScheduleNoShow
    )
    consume(
        "appointment-schedule-completed-move-to-waiting-cpts-application-phase",
        AppointmentScheduleCompletedEvent.name,
        appointmentScheduleConsumer::handleScheduleCompleted
    )

    val productChangedConsumer by inject<ProductChangedConsumer>()
    consume("product-changed-create-beneficiary", ProductChangedEvent.name, productChangedConsumer::createBeneficiary)

    val memberUpdatedConsumer by inject<MemberUpdatedConsumer>()
    consume(
        "member-updated-update-beneficiary-member-status",
        MemberUpdatedEvent.name,
        memberUpdatedConsumer::updateMemberStatusOnBeneficiary
    )

    val memberContractCreatedConsumer by inject<MemberContractCreatedConsumer>()
    consume(
        "member-contract-created-move-to-cpts-confirmation-or-contract-signature-phase",
        MemberContractCreatedEvent.name,
        memberContractCreatedConsumer::moveToCptsConfirmationOrToContractSignaturePhase
    )

    val memberContractSignedConsumer by inject<MemberContractSignedConsumer>()
    consume(
        "member-contract-signed-move-to-registration",
        MemberContractSignedEvent.name,
        memberContractSignedConsumer::moveBeneficiaryOnboardingToRegistration
    )

    val beneficiaryCompiledViewConsumer by inject<BeneficiaryCompiledViewConsumer>()
    consume(
        "beneficiary-archived-delete-bcv",
        BeneficiaryArchivedEvent.name,
        beneficiaryCompiledViewConsumer::deleteBeneficiaryCompiledViewFromBeneficiaryArchived
    )
    consume(
        "upsert-beneficiary-compiled-view-upsert",
        UpsertBeneficiaryCompiledViewEvent.name,
        beneficiaryCompiledViewConsumer::upsertBeneficiaryCompiledViewEvent,
        additionalProperties = AdditionalProperties(delay = Duration.ofSeconds(1))
    )
    consume(
        "beneficiary-created-redirect-event",
        BeneficiaryCreatedEvent.name,
        beneficiaryCompiledViewConsumer::redirectBeneficiaryCreatedEventToUpsertBeneficiaryCompiledView
    )
    consume(
        "beneficiary-onboarding-updated-redirect-event",
        BeneficiaryOnboardingUpdatedEvent.name,
        beneficiaryCompiledViewConsumer::redirectBeneficiaryOnboardingUpdatedEventToUpsertBeneficiaryCompiledView
    )
    consume(
        "beneficiary-onboarding-phase-changed-redirect-event",
        BeneficiaryOnboardingPhaseChangedEvent.name,
        beneficiaryCompiledViewConsumer::redirectBeneficiaryOnboardingPhaseChangedEventToUpsertBeneficiaryCompiledView
    )
    consume(
        "pending-activation-beneficiary-membership-redirect-event",
        PendingActivationBeneficiaryMembershipEvent.NAME,
        beneficiaryCompiledViewConsumer::redirectPendingActivationBeneficiaryMembershipToUpsertBeneficiaryCompiledView
    )
    consume(
        "member-updated-redirect-event",
        MemberUpdatedEvent.name,
        beneficiaryCompiledViewConsumer::redirectMemberUpdatedEventToUpsertBeneficiaryCompiledView
    )
    consume(
        "clinical-account-deleted-redirect-event",
        PersonClinicalAccountDeleteEvent.name,
        beneficiaryCompiledViewConsumer::redirectPersonClinicalAccountDeleteEventToUpsertBeneficiaryCompiledView
    )
    consume(
        "clinical-account-created-redirect-event",
        PersonClinicalAccountCreatedEvent.name,
        beneficiaryCompiledViewConsumer::redirectPersonClinicalAccountCreatedEventToUpsertBeneficiaryCompiledView
    )
    consume(
        "member-product-change-canceled-event-redirect",
        MemberProductChangeCanceledEvent.name,
        beneficiaryCompiledViewConsumer::redirectMemberProductChangeCanceledEventToUpsertBeneficiaryCompiledView
    )
    consume(
        "member-product-change-requested-event-redirect",
        MemberProductChangeRequestedEvent.name,
        beneficiaryCompiledViewConsumer::redirectMemberProductChangeRequestedEventToUpsertBeneficiaryCompiledView
    )
    consume(
        "beneficiary-updated-redirect-event",
        BeneficiaryUpdatedEvent.name,
        beneficiaryCompiledViewConsumer::redirectBeneficiaryUpdatedEventToUpsertBeneficiaryCompiledView
    )

    consume(
        "person-updated-redirect-event",
        PersonUpdatedEvent.name,
        beneficiaryCompiledViewConsumer::redirectPersonUpdatedEventEventToUpsertBeneficiaryCompiledView
    )

    val cassiMemberConsumer by inject<CassiMemberConsumer>()
    consume(
        "cassi-member-requested-updated",
        RequestCassiMemberUpdateEvent.name,
        cassiMemberConsumer::handleRequestedCassiMemberUpdate
    )
    consume("delete-cassi-member-member-canceled", MemberCancelledEvent.name, cassiMemberConsumer::handleMemberCanceled)

    val companyBillingAccountablePartyUpdatedConsumer by inject<CompanyBillingAccountablePartyUpdatedConsumer>()
    consume(
        "update-all-person-billing-accountable-party-related-to-company",
        CompanyBillingAccountablePartyUpdatedEvent.name,
        companyBillingAccountablePartyUpdatedConsumer::handleCompanyBillingAccountablePartyUpdated
    )

    val backfillConsumer by inject<BackfillConsumer>()
    consume(
        "request-update-beneficiary-dependent-employee-bind",
        RequestUpdateDependentEmployeeBind.name,
        backfillConsumer::updateDependentEmployeeBind
    )
    consume(
        "request-create-subcontract-from-company",
        RequestCreateSubcontractFromCompanyEvent.name,
        backfillConsumer::createSubcontractFromCompany
    )

    val companyConsumer by inject<CompanyConsumer>()
    consume(
        "cognito-company-upsert-requested",
        CognitoCompanyUpsertRequestedEvent.name,
        companyConsumer::handleCognitoCompanyUpsertRequested
    )
    consume(
        "company-upserted",
        CompanyUpsertedEvent.name,
        companyConsumer::upsertCompanyDeal
    )

    val subcontractConsumer by inject<SubContractConsumer>()
    consume(
        "sub-contract-created-associate-beneficiaries",
        CompanySubContractCreatedEvent.name,
        subcontractConsumer::handleCompanySubContractCreatedEvent
    )

    val beneficiaryConsumer by inject<BeneficiaryConsumer>()
    consume(
        "cognito-company-upserted",
        CognitoCompanyUpsertedEvent.name,
        beneficiaryConsumer::handleCognitoCompanyUpserted
    )

    val companyStaffConsumer by inject<CompanyStaffConsumer>()
    consume(
        "cognito-company-upserted-create-company-staff",
        CognitoCompanyUpsertedEvent.name,
        companyStaffConsumer::handleCognitoCompanyUpserted,
    )

    val beneficiaryOnboardingFinishedConsumer by inject<BeneficiaryOnboardingFinishedConsumer>()
    consume(
        "beneficiary-onboarding-finished-handle-company-finished",
        BeneficiaryOnboardingFinishedEvent.name,
        beneficiaryOnboardingFinishedConsumer::handleBeneficiaryOnboardingFinished
    )

    val companyContractConsumer by inject<CompanyContractConsumer>()
    consume(
        "create-first-invoice-group-payment-company-contract-created",
        CompanyContractCreatedEvent.name,
        companyContractConsumer::handleCreateCompanyFirstPayment
    )

    consume(
        "create-first-invoice-group-payment-company-contract-updated",
        CompanyContractUpdatedEvent.name,
        companyContractConsumer::handleUpdateCompanyFirstPayment
    )

    val memberInvoiceGroupPaidEvent by inject<MemberInvoiceGroupPaidConsumer>()
    consume(
        "member-invoice-group-paid-activate-member-when-invoice-group-is-paid",
        MemberInvoiceGroupPaidEvent.name,
        memberInvoiceGroupPaidEvent::activateMemberWhenInvoiceGroupIsPaid
    )

    val dealConsumer by inject<DealConsumer>()
    consume(
        "deal-contract-requested-consumer",
        DealContractRequestedEvent.name,
        dealConsumer::handleDealContractRequested
    )

    val memberActivatedConsumer by inject<MemberActivatedConsumer>()
    consume(
        "update-deal-after-all-beneficiaries-have-been-activated",
        MemberActivatedEvent.name,
        memberActivatedConsumer::updateDealAfterAllBeneficiariesHaveBeenActivated
    )

    consume(
        "active-dependents-for-employees",
        MemberActivatedEvent.name,
        memberActivatedConsumer::activeDependentsFromBeneficiary,
    )

    val personHealthConsumer by inject<PersonHealthConsumer>()
    consume(
        "person-event-updated-move-to-send-cpts-phase",
        PersonHealthEventUpdatedEvent.name,
        personHealthConsumer::moveToSendCpts
    )

    val companyActivationFilesConsumer by inject<CompanyActivationFilesConsumer>()
    consume(
        "company-activation-files-requested-event-handle-creation",
        CompanyActivationFilesRequestedEvent.name,
        companyActivationFilesConsumer::handleCompanyActivationFilesRequest
    )

    val cognitoRawDataConsumer by inject<CognitoRawDataConsumer>()
    consume(
        "cognito-raw-data-send-to-fly-webhook",
        CognitoCompanyUpsertRequestedRawEvent.name,
        cognitoRawDataConsumer::handleCognitoRawData
    )

    val pendingActivationBeneficiaryMembershipConsumer by inject<PendingActivationBeneficiaryMembershipConsumer>()
    consume(
        "pending-activation-beneficiary-send-braze",
        PendingActivationBeneficiaryMembershipEvent.NAME,
        pendingActivationBeneficiaryMembershipConsumer::sendBrazeEvent
    )

    val eNotasWebhookConsumer by inject<ENotasWebhookConsumer>()
    consume(
        "e-notas-webhook-consumer",
        ENotasTaxReceiptWebhookReceivedEvent.name,
        eNotasWebhookConsumer::consume
    )

    val invoiceGroupTaxReceiptConsumer by inject<InvoiceGroupTaxReceiptConsumer>()
    consume(
        "invoice-group-tax-receipt-received-consumer",
        InvoiceGroupTaxReceiptReceivedEvent.name,
        invoiceGroupTaxReceiptConsumer::consume
    )

    val invoiceLiquidationTaxReceiptConsumer by inject<InvoiceLiquidationTaxReceiptConsumer>()
    consume(
        "invoice-liquidation-tax-receipt-received-consumer",
        InvoiceLiquidationTaxReceiptReceivedEvent.name,
        invoiceLiquidationTaxReceiptConsumer::consume
    )

    val memberTelegramTrackingConsumer by inject<MemberTelegramTrackingConsumer>()
    consume(
        "member-telegram-tracking-sync-consumer",
        MemberTelegramTrackingRegisteredEvent.name,
        memberTelegramTrackingConsumer::syncTrackingCode
    )

    val memberProductChangeAppliedConsumer by inject<MemberProductChangeAppliedConsumer>()
    consume(
        "member-product-change-applied-consumer",
        MemberProductChangeAppliedEvent.name,
        memberProductChangeAppliedConsumer::handleMemberProductChangeApplied
    )

    val companyExportRequestedByStaffConsumer by inject<CompanyExportRequestedByStaffConsumer>()
    consume(
        "company-export-requested-by-staff-consumer",
        CompanyExportRequestedByStaffEvent.NAME,
        companyExportRequestedByStaffConsumer::handler
    )

    val signerAddedToContractConsumer by inject<SignerAddedToContractConsumer>()
    consume(
        "signer-added-to-contract-consumer",
        SignerAddedToContractEvent.name,
        signerAddedToContractConsumer::handler
    )

    val personCPTUpdatedConsumer by inject<PersonCPTUpdatedConsumer>()
    consume(
        "move-to-contract-signature-phase-when-cpts-is-empty",
        AppointmentCompletedEvent.name,
        personCPTUpdatedConsumer::moveToContractSignaturePhaseWhenCptsIsEmpty
    )

    val preActivationPaymentPaidConsumer by inject<PreActivationPaymentPaidConsumer>()
    consume(
        "activate-beneficiaries-when-pre-activation-payment-is-paid",
        PreActivationPaymentPaidEvent.name,
        preActivationPaymentPaidConsumer::activateBeneficiariesWhenPreActivationPaymentIsPaid
    )

    val memberActivationReturnedCreatedConsumer by inject<NullvsMemberActivatedConsumer>()
    consume(
        "totvs-member-activation",
        NullvsMemberActivatedEvent.name,
        memberActivationReturnedCreatedConsumer::createFirstPaymentScheduleAfterActivation
    )

    val memberForSubContractPrePayActivatedConsumer by inject<MemberForSubcontractPrePayActivatedConsumer>()
    consume(
        "member-activated-on-pre-paid-sub-contract",
        MemberActivatedOnPrePaidSubcontractEvent.name,
        memberForSubContractPrePayActivatedConsumer::createFirstPaymentSchedule
    )

    val enrichCompanyEmptyAddressConsumer by inject<EnrichCompanyEmptyAddressConsumer>()
    consume(
        "enrich-company-empty-address",
        CognitoCompanyUpsertedEvent.name,
        enrichCompanyEmptyAddressConsumer::fillAddressForCompany
    )

    val billingAccountablePartyAddressSyncConsumer by inject<BillingAccountablePartyAddressSyncConsumer>()
    consume(
        "copy-company-address-to-billing-accountable-party-address",
        CompanyUpsertedEvent.name,
        billingAccountablePartyAddressSyncConsumer::copyCompanyAddressToBillingAccountablePartyAddress
    )
}
