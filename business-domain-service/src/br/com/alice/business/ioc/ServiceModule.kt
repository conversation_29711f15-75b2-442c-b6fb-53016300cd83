package br.com.alice.business.ioc

import br.com.alice.business.SERVICE_NAME
import br.com.alice.business.ServiceConfig
import br.com.alice.business.client.*
import br.com.alice.business.clients.CognitoClient
import br.com.alice.business.clients.ENotasGWClient
import br.com.alice.business.clients.ReceitaFederalClient
import br.com.alice.business.communication.Mailer
import br.com.alice.business.consumers.*
import br.com.alice.business.controllers.BackFillController
import br.com.alice.business.controllers.BeneficiaryCompiledViewBackfillController
import br.com.alice.business.controllers.BeneficiaryCompiledViewRecurrentController
import br.com.alice.business.controllers.CompanyActivationFilesBackfillController
import br.com.alice.business.controllers.CompanyProductConfigurationBackfillController
import br.com.alice.business.controllers.CompanyStaffBackfillController
import br.com.alice.business.controllers.RecurrentController
import br.com.alice.business.services.*
import br.com.alice.business.services.client.cassi.CassiClient
import br.com.alice.business.services.client.cassi.CassiClientImpl
import br.com.alice.business.services.client.clicksign.ClicksignClient
import br.com.alice.business.services.client.clicksign.ClicksignClientImpl
import br.com.alice.business.services.factory.MemberContractFactory
import br.com.alice.business.services.factory.internal.B2BContractFactory
import br.com.alice.business.services.factory.internal.ContractFactory
import br.com.alice.business.services.internal.B2bBatchInvoiceReportService
import br.com.alice.business.services.internal.BeneficiaryActivationService
import br.com.alice.business.services.internal.CompanyActivationFilesService as InternalCompanyActivationFilesService
import br.com.alice.business.services.internal.BeneficiaryHubspotService as InternalBeneficiaryHubspotService
import br.com.alice.business.services.internal.IdentifyMemberActivatedOnPrePaidSubContractService
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.RunningMode
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.redis.CacheFactory
import br.com.alice.common.storage.FileStorage
import br.com.alice.common.storage.LocalFileStorage
import br.com.alice.common.storage.S3FileStorage
import br.com.alice.communication.crm.hubspot.b2b.BusinessHubspotSalesCrmPipeline
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotClientImpl
import br.com.alice.communication.crm.hubspot.b2b.client.HubspotClientLocal
import br.com.alice.communication.crm.sales.b2b.BusinessSalesCrmPipeline
import br.com.alice.communication.email.sender.EmailSenderClient
import br.com.alice.communication.email.sender.SimpleEmailServiceClient
import br.com.alice.communication.email.template.EmailTemplateClient
import br.com.alice.communication.email.template.SimpleEmailServiceTemplateClient
import com.typesafe.config.ConfigFactory
import io.ktor.client.engine.apache.Apache
import io.ktor.client.engine.apache5.Apache5
import io.ktor.server.config.HoconApplicationConfig
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.bind
import org.koin.dsl.module
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider
import software.amazon.awssdk.auth.credentials.AwsSessionCredentials
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.ses.SesClient

val ServiceModule = module(createdAtStart = true) {
    val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    // Configuration
    single { config }

    // Mailer service config
    single { Mailer(get()) }

    when (val environment = BaseConfig.instance.runningMode) {
        RunningMode.PRODUCTION -> {
            single<EmailSenderClient> { SimpleEmailServiceClient(get()) }
            single<EmailTemplateClient> { SimpleEmailServiceTemplateClient(get()) }

            single<FileStorage> { S3FileStorage() }
        }

        else -> {
            val awsCredentials = AwsSessionCredentials.create(
                config.property(
                    "${environment.value.lowercase()}.AWS_ACCESS_KEY_ID"
                ).getString(),
                config.property(
                    "${environment.value.lowercase()}.AWS_SECRET_ACCESS_KEY"
                ).getString(),
                config.property(
                    "${environment.value.lowercase()}.AWS_SESSION_TOKEN"
                ).getString()
            )

            val awsCredentialsProvider = AwsCredentialsProvider { awsCredentials }

            val sesClient = SesClient.builder().apply {
                credentialsProvider(awsCredentialsProvider)
                region(Region.US_EAST_1)
            }.build()

            single<EmailSenderClient> { SimpleEmailServiceClient(sesClient) }
            single<EmailTemplateClient> { SimpleEmailServiceTemplateClient(sesClient) }

            single<FileStorage> { LocalFileStorage() }
        }
    }

    val cache = CacheFactory.newInstance("business-domain-service-cache")

    // Servers
    loadServiceServers("br.com.alice.business.services")

    // Exposed services
    singleOf(::BeneficiaryCompiledViewServiceImpl) bind BeneficiaryCompiledViewService::class
    singleOf(::BeneficiaryHubspotServiceImpl) bind BeneficiaryHubspotService::class
    singleOf(::BeneficiaryOnboardingServiceImpl) bind BeneficiaryOnboardingService::class
    singleOf(::BeneficiaryServiceImpl) bind BeneficiaryService::class
    singleOf(::CassiMemberServiceImpl) bind CassiMemberService::class
    singleOf(::CompanyActivationFilesServiceImpl) bind CompanyActivationFilesService::class
    singleOf(::CompanyContractDocumentServiceImpl) bind CompanyContractDocumentService::class
    singleOf(::CompanyContractServiceImpl) bind CompanyContractService::class
    singleOf(::CompanyDataExtractionServiceImpl) bind CompanyDataExtractionService::class
    singleOf(::CompanyGracePeriodServiceImpl) bind CompanyGracePeriodService::class
    singleOf(::CompanyProductConfigurationServiceImpl) bind CompanyProductConfigurationService::class
    singleOf(::CompanyProductPriceListingServiceImpl) bind CompanyProductPriceListingService::class
    singleOf(::CompanyServiceImpl) bind CompanyService::class
    singleOf(::CompanyStaffServiceImpl) bind CompanyStaffService::class
    singleOf(::CompanySubContractServiceImpl) bind CompanySubContractService::class
    singleOf(::InvoiceGroupTaxReceiptServiceImpl) bind InvoiceGroupTaxReceiptService::class
    singleOf(::InvoiceLiquidationTaxReceiptServiceImpl) bind InvoiceLiquidationTaxReceiptService::class
    singleOf(::MailerServiceImpl) bind MailerService::class
    singleOf(::MemberContractServiceImpl) bind MemberContractService::class
    singleOf(::MemberTelegramTrackingServiceImpl) bind MemberTelegramTrackingService::class
    singleOf(::SignerAddedToContractServiceImpl) bind SignerAddedToContractService::class
    singleOf(::StandardCostServiceImpl) bind StandardCostService::class

    single<ClicksignClient> {
        ClicksignClientImpl(
            ServiceConfig.Clicksign.config(),
            Apache.create(),
        )
    }

    single<CassiClient> {
        CassiClientImpl(
            ServiceConfig.Cassi.config(),
            Apache.create(),
            cache
        )
    }

    // Internal services
    single { B2BContractFactory(get()) } bind ContractFactory::class
    single { MemberContractFactory(get(), get(), getAll()) }
    singleOf(::BeneficiaryActivationService)
    singleOf(::B2bBatchInvoiceReportService)
    singleOf(::InternalBeneficiaryHubspotService)
    singleOf(::InternalCompanyActivationFilesService)
    singleOf(::IdentifyMemberActivatedOnPrePaidSubContractService)

    // Controllers
    single { HealthController(SERVICE_NAME) }
    single {
        BackFillController(
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
        )
    }
    singleOf(::BeneficiaryCompiledViewBackfillController)
    singleOf(::CompanyProductConfigurationBackfillController)
    singleOf(::CompanyStaffBackfillController)
    singleOf(::RecurrentController)
    singleOf(::BeneficiaryCompiledViewRecurrentController)
    singleOf(::CompanyActivationFilesBackfillController)

    // Consumers
    singleOf(::AppointmentScheduleConsumer)
    singleOf(::BackfillConsumer)
    singleOf(::BeneficiaryCanceledConsumer)
    singleOf(::BeneficiaryCompiledViewConsumer)
    singleOf(::BeneficiaryConsumer)
    singleOf(::BeneficiaryCreatedConsumer)
    singleOf(::BeneficiaryOnboardingFinishedConsumer)
    singleOf(::BeneficiaryOnboardingPhaseChangedConsumer)
    singleOf(::CassiMemberConsumer)
    singleOf(::CognitoRawDataConsumer)
    singleOf(::CompanyActivationFilesConsumer)
    singleOf(::CompanyBillingAccountablePartyUpdatedConsumer)
    singleOf(::CompanyConsumer)
    singleOf(::CompanyContractConsumer)
    singleOf(::CompanyExportRequestedByStaffConsumer)
    singleOf(::CompanyStaffConsumer)
    singleOf(::DealConsumer)
    singleOf(::ENotasWebhookConsumer)
    singleOf(::InvoiceGroupTaxReceiptConsumer)
    singleOf(::InvoiceLiquidationTaxReceiptConsumer)
    singleOf(::MemberActivatedConsumer)
    singleOf(::MemberContractCreatedConsumer)
    singleOf(::MemberContractSignedConsumer)
    singleOf(::MemberInvoiceGeneratedConsumer)
    singleOf(::MemberInvoiceGroupPaidConsumer)
    singleOf(::MemberProductChangeAppliedConsumer)
    singleOf(::MemberTelegramTrackingConsumer)
    singleOf(::MemberUpdatedConsumer)
    singleOf(::MoveToPhaseConsumer)
    singleOf(::PendingActivationBeneficiaryMembershipConsumer)
    singleOf(::PersonHealthConsumer)
    singleOf(::ProductChangedConsumer)
    singleOf(::SignerAddedToContractConsumer)
    singleOf(::SubContractConsumer)
    singleOf(::UserAuthenticatedConsumer)
    singleOf(::PersonCPTUpdatedConsumer)
    singleOf(::PreActivationPaymentPaidConsumer)
    singleOf(::NullvsMemberActivatedConsumer)
    singleOf(::MemberForSubcontractPrePayActivatedConsumer)
    singleOf(::EnrichCompanyEmptyAddressConsumer)
    singleOf(::BillingAccountablePartyAddressSyncConsumer)

    // Hubspot configuration
    single<BusinessSalesCrmPipeline> {
        val client = if (ServiceConfig.isProduction && ServiceConfig.runningMode != RunningMode.TEST)
            HubspotClientImpl(
                ServiceConfig.Crm.hubspotConfig(),
                Apache5.create()
            ) else HubspotClientLocal()

        BusinessHubspotSalesCrmPipeline(client)
    }

    // Clients
    single { ReceitaFederalClient(ServiceConfig.Receitaws.authToken) }
    single { CognitoClient() }
    single { ENotasGWClient() }
}
