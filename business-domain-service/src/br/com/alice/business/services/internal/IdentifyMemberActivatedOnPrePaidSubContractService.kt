package br.com.alice.business.services.internal

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.client.CompanyContractService
import br.com.alice.business.client.CompanyService
import br.com.alice.business.client.CompanySubContractService
import br.com.alice.business.events.MemberActivatedOnPrePaidSubcontractEvent
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.PaymentModel
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class IdentifyMemberActivatedOnPrePaidSubContractService(
    private val memberService: MemberService,
    private val beneficiaryService: BeneficiaryService,
    private val companyContractService: CompanyContractService,
    private val companySubContractService: CompanySubContractService,
    private val companyService: CompanyService,
    private val kafkaProducerService: KafkaProducerService
) {
    suspend fun findOutByMemberId(memberId: UUID) = coroutineScope {
        getB2BMemberOrThrowAnException(memberId)
            .flatMap { member ->
                getCompanySubContractIdByBeneficiary(member.id).flatMap { subcontractId ->
                    companySubContractService.get(subcontractId)
                }.map { subcontract ->
                    logger.info(
                        "Creating first payment schedule for member",
                        "member_id" to memberId,
                        "subcontract_id" to subcontract.id,
                    )

                    val companyContractDeferred =
                        async { companyContractService.findBySubcontractId(subcontract.id).get() }
                    val companyDeferred = async { companyService.get(subcontract.companyId).get() }
                    val companyContract =
                        companyContractDeferred.await()

                    val company = companyDeferred.await()

                    if (companyContract.paymentType == PaymentModel.POST_PAY) {
                        logger.info("Company contract payment type is POST_PAY, skipping schedule creation")
                        throw ContractIsPostPayException()
                    }

                    kafkaProducerService.produce(
                        MemberActivatedOnPrePaidSubcontractEvent(
                            companyContract = companyContract,
                            companySubcontract = subcontract,
                            company = company,
                        ),
                        partitionKey = subcontract.id.toString(),
                    )

                    member
                }
            }
    }

    private suspend fun getB2BMemberOrThrowAnException(memberId: UUID) = memberService.get(memberId)
        .map { member ->
            member.takeIf { it.isB2B }
                ?: throw MemberIsNotB2BException()
        }

    private suspend fun getCompanySubContractIdByBeneficiary(memberId: UUID) =
        beneficiaryService.findByMemberId(memberId).map {
            it.companySubContractId
                ?: throw BeneficiaryWithoutSubContractException()

        }
}
