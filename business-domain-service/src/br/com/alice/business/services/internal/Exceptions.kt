package br.com.alice.business.services.internal

import br.com.alice.common.core.exceptions.BadRequestException


class MemberIsNotB2BException(
    message: String = "The member is not B2B, skipping schedule creation.",
    code: String = "member_is_not_b2b",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class BeneficiaryWithoutSubContractException(
    message: String = "This beneficiary does not belong to a subcontract, skipping schedule creation.",
    code: String = "beneficiary_without_sub_contract",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class ContractIsPostPayException(
    message: String = "This company contract payment type is POST_PAY, skipping schedule creation.",
    code: String = "contract_is_post_pay",
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

