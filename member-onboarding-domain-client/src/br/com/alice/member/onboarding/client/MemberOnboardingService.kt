package br.com.alice.member.onboarding.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.MemberOnboarding
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType
import br.com.alice.data.layer.models.MemberOnboardingReferencedLink
import br.com.alice.data.layer.models.MemberOnboardingReferencedLinkModel
import br.com.alice.member.onboarding.model.OnboardingVersion
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface MemberOnboardingService : Service {
   override val namespace get() = "member_onboarding"
   override val serviceName get() = "member_onboarding"


   suspend fun get(id: UUID): Result<MemberOnboarding, Throwable>

   suspend fun getByPersonId(personId: PersonId): Result<MemberOnboarding, Throwable>

   suspend fun getByPersonIdAndCompleted(personId: PersonId, completed: Boolean): Result<MemberOnboarding, Throwable>

   suspend fun getByPersonIdCheckingSteps(personId: PersonId, isMemberActive: Boolean): Result<MemberOnboarding, Throwable>

   suspend fun updateStepStatus(
      memberOnboardingId: UUID,
      stepType: MemberOnboardingStepType,
      stepStatus: MemberOnboardingStepStatus = MemberOnboardingStepStatus.COMPLETED
   ): Result<MemberOnboarding, Throwable>

   suspend fun createMemberOnboardingIfNecessary(personId: PersonId): Result<Boolean, Throwable>

   suspend fun addReferencedLink(memberOnboardingId: UUID, referencedLinks: List<MemberOnboardingReferencedLink>, shouldOverride: Boolean = false): Result<MemberOnboarding, Throwable>

   suspend fun findAllPaginatedByCompleted(completed: Boolean, offset: Int, limit: Int): Result<List<MemberOnboarding>, Throwable>

   suspend fun findAllByReferencedLinkModelPaginated(offset: Int, limit: Int, model: MemberOnboardingReferencedLinkModel): Result<List<MemberOnboarding>, Throwable>

   suspend fun findAllPaginated(offset: Int, limit: Int): Result<List<MemberOnboarding>, Throwable>

   suspend fun findByIds(ids: List<UUID>): Result<List<MemberOnboarding>, Throwable>

   suspend fun delete(model: MemberOnboarding): Result<Boolean, Throwable>

   suspend fun deleteList(models: List<MemberOnboarding>): Result<List<Boolean>, Throwable>

   suspend fun getOnboardingVersion(personId: PersonId): Result<OnboardingVersion, Throwable>

   suspend fun getAliceInfoVersion(personId: PersonId): Result<Int, Throwable>

   suspend fun getByPersonIds(personIds: List<PersonId>): Result<List<MemberOnboarding>, Throwable>

   suspend fun getOrCreateAbOnboardingVersion(personId: PersonId): Result<OnboardingVersion, Throwable>
}
