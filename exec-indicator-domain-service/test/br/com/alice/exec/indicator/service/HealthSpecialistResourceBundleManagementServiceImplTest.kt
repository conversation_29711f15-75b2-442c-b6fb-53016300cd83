package br.com.alice.exec.indicator.service

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentRecommendationLevel
import br.com.alice.data.layer.models.HealthSpecialistResourceBundle
import br.com.alice.data.layer.models.HealthSpecialistResourceBundleServiceType
import br.com.alice.data.layer.models.MedicalSpecialtyType
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.data.layer.models.ResourceBundleSpecialty
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPrice
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPricing
import br.com.alice.data.layer.models.TierType
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleService
import br.com.alice.exec.indicator.events.HealthSpecialistResourceBundleUpsertedEvent
import br.com.alice.exec.indicator.models.AppointmentRecommendationBondRequest
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePaginatedResponse
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePatchRequest
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePatchResponse
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePricingStatus
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundleWithPricingData
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistMedicalSpecialtyResponse
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistRequestFilters
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistResourceBundleResponse
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistResourceBundleWithCountResponse
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtiesWithCount
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyAggregate
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyAggregateCount
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyResponse
import br.com.alice.exec.indicator.models.ResourceSuggestedProcedure
import br.com.alice.exec.indicator.models.SecondaryResourcesTransport
import br.com.alice.exec.indicator.service.internal.ResourceBundleSpecialtyPricingService
import br.com.alice.exec.indicator.service.internal.ResourceBundleSpecialtyService
import br.com.alice.provider.client.MedicalSpecialtyService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class HealthSpecialistResourceBundleManagementServiceImplTest {

    private val healthSpecialistResourceBundleService: HealthSpecialistResourceBundleService = mockk()
    private val resourceBundleSpecialtyService: ResourceBundleSpecialtyService = mockk()
    private val producerService: KafkaProducerService = mockk()
    private val resourceBundleSpecialtyPricingService: ResourceBundleSpecialtyPricingService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val service = HealthSpecialistResourceBundleManagementServiceImpl(
        healthSpecialistResourceBundleService,
        resourceBundleSpecialtyService,
        producerService,
        resourceBundleSpecialtyPricingService,
        medicalSpecialtyService,
    )

    @BeforeTest
    fun setup() {
        clearAllMocks()
    }


    private val bundle = HealthSpecialistResourceBundle(
        id = RangeUUID.generate(),
        primaryTuss = "TUSS123",
        description = "Original description",
        status = Status.INACTIVE,
        serviceType = HealthSpecialistResourceBundleServiceType.EXAM
    )

    private val resourceBundleSpecialty = ResourceBundleSpecialty(
        id = UUID.randomUUID(),
        healthSpecialistResourceBundleId = bundle.id,
        medicalSpecialtyId = UUID.randomUUID(),
        status = Status.ACTIVE,
        pricingStatus = PricingStatus.PRICED,
    )

    val medicalSpecialty = TestModelFactory.buildMedicalSpecialty(
        id = resourceBundleSpecialty.medicalSpecialtyId,
    )

    val resourceBundleSpecialtyPricing = ResourceBundleSpecialtyPricing(
        id = UUID.randomUUID(),
        resourceBundleSpecialtyId = resourceBundleSpecialty.id,
        prices = listOf(
            ResourceBundleSpecialtyPrice(
                tier = SpecialistTier.ULTRA_EXPERT,
                productTier = TierType.TIER_0,
                price = BigDecimal(100)
            ),
        ),
        beginAt = LocalDate.now(),
        endAt = null
    )

    val id = UUID.randomUUID()
    val existingSpecialtyId = UUID.randomUUID()
    val existingResourceBundleSpecialty = ResourceBundleSpecialty(
        id = UUID.randomUUID(),
        healthSpecialistResourceBundleId = id,
        medicalSpecialtyId = existingSpecialtyId,
        status = Status.INACTIVE,
        pricingStatus = PricingStatus.NOT_PRICED
    )

    @Test
    fun `test update - add new specialty`() = runBlocking {
        val request = HealthSpecialistResourceBundlePatchRequest(
            aliceDescription = "Updated description",
            status = Status.ACTIVE,
            medicalSpecialtyIds = listOf(UUID.randomUUID())
        )
        val bundle = HealthSpecialistResourceBundle(
            id = id,
            primaryTuss = "TUSS123",
            description = "Original description",
            status = Status.INACTIVE,
            serviceType = HealthSpecialistResourceBundleServiceType.EXAM
        )
        val updatedBundle = bundle.copy(
            secondaryResources = bundle.secondaryResources,
            executionAmount = bundle.executionAmount,
            executionEnvironment = bundle.executionEnvironment,
            description = request.aliceDescription!!,
            status = request.status!!,
            serviceType = bundle.serviceType
        )
        val response = HealthSpecialistResourceBundlePatchResponse(
            id = updatedBundle.id,
            secondaryResources = updatedBundle.secondaryResources.map { SecondaryResourcesTransport(it) },
            executionAmount = updatedBundle.executionAmount,
            executionEnvironment = updatedBundle.executionEnvironment,
            aliceDescription = updatedBundle.description,
            status = updatedBundle.status,
            serviceType = updatedBundle.serviceType,
            medicalSpecialtyIds = request.medicalSpecialtyIds!!
        )

        coEvery {
            healthSpecialistResourceBundleService.get(id)
        } returns Result.success(bundle)
        coEvery {
            healthSpecialistResourceBundleService.update(
                match { it.id == id  &&
                        it.primaryTuss == bundle.primaryTuss &&
                        it.description == request.aliceDescription &&
                        it.status == request.status &&
                        it.serviceType == bundle.serviceType &&
                        it.executionAmount == bundle.executionAmount &&
                        it.executionEnvironment == bundle.executionEnvironment &&
                        it.secondaryResources == bundle.secondaryResources
                }
            )
        } returns Result.success(updatedBundle)
        coEvery {
            resourceBundleSpecialtyService.getByResourceBundleId(id)
        } returns Result.success(emptyList())
        coEvery {
            resourceBundleSpecialtyService.addList(match {
                it.size == 1 &&
                        it[0].healthSpecialistResourceBundleId == id &&
                        it[0].medicalSpecialtyId == request.medicalSpecialtyIds!![0] &&
                        it[0].status == Status.ACTIVE
            })
        } returns Result.success(
            listOf(
                ResourceBundleSpecialty(
                    id = UUID.randomUUID(),
                    healthSpecialistResourceBundleId = id,
                    medicalSpecialtyId = request.medicalSpecialtyIds!![0],
                    status = Status.ACTIVE,
                    pricingStatus = PricingStatus.PRICED
                )
            )
        )
        coEvery { producerService.produce(
            match {
                (it as HealthSpecialistResourceBundleUpsertedEvent).payload.healthSpecialistResourceBundleId == id
            }
        ) } returns ProducerResult(LocalDateTime.now(), "1", 1)

        val result = service.update(id, request)

        assertThat(result).isSuccessWithData(response)
        coVerify { healthSpecialistResourceBundleService.get(id) }
        coVerify { healthSpecialistResourceBundleService.update(any()) }
        coVerify { resourceBundleSpecialtyService.getByResourceBundleId(id) }
        coVerify { resourceBundleSpecialtyService.addList(any()) }
        coVerify { producerService.produce(any()) }
    }

    @Test
    fun `test update - add new specialty and remove one, should also end validity of the pricing of the removed`() = runBlocking {
        val existingResourceBundleSpecialtyPricing = ResourceBundleSpecialtyPricing(
            id = UUID.randomUUID(),
            resourceBundleSpecialtyId = existingResourceBundleSpecialty.id,
            prices = emptyList(),
            beginAt = LocalDate.now().minusDays(5),
            endAt = null
        )

        val newSpecialtyId = UUID.randomUUID()
        val request = HealthSpecialistResourceBundlePatchRequest(
            aliceDescription = "Updated description",
            status = Status.ACTIVE,
            medicalSpecialtyIds = listOf(newSpecialtyId)
        )
        val bundle = HealthSpecialistResourceBundle(
            id = id,
            primaryTuss = "TUSS123",
            description = "Original description",
            status = Status.INACTIVE,
            serviceType = HealthSpecialistResourceBundleServiceType.EXAM
        )
        val updatedBundle = bundle.copy(
            secondaryResources = bundle.secondaryResources,
            executionAmount = bundle.executionAmount,
            executionEnvironment = bundle.executionEnvironment,
            description = request.aliceDescription!!,
            status = request.status!!,
            serviceType = bundle.serviceType
        )
        val response = HealthSpecialistResourceBundlePatchResponse(
            id = updatedBundle.id,
            secondaryResources = updatedBundle.secondaryResources.map { SecondaryResourcesTransport(it) },
            executionAmount = updatedBundle.executionAmount,
            executionEnvironment = updatedBundle.executionEnvironment,
            aliceDescription = updatedBundle.description,
            status = updatedBundle.status,
            serviceType = updatedBundle.serviceType,
            medicalSpecialtyIds = request.medicalSpecialtyIds!!
        )

        coEvery { healthSpecialistResourceBundleService.get(id) } returns Result.success(bundle)
        coEvery {
            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(listOf(existingResourceBundleSpecialty.id))
        } returns Result.success(
            listOf(existingResourceBundleSpecialtyPricing)
        )
        coEvery { healthSpecialistResourceBundleService.update(any()) } returns Result.success(updatedBundle)
        coEvery { resourceBundleSpecialtyService.getByResourceBundleId(id) } returns Result.success(
            listOf(
                existingResourceBundleSpecialty
            )
        )
        coEvery {
            resourceBundleSpecialtyService.addList(
                match {
                    it.size == 1 &&
                            it[0].healthSpecialistResourceBundleId == id &&
                            it[0].medicalSpecialtyId == newSpecialtyId &&
                            it[0].status == Status.ACTIVE
                }
            )
        } returns Result.success(
            listOf(
                ResourceBundleSpecialty(
                    id = UUID.randomUUID(),
                    healthSpecialistResourceBundleId = id,
                    medicalSpecialtyId = newSpecialtyId,
                    status = Status.ACTIVE,
                    pricingStatus = PricingStatus.NOT_PRICED
                )
            )
        )
        coEvery {
            resourceBundleSpecialtyService.updateList(
                match {
                    it.size == 1 &&
                            it[0].healthSpecialistResourceBundleId == id &&
                            it[0].medicalSpecialtyId == existingSpecialtyId &&
                            it[0].status == Status.INACTIVE
                }
            )
        } returns Result.success(
            listOf(
                ResourceBundleSpecialty(
                    id = UUID.randomUUID(),
                    healthSpecialistResourceBundleId = id,
                    medicalSpecialtyId = existingSpecialtyId,
                    status = Status.INACTIVE,
                    pricingStatus = PricingStatus.NOT_PRICED
                )
            )
        )
        coEvery {
            resourceBundleSpecialtyPricingService.updateList(
                match {
                    it.size == 1 &&
                            it[0].id == existingResourceBundleSpecialtyPricing.id &&
                            it[0].endAt == LocalDate.now()
                }
            )
        } returns Result.success(
            listOf(
                ResourceBundleSpecialtyPricing(
                    id = existingResourceBundleSpecialtyPricing.id,
                    resourceBundleSpecialtyId = existingResourceBundleSpecialtyPricing.resourceBundleSpecialtyId,
                    prices = emptyList(),
                    beginAt = existingResourceBundleSpecialtyPricing.beginAt,
                    endAt = LocalDate.now()
                )
            )
        )
        coEvery {
            producerService.produce(
                match {
                    (it as HealthSpecialistResourceBundleUpsertedEvent).payload.healthSpecialistResourceBundleId == id
                }
            )
        } returns ProducerResult(LocalDateTime.now(), "1", 1)

        val result = service.update(id, request)

        assertThat(result).isSuccessWithData(response)
        coVerify { healthSpecialistResourceBundleService.get(id) }
        coVerify { healthSpecialistResourceBundleService.update(any()) }
        coVerify { resourceBundleSpecialtyService.getByResourceBundleId(id) }
        coVerify { resourceBundleSpecialtyService.addList(any()) }
        coVerify { resourceBundleSpecialtyService.updateList(any()) }
        coVerifyOnce { resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(any()) }
        coVerifyOnce { resourceBundleSpecialtyPricingService.updateList(any()) }
        coVerify { producerService.produce(any()) }
    }

    @Test
    fun `test update - add new specialty and remove one, should also delete the pricing of the removed if it has a future validity`() = runBlocking {
        val existingResourceBundleSpecialtyPricing = ResourceBundleSpecialtyPricing(
            id = UUID.randomUUID(),
            resourceBundleSpecialtyId = existingResourceBundleSpecialty.id,
            prices = emptyList(),
            beginAt = LocalDate.now().minusDays(5),
            endAt = null
        )

        val newSpecialtyId = UUID.randomUUID()
        val request = HealthSpecialistResourceBundlePatchRequest(
            aliceDescription = "Updated description",
            status = Status.ACTIVE,
            medicalSpecialtyIds = listOf(newSpecialtyId)
        )
        val bundle = HealthSpecialistResourceBundle(
            id = id,
            primaryTuss = "TUSS123",
            description = "Original description",
            status = Status.INACTIVE,
            serviceType = HealthSpecialistResourceBundleServiceType.EXAM
        )
        val updatedBundle = bundle.copy(
            secondaryResources = bundle.secondaryResources,
            executionAmount = bundle.executionAmount,
            executionEnvironment = bundle.executionEnvironment,
            description = request.aliceDescription!!,
            status = request.status!!,
            serviceType = bundle.serviceType
        )
        val response = HealthSpecialistResourceBundlePatchResponse(
            id = updatedBundle.id,
            secondaryResources = updatedBundle.secondaryResources.map { SecondaryResourcesTransport(it) },
            executionAmount = updatedBundle.executionAmount,
            executionEnvironment = updatedBundle.executionEnvironment,
            aliceDescription = updatedBundle.description,
            status = updatedBundle.status,
            serviceType = updatedBundle.serviceType,
            medicalSpecialtyIds = request.medicalSpecialtyIds!!
        )

        coEvery { healthSpecialistResourceBundleService.get(id) } returns Result.success(bundle)
        coEvery {
            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(listOf(existingResourceBundleSpecialty.id))
        } returns Result.success(
            listOf(existingResourceBundleSpecialtyPricing.copy(beginAt = LocalDate.now().plusDays(5)))
        )
        coEvery { healthSpecialistResourceBundleService.update(any()) } returns Result.success(updatedBundle)
        coEvery { resourceBundleSpecialtyService.getByResourceBundleId(id) } returns Result.success(
            listOf(
                existingResourceBundleSpecialty
            )
        )
        coEvery {
            resourceBundleSpecialtyService.addList(
                match {
                    it.size == 1 &&
                            it[0].healthSpecialistResourceBundleId == id &&
                            it[0].medicalSpecialtyId == newSpecialtyId &&
                            it[0].status == Status.ACTIVE
                }
            )
        } returns Result.success(
            listOf(
                ResourceBundleSpecialty(
                    id = UUID.randomUUID(),
                    healthSpecialistResourceBundleId = id,
                    medicalSpecialtyId = newSpecialtyId,
                    status = Status.ACTIVE,
                    pricingStatus = PricingStatus.NOT_PRICED
                )
            )
        )
        coEvery {
            resourceBundleSpecialtyService.updateList(
                match {
                    it.size == 1 &&
                            it[0].healthSpecialistResourceBundleId == id &&
                            it[0].medicalSpecialtyId == existingSpecialtyId &&
                            it[0].status == Status.INACTIVE
                }
            )
        } returns Result.success(
            listOf(
                ResourceBundleSpecialty(
                    id = UUID.randomUUID(),
                    healthSpecialistResourceBundleId = id,
                    medicalSpecialtyId = existingSpecialtyId,
                    status = Status.INACTIVE,
                    pricingStatus = PricingStatus.NOT_PRICED
                )
            )
        )
        coEvery {
            resourceBundleSpecialtyPricingService.deleteList(
                match {
                    it.size == 1 &&
                            it[0].id == existingResourceBundleSpecialtyPricing.id
                }
            )
        } returns Result.success(
            listOf(
                true
            )
        )
        coEvery {
            producerService.produce(
                match {
                    (it as HealthSpecialistResourceBundleUpsertedEvent).payload.healthSpecialistResourceBundleId == id
                }
            )
        } returns ProducerResult(LocalDateTime.now(), "1", 1)

        val result = service.update(id, request)

        assertThat(result).isSuccessWithData(response)
        coVerify { healthSpecialistResourceBundleService.get(id) }
        coVerify { healthSpecialistResourceBundleService.update(any()) }
        coVerify { resourceBundleSpecialtyService.getByResourceBundleId(id) }
        coVerify { resourceBundleSpecialtyService.addList(any()) }
        coVerify { resourceBundleSpecialtyService.updateList(any()) }
        coVerifyOnce { resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(any()) }
        coVerifyOnce { resourceBundleSpecialtyPricingService.deleteList(any()) }
        coVerify { producerService.produce(any()) }
    }

    @Test
    fun `test update - reactivate an existing inactive specialty`() = runBlocking {
        val request = HealthSpecialistResourceBundlePatchRequest(
            aliceDescription = "Updated description",
            status = Status.ACTIVE,
            medicalSpecialtyIds = listOf(existingSpecialtyId)
        )
        val bundle = HealthSpecialistResourceBundle(
            id = id,
            primaryTuss = "TUSS123",
            description = "Original description",
            status = Status.INACTIVE,
            serviceType = HealthSpecialistResourceBundleServiceType.EXAM
        )
        val updatedBundle = bundle.copy(
            description = request.aliceDescription!!,
            status = request.status!!,
        )
        val response = HealthSpecialistResourceBundlePatchResponse(
            id = updatedBundle.id,
            secondaryResources = updatedBundle.secondaryResources.map { SecondaryResourcesTransport(it) },
            executionAmount = updatedBundle.executionAmount,
            executionEnvironment = updatedBundle.executionEnvironment,
            aliceDescription = updatedBundle.description,
            status = updatedBundle.status,
            serviceType = updatedBundle.serviceType,
            medicalSpecialtyIds = request.medicalSpecialtyIds!!
        )

        coEvery { healthSpecialistResourceBundleService.get(id) } returns Result.success(bundle)
        coEvery { healthSpecialistResourceBundleService.update(any()) } returns Result.success(updatedBundle)
        coEvery { resourceBundleSpecialtyService.getByResourceBundleId(id) } returns Result.success(
            listOf(
                existingResourceBundleSpecialty
            )
        )
        coEvery {
            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(listOf(existingResourceBundleSpecialty.id))
        } returns Result.success(
            emptyList()
        )
        coEvery {
            resourceBundleSpecialtyService.updateList(
                match {
                    it.size == 1 &&
                            it[0].healthSpecialistResourceBundleId == id &&
                            it[0].medicalSpecialtyId == existingSpecialtyId &&
                            it[0].status == Status.ACTIVE
                }
            )
        } returns Result.success(
            listOf(
                ResourceBundleSpecialty(
                    id = UUID.randomUUID(),
                    healthSpecialistResourceBundleId = id,
                    medicalSpecialtyId = existingSpecialtyId,
                    status = Status.ACTIVE,
                    pricingStatus = PricingStatus.NOT_PRICED
                )
            )
        )
        coEvery { producerService.produce(
            match {
                (it as HealthSpecialistResourceBundleUpsertedEvent).payload.healthSpecialistResourceBundleId == id
            }
        ) } returns ProducerResult(LocalDateTime.now(), "1", 1)

        val result = service.update(id, request)

        assertThat(result).isSuccessWithData(response)
        coVerify { healthSpecialistResourceBundleService.get(id) }
        coVerify { healthSpecialistResourceBundleService.update(any()) }
        coVerify { resourceBundleSpecialtyService.getByResourceBundleId(id) }
        coVerify { resourceBundleSpecialtyService.updateList(any()) }
        coVerifyOnce { resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(any()) }
        coVerify { producerService.produce(any()) }
    }

    @Test
    fun `test update - no change in specialties`() = runBlocking {
        val id = UUID.randomUUID()
        val existingSpecialtyId = UUID.randomUUID()
        val existingResourceBundleSpecialty = ResourceBundleSpecialty(
            id = UUID.randomUUID(),
            healthSpecialistResourceBundleId = id,
            medicalSpecialtyId = existingSpecialtyId,
            status = Status.ACTIVE,
            pricingStatus = PricingStatus.NOT_PRICED
        )
        val request = HealthSpecialistResourceBundlePatchRequest(
            aliceDescription = "Updated description",
            status = Status.ACTIVE,
            medicalSpecialtyIds = listOf(existingSpecialtyId)
        )
        val bundle = HealthSpecialistResourceBundle(
            id = id,
            primaryTuss = "TUSS123",
            description = "Original description",
            status = Status.INACTIVE,
            serviceType = HealthSpecialistResourceBundleServiceType.EXAM
        )
        val updatedBundle = bundle.copy(
            description = request.aliceDescription!!,
            status = request.status!!,
        )
        val response = HealthSpecialistResourceBundlePatchResponse(
            id = updatedBundle.id,
            secondaryResources = updatedBundle.secondaryResources.map { SecondaryResourcesTransport(it) },
            executionAmount = updatedBundle.executionAmount,
            executionEnvironment = updatedBundle.executionEnvironment,
            aliceDescription = updatedBundle.description,
            status = updatedBundle.status,
            serviceType = updatedBundle.serviceType,
            medicalSpecialtyIds = request.medicalSpecialtyIds!!
        )

        coEvery { healthSpecialistResourceBundleService.get(id) } returns Result.success(bundle)
        coEvery {
            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(listOf(existingResourceBundleSpecialty.id))
        } returns Result.success(
            emptyList()
        )
        coEvery { healthSpecialistResourceBundleService.update(any()) } returns Result.success(updatedBundle)
        coEvery { resourceBundleSpecialtyService.getByResourceBundleId(id) } returns Result.success(
            listOf(
                existingResourceBundleSpecialty
            )
        )
        coEvery { producerService.produce(
            match {
                (it as HealthSpecialistResourceBundleUpsertedEvent).payload.healthSpecialistResourceBundleId == id
            }
        ) } returns ProducerResult(LocalDateTime.now(), "1", 1)

        val result = service.update(id, request)

        assertThat(result).isSuccessWithData(response)
        coVerify { healthSpecialistResourceBundleService.get(id) }
        coVerify { healthSpecialistResourceBundleService.update(any()) }
        coVerify { resourceBundleSpecialtyService.getByResourceBundleId(id) }
        coVerify(exactly = 0) { resourceBundleSpecialtyService.addList(any()) }
        coVerify(exactly = 0) { resourceBundleSpecialtyService.updateList(any()) }
        coVerifyOnce { resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(any()) }
        coVerify { producerService.produce(any()) }
    }

    @Test
    fun `test update - specialties not passed`() = runBlocking {
        val request = HealthSpecialistResourceBundlePatchRequest(
            aliceDescription = "Updated description",
            status = Status.ACTIVE,
            medicalSpecialtyIds = null
        )
        val bundle = HealthSpecialistResourceBundle(
            id = id,
            primaryTuss = "TUSS123",
            description = "Original description",
            status = Status.INACTIVE,
            serviceType = HealthSpecialistResourceBundleServiceType.EXAM
        )
        val updatedBundle = bundle.copy(
            description = request.aliceDescription!!,
            status = request.status!!,
        )
        val response = HealthSpecialistResourceBundlePatchResponse(
            id = updatedBundle.id,
            secondaryResources = updatedBundle.secondaryResources.map { SecondaryResourcesTransport(it) },
            executionAmount = updatedBundle.executionAmount,
            executionEnvironment = updatedBundle.executionEnvironment,
            aliceDescription = updatedBundle.description,
            status = updatedBundle.status,
            serviceType = updatedBundle.serviceType,
            medicalSpecialtyIds = listOf(existingSpecialtyId)
        )

        coEvery { healthSpecialistResourceBundleService.get(id) } returns Result.success(bundle)
        coEvery { healthSpecialistResourceBundleService.update(
            match {
                it.id == id &&
                        it.primaryTuss == bundle.primaryTuss &&
                        it.description == request.aliceDescription &&
                        it.status == request.status &&
                        it.serviceType == bundle.serviceType &&
                        it.executionAmount == bundle.executionAmount &&
                        it.executionEnvironment == bundle.executionEnvironment &&
                        it.secondaryResources == bundle.secondaryResources
            }
        ) } returns Result.success(updatedBundle)
        coEvery { resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(
            listOf(existingResourceBundleSpecialty.id)
        ) } returns Result.success(
            emptyList()
        )
        coEvery { resourceBundleSpecialtyService.getByResourceBundleId(id) } returns Result.success(
            listOf(
                existingResourceBundleSpecialty
            )
        )
        coEvery { producerService.produce(
            match {
                (it as HealthSpecialistResourceBundleUpsertedEvent).payload.healthSpecialistResourceBundleId == id
            }
        ) } returns ProducerResult(LocalDateTime.now(), "1", 1)

        val result = service.update(id, request)

        assertThat(result).isSuccessWithData(response)
        coVerify { healthSpecialistResourceBundleService.get(id) }
        coVerify { healthSpecialistResourceBundleService.update(any()) }
        coVerify { resourceBundleSpecialtyService.getByResourceBundleId(id) }
        coVerify(exactly = 0) { resourceBundleSpecialtyService.addList(any()) }
        coVerify(exactly = 0) { resourceBundleSpecialtyService.updateList(any()) }
        coVerifyOnce { resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(any()) }
        coVerify { producerService.produce(any()) }
    }

    @Test
    fun `list should return filtered health specialist resource bundles for not priced codes`() = runBlocking {
        val resourceBundleSpecialtyPricing = ResourceBundleSpecialtyPricing(
            id = UUID.randomUUID(),
            resourceBundleSpecialtyId = resourceBundleSpecialty.id,
            prices = emptyList(),
            beginAt = LocalDate.now(),
            endAt = null
        )

        val query = "some-query"
        val range = 0..10
        val paginatedResponse = HealthSpecialistResourceBundlePaginatedResponse(
            items = listOf(
                HealthSpecialistResourceBundleWithPricingData(
                    healthSpecialistResourceBundle = bundle,
                    medicalSpecialtyIds = listOf(resourceBundleSpecialty.medicalSpecialtyId),
                    pricingStatus = HealthSpecialistResourceBundlePricingStatus.PENDING,
                    specialtiesCount = 1,
                    allSpecialtiesCount = 1
                )
            ),
            total = 1
        )

        coEvery {
            healthSpecialistResourceBundleService.findByFilters(query, range)
        } returns listOf(bundle).success()

        coEvery {
            healthSpecialistResourceBundleService.countByFilters(query)
        } returns 1.success()

        coEvery {
            resourceBundleSpecialtyService.getByResourceBundleIds(listOf(bundle.id))
        } returns listOf(resourceBundleSpecialty).success()

        coEvery {
            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(
                listOf(resourceBundleSpecialty.id)
            )
        } returns listOf(resourceBundleSpecialtyPricing).success()

        coEvery {
            medicalSpecialtyService.getActivesByType(
                type = MedicalSpecialtyType.SPECIALTY,
                excludeInternal = true,
            )
        } returns listOf(TestModelFactory.buildMedicalSpecialty()).success()

        val result = service.list(query, range)

        assertThat(result).isSuccessWithData(paginatedResponse)
        coVerifyOnce { healthSpecialistResourceBundleService.findByFilters(any(), any()) }
        coVerifyOnce { healthSpecialistResourceBundleService.countByFilters(any()) }
        coVerifyOnce { resourceBundleSpecialtyService.getByResourceBundleIds(any()) }
        coVerifyOnce { resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(any()) }
        coVerifyOnce { medicalSpecialtyService.getActivesByType(
            any(),
            any()
        ) }
    }

    @Test
    fun `list should return filtered health specialist resource bundles for priced codes`() = runBlocking {
        val query = "some-query"
        val range = 0..10
        val paginatedResponse = HealthSpecialistResourceBundlePaginatedResponse(
            items = listOf(
                HealthSpecialistResourceBundleWithPricingData(
                    healthSpecialistResourceBundle = bundle,
                    medicalSpecialtyIds = listOf(resourceBundleSpecialty.medicalSpecialtyId),
                    pricingStatus = HealthSpecialistResourceBundlePricingStatus.PENDING,
                    specialtiesCount = 1,
                    allSpecialtiesCount = 1
                )
            ),
            total = 1
        )

        coEvery {
            healthSpecialistResourceBundleService.findByFilters(query, range)
        } returns listOf(bundle).success()

        coEvery {
            healthSpecialistResourceBundleService.countByFilters(query)
        } returns 1.success()

        coEvery {
            resourceBundleSpecialtyService.getByResourceBundleIds(listOf(bundle.id))
        } returns listOf(resourceBundleSpecialty).success()

        coEvery {
            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(
                listOf(resourceBundleSpecialty.id)
            )
        } returns listOf(resourceBundleSpecialtyPricing).success()

        coEvery {
            medicalSpecialtyService.getActivesByType(
                type = MedicalSpecialtyType.SPECIALTY,
                excludeInternal = true,
            )
        } returns listOf(TestModelFactory.buildMedicalSpecialty()).success()

        val result = service.list(query, range)

        assertThat(result).isSuccessWithData(paginatedResponse)
        coVerifyOnce { healthSpecialistResourceBundleService.findByFilters(any(), any()) }
        coVerifyOnce { healthSpecialistResourceBundleService.countByFilters(any(), any()) }
        coVerifyOnce { resourceBundleSpecialtyService.getByResourceBundleIds(any(), any()) }
        coVerifyOnce { resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(any()) }
        coVerifyOnce { medicalSpecialtyService.getActivesByType(
            any(), any()
        ) }
    }

    @Test
    fun `getMedicalSpecialtiesRelatedToResourceBundle should return medical specialties related to resource bundle`() = runBlocking {
        val resourceBundleId = UUID.randomUUID()
        val medicalSpecialtyId = UUID.randomUUID()
        val resourceBundleSpecialty = TestModelFactory.buildResourceBundleSpecialty(
            id = UUID.randomUUID(),
            resourceBundleId = resourceBundleId,
            specialtyId = medicalSpecialtyId,
            status = Status.ACTIVE
        )

        val medicalSpecialty = TestModelFactory.buildMedicalSpecialty(
            id = medicalSpecialtyId,
            type = MedicalSpecialtyType.SPECIALTY,
        )

        val resourceBundleSpecialtyPricing = TestModelFactory.buildResourceBundleSpecialtyPricing(
            id = UUID.randomUUID(),
            resourceBundleSpecialtyId = resourceBundleSpecialty.id,
        )

        val expectedResponse = ResourceBundleSpecialtiesWithCount(
            count = 1,
            specialties = listOf(
                ResourceBundleSpecialtyResponse(
                    id = resourceBundleSpecialty.id,
                    name = medicalSpecialty.name,
                    isTherapy = false,
                    pricingStatus = HealthSpecialistResourceBundlePricingStatus.PENDING,
                    currentBeginAt = resourceBundleSpecialtyPricing.beginAt,
                    currentEndAt = null,
                    hasScheduledPriceChange = false,
                    medicalSpecialtyId = medicalSpecialty.id
                )
            )
        )

        coEvery {
            resourceBundleSpecialtyService.getByResourceBundleId(resourceBundleId)
        } returns listOf(resourceBundleSpecialty).success()

        coEvery {
            medicalSpecialtyService.getByIds(
                listOf(medicalSpecialtyId),
            )
        } returns listOf(medicalSpecialty).success()

        coEvery {
            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(
                listOf(resourceBundleSpecialty.id)
            )
        } returns listOf(resourceBundleSpecialtyPricing).success()

        val result = service.getMedicalSpecialtiesRelatedToResourceBundle(resourceBundleId, IntRange(0, 10))

        assertThat(result).isSuccessWithData(expectedResponse)

        coVerifyOnce {
            resourceBundleSpecialtyService.getByResourceBundleId(any())
        }
        coVerifyOnce {
            medicalSpecialtyService.getByIds(any())
        }
        coVerifyOnce {
            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(any())
        }
    }

    @Test
    fun `#getPendingResourceSpecialtyBundlesForPricing should return resource bundle specialties that are not priced`() = runBlocking {
        val expectedResponse = listOf(
            ResourceBundleSpecialty(
                id = UUID.randomUUID(),
                healthSpecialistResourceBundleId = UUID.randomUUID(),
                medicalSpecialtyId = UUID.randomUUID(),
                status = Status.ACTIVE,
                pricingStatus = PricingStatus.PRICED,
            )
        )

        coEvery {
            resourceBundleSpecialtyService.getByPricingStatus(
                PricingStatus.NOT_PRICED,
                filterActive = true
            )
        } returns expectedResponse.success()

        val result = service.getPendingResourceSpecialtyBundlesForPricing()

        assertThat(result).isSuccessWithData(expectedResponse)
    }

    @Test
    fun `#getResourceBundleSpecialtyPricingList should return results when query is passed`() = runBlocking {
        val filters = PricingForHealthSpecialistRequestFilters(query = "some-query")
        val range = 0..10

        coEvery { healthSpecialistResourceBundleService.findByFilters(filters.query, range) } returns listOf(bundle).success()
        coEvery { healthSpecialistResourceBundleService.countByFilters(filters.query) } returns 1.success()
        coEvery { resourceBundleSpecialtyService.getByResourceBundleIds(listOf(bundle.id)) } returns listOf(resourceBundleSpecialty).success()
        coEvery { resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(listOf(resourceBundleSpecialty.id)) } returns listOf(resourceBundleSpecialtyPricing).success()
        coEvery { medicalSpecialtyService.getByIds(any()) } returns listOf(medicalSpecialty).success()

        val result = service.getResourceBundleSpecialtyPricingList(filters, range)

        assertThat(result).isSuccessWithData(
            PricingForHealthSpecialistResourceBundleWithCountResponse(
                count = 1,
                items = listOf(
                    PricingForHealthSpecialistResourceBundleResponse(
                        healthSpecialistResourceBundleId = bundle.id,
                        primaryTuss = bundle.primaryTuss,
                        aliceCode = bundle.code,
                        description = bundle.description,
                        pendingNumber = 0,
                        serviceType = bundle.serviceType.description,
                        medicalSpecialties = listOf(
                            PricingForHealthSpecialistMedicalSpecialtyResponse(
                                medicalSpecialtyId = medicalSpecialty.id,
                                description = medicalSpecialty.name,
                                prices = listOf(
                                    ResourceBundleSpecialtyPrice(
                                        tier = resourceBundleSpecialtyPricing.prices.first().tier,
                                        price = resourceBundleSpecialtyPricing.prices.first().price,
                                        productTier = resourceBundleSpecialtyPricing.prices.first().productTier
                                    )
                                ),
                                pendingNumber = 9,
                                beginAt = resourceBundleSpecialtyPricing.beginAt,
                                changeBeginAt = null,
                                resourceBundleSpecialtyId = resourceBundleSpecialty.id
                            )
                        )
                    )
                )
            )
        )

        coVerifyOnce {
            healthSpecialistResourceBundleService.findByFilters(any(), any())
        }
        coVerifyOnce {
            healthSpecialistResourceBundleService.countByFilters(any())
        }

        coVerifyOnce {
            resourceBundleSpecialtyService.getByResourceBundleIds(any())
        }

        coVerifyOnce {
            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(any())
        }

        coVerifyOnce {
            medicalSpecialtyService.getByIds(any())
        }
    }

    @Test
    fun `#getResourceBundleSpecialtyPricingList should return results when medicalSpecialtyIds filter is passed`() = runBlocking<Unit> {
        val filters = PricingForHealthSpecialistRequestFilters(medicalSpecialtyIds = listOf(UUID.randomUUID()))
        val range = 0..10

        coEvery { resourceBundleSpecialtyService.getByFilters(filters.medicalSpecialtyIds, null) } returns listOf(resourceBundleSpecialty).success()
        coEvery { healthSpecialistResourceBundleService.findByFilters(null, range, listOf(resourceBundleSpecialty.healthSpecialistResourceBundleId)) } returns listOf(bundle).success()
        coEvery { healthSpecialistResourceBundleService.countByFilters(null, listOf(resourceBundleSpecialty.healthSpecialistResourceBundleId)) } returns 1.success()
        coEvery { resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(listOf(resourceBundleSpecialty.id)) } returns listOf(resourceBundleSpecialtyPricing).success()
        coEvery { medicalSpecialtyService.getByIds(any()) } returns listOf(medicalSpecialty).success()

        val result = service.getResourceBundleSpecialtyPricingList(filters, range)

        assertThat(result).isSuccessWithData(
            PricingForHealthSpecialistResourceBundleWithCountResponse(
                count = 1,
                items = listOf(
                    PricingForHealthSpecialistResourceBundleResponse(
                        healthSpecialistResourceBundleId = bundle.id,
                        primaryTuss = bundle.primaryTuss,
                        aliceCode = bundle.code,
                        description = bundle.description,
                        pendingNumber = 0,
                        serviceType = bundle.serviceType.description,
                        medicalSpecialties = listOf(
                            PricingForHealthSpecialistMedicalSpecialtyResponse(
                                medicalSpecialtyId = medicalSpecialty.id,
                                description = medicalSpecialty.name,
                                prices = listOf(
                                    ResourceBundleSpecialtyPrice(
                                        tier = resourceBundleSpecialtyPricing.prices.first().tier,
                                        price = resourceBundleSpecialtyPricing.prices.first().price,
                                        productTier = resourceBundleSpecialtyPricing.prices.first().productTier
                                    )
                                ),
                                pendingNumber = 9,
                                beginAt = resourceBundleSpecialtyPricing.beginAt,
                                changeBeginAt = null,
                                resourceBundleSpecialtyId = resourceBundleSpecialty.id
                            )
                        )
                    )
                )
            )
        )

        coVerifyOnce {
            healthSpecialistResourceBundleService.findByFilters(any(), any(), any())
        }
        coVerifyOnce {
            healthSpecialistResourceBundleService.countByFilters(any(), any())
        }

        coVerifyNone {
            resourceBundleSpecialtyService.getByResourceBundleIds(any(), any())
        }

        coVerifyOnce {
            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(any())
        }

        coVerifyOnce {
            medicalSpecialtyService.getByIds(any())
        }
    }

    @Test
    fun `#getResourceBundleSpecialtyPricingList should return results when status filter is passed`() = runBlocking<Unit> {
        val filters = PricingForHealthSpecialistRequestFilters(status = PricingStatus.NOT_PRICED)
        val range = 0..10

        coEvery { resourceBundleSpecialtyService.getByFilters(null, filters.status) } returns listOf(resourceBundleSpecialty).success()
        coEvery { healthSpecialistResourceBundleService.findByFilters(null, range, listOf(resourceBundleSpecialty.healthSpecialistResourceBundleId)) } returns listOf(bundle).success()
        coEvery { healthSpecialistResourceBundleService.countByFilters(null, listOf(resourceBundleSpecialty.healthSpecialistResourceBundleId)) } returns 1.success()
        coEvery { resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(listOf(resourceBundleSpecialty.id)) } returns listOf(resourceBundleSpecialtyPricing).success()
        coEvery { medicalSpecialtyService.getByIds(any()) } returns listOf(medicalSpecialty).success()

        val result = service.getResourceBundleSpecialtyPricingList(filters, range)

        assertThat(result).isSuccessWithData(
            PricingForHealthSpecialistResourceBundleWithCountResponse(
                count = 1,
                items = listOf(
                    PricingForHealthSpecialistResourceBundleResponse(
                        healthSpecialistResourceBundleId = bundle.id,
                        primaryTuss = bundle.primaryTuss,
                        aliceCode = bundle.code,
                        description = bundle.description,
                        pendingNumber = 0,
                        serviceType = bundle.serviceType.description,
                        medicalSpecialties = listOf(
                            PricingForHealthSpecialistMedicalSpecialtyResponse(
                                medicalSpecialtyId = medicalSpecialty.id,
                                description = medicalSpecialty.name,
                                prices = listOf(
                                    ResourceBundleSpecialtyPrice(
                                        tier = resourceBundleSpecialtyPricing.prices.first().tier,
                                        price = resourceBundleSpecialtyPricing.prices.first().price,
                                        productTier = resourceBundleSpecialtyPricing.prices.first().productTier
                                    )
                                ),
                                pendingNumber = 9,
                                beginAt = resourceBundleSpecialtyPricing.beginAt,
                                changeBeginAt = null,
                                resourceBundleSpecialtyId = resourceBundleSpecialty.id
                            )
                        )
                    )
                )
            )
        )

        coVerifyOnce {
            healthSpecialistResourceBundleService.findByFilters(any(), any(), any())
        }
        coVerifyOnce {
            healthSpecialistResourceBundleService.countByFilters(any(), any())
        }

        coVerifyNone {
            resourceBundleSpecialtyService.getByResourceBundleIds(any(), any())
        }

        coVerifyOnce {
            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(any())
        }

        coVerifyOnce {
            medicalSpecialtyService.getByIds(any())
        }
    }

    @Test
    fun `#getResourceBundleSpecialtyPricingList should return results when both status and medicalSpecialtyIds filters are passed`() = runBlocking<Unit> {
        val filters = PricingForHealthSpecialistRequestFilters(
            medicalSpecialtyIds = listOf(UUID.randomUUID()),
            status = PricingStatus.NOT_PRICED
        )
        val range = 0..10

        coEvery {
            resourceBundleSpecialtyService.getByFilters(
                filters.medicalSpecialtyIds,
                filters.status
            )
        } returns listOf(resourceBundleSpecialty).success()
        coEvery {
            healthSpecialistResourceBundleService.findByFilters(
                null,
                range,
                listOf(resourceBundleSpecialty.healthSpecialistResourceBundleId)
            )
        } returns listOf(bundle).success()
        coEvery {
            healthSpecialistResourceBundleService.countByFilters(
                null,
                listOf(resourceBundleSpecialty.healthSpecialistResourceBundleId)
            )
        } returns 1.success()
        coEvery {
            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(
                listOf(
                    resourceBundleSpecialty.id
                )
            )
        } returns listOf(resourceBundleSpecialtyPricing).success()
        coEvery { medicalSpecialtyService.getByIds(any()) } returns listOf(medicalSpecialty).success()

        val result = service.getResourceBundleSpecialtyPricingList(filters, range)

        assertThat(result).isSuccessWithData(
            PricingForHealthSpecialistResourceBundleWithCountResponse(
                count = 1,
                items = listOf(
                    PricingForHealthSpecialistResourceBundleResponse(
                        healthSpecialistResourceBundleId = bundle.id,
                        primaryTuss = bundle.primaryTuss,
                        aliceCode = bundle.code,
                        description = bundle.description,
                        pendingNumber = 0,
                        serviceType = bundle.serviceType.description,
                        medicalSpecialties = listOf(
                            PricingForHealthSpecialistMedicalSpecialtyResponse(
                                medicalSpecialtyId = medicalSpecialty.id,
                                description = medicalSpecialty.name,
                                prices = listOf(
                                    ResourceBundleSpecialtyPrice(
                                        tier = resourceBundleSpecialtyPricing.prices.first().tier,
                                        price = resourceBundleSpecialtyPricing.prices.first().price,
                                        productTier = resourceBundleSpecialtyPricing.prices.first().productTier
                                    )
                                ),
                                pendingNumber = 9,
                                beginAt = resourceBundleSpecialtyPricing.beginAt,
                                changeBeginAt = null,
                                resourceBundleSpecialtyId = resourceBundleSpecialty.id
                            )
                        )
                    )
                )
            )
        )

        coVerifyOnce {
            healthSpecialistResourceBundleService.findByFilters(any(), any(), any())
        }
        coVerifyOnce {
            healthSpecialistResourceBundleService.countByFilters(any(), any())
        }

        coVerifyNone {
            resourceBundleSpecialtyService.getByResourceBundleIds(any(), any())
        }

        coVerifyOnce {
            resourceBundleSpecialtyPricingService.getActiveByResourceBundleSpecialtyId(any())
        }

        coVerifyOnce {
            medicalSpecialtyService.getByIds(any())
        }
    }

    @Test
    fun `getBySpecialty should return health specialist resource bundle by specialty`() = runBlocking {
        val specialtyId = UUID.randomUUID()
        val query = "some-query"
        val range = 0..9

        val resourceBundleSpecialty = TestModelFactory.buildResourceBundleSpecialty(
            id = UUID.randomUUID(),
            specialtyId = specialtyId,
        )

        val filter = HealthSpecialistResourceBundleService.Filter(
            searchToken = query,
            ids = listOf(resourceBundleSpecialty.healthSpecialistResourceBundleId)
        )

        val healthSpecialistResourceBundle = TestModelFactory.buildHealthSpecialistResourceBundle(
            id = resourceBundleSpecialty.healthSpecialistResourceBundleId,
        )

        coEvery {
            resourceBundleSpecialtyService.findBySpecialtyId(specialtyId)
        } returns listOf(resourceBundleSpecialty).success()

        coEvery {
            healthSpecialistResourceBundleService.findBy(filter, range)
        } returns listOf(healthSpecialistResourceBundle).success()

        coEvery {
            healthSpecialistResourceBundleService.countBy(filter)
        } returns 1.success()

        val result = service.getBySpecialty(specialtyId, query, range)
        assertThat(result).isSuccessWithData(
            ResourceBundleSpecialtyAggregateCount(
                count = 1,
                response = listOf(
                    ResourceBundleSpecialtyAggregate(
                        id = resourceBundleSpecialty.id,
                        appointmentRecommendationLevel = resourceBundleSpecialty.appointmentRecommendationLevel,
                        status = resourceBundleSpecialty.status,
                        medicalSpecialtyId = resourceBundleSpecialty.medicalSpecialtyId,
                        primaryTuss = healthSpecialistResourceBundle.primaryTuss,
                        code = healthSpecialistResourceBundle.code,
                        description = healthSpecialistResourceBundle.description,
                        healthSpecialistResourceBundleStatus = healthSpecialistResourceBundle.status,
                        serviceType = HealthSpecialistResourceBundleServiceType.CONSULTATION
                    )
                )
            )
        )

        coVerifyOnce { resourceBundleSpecialtyService.findBySpecialtyId(any()) }
        coVerifyOnce { healthSpecialistResourceBundleService.findBy(any(), any()) }
        coVerifyOnce { healthSpecialistResourceBundleService.countBy(any()) }
    }

    @Test
    fun `getSuggestedBySpecialty should return suggested procedures`() = runBlocking {
        val specialtyId = UUID.randomUUID()
        val suggestedProcedure = TestModelFactory.buildResourceBundleSpecialty(
            specialtyId = specialtyId,
            appointmentRecommendationLevel = AppointmentRecommendationLevel.RECOMMENDED
        )
        val defaultProcedure = TestModelFactory.buildResourceBundleSpecialty(
            specialtyId = specialtyId,
            appointmentRecommendationLevel = AppointmentRecommendationLevel.DEFAULT
        )

        val defaultHealthSpecialistResourceBundle = TestModelFactory.buildHealthSpecialistResourceBundle(
            id = defaultProcedure.healthSpecialistResourceBundleId,
        )
        val suggestedHealthSpecialistResourceBundle = TestModelFactory.buildHealthSpecialistResourceBundle(
            id = suggestedProcedure.healthSpecialistResourceBundleId,
        )


        val appointmentLevel =
            listOf(AppointmentRecommendationLevel.DEFAULT, AppointmentRecommendationLevel.RECOMMENDED)

        coEvery {
            resourceBundleSpecialtyService.findBySpecialtyId(specialtyId, appointmentLevel)
        } returns listOf(suggestedProcedure, defaultProcedure).success()

        coEvery {
            healthSpecialistResourceBundleService.findBy(
                HealthSpecialistResourceBundleService.Filter(
                    ids = listOf(
                        suggestedProcedure.healthSpecialistResourceBundleId,
                        defaultProcedure.healthSpecialistResourceBundleId
                    )
                )
            )
        }  returns listOf(defaultHealthSpecialistResourceBundle,suggestedHealthSpecialistResourceBundle).success()

        val result = service.getSuggestedBySpecialty(specialtyId)

        assertThat(result).isSuccessWithData(
            ResourceSuggestedProcedure(
                suggestedProcedure = listOf(
                    ResourceBundleSpecialtyAggregate(
                        id = suggestedProcedure.id,
                        appointmentRecommendationLevel = suggestedProcedure.appointmentRecommendationLevel,
                        status = suggestedProcedure.status,
                        medicalSpecialtyId = suggestedProcedure.medicalSpecialtyId,
                        primaryTuss = suggestedHealthSpecialistResourceBundle.primaryTuss,
                        code = suggestedHealthSpecialistResourceBundle.code,
                        description = suggestedHealthSpecialistResourceBundle.description,
                        healthSpecialistResourceBundleStatus = suggestedHealthSpecialistResourceBundle.status,
                        serviceType = HealthSpecialistResourceBundleServiceType.CONSULTATION
                    )
                ),
                procedureDefault = ResourceBundleSpecialtyAggregate(
                    id = defaultProcedure.id,
                    appointmentRecommendationLevel = defaultProcedure.appointmentRecommendationLevel,
                    status = defaultProcedure.status,
                    medicalSpecialtyId = defaultProcedure.medicalSpecialtyId,
                    primaryTuss = defaultHealthSpecialistResourceBundle.primaryTuss,
                    code = defaultHealthSpecialistResourceBundle.code,
                    description = defaultHealthSpecialistResourceBundle.description,
                    healthSpecialistResourceBundleStatus = defaultHealthSpecialistResourceBundle.status,
                    serviceType = HealthSpecialistResourceBundleServiceType.CONSULTATION
                )
            )
        )

        coVerifyOnce { resourceBundleSpecialtyService.findBySpecialtyId(any(), any()) }
        coVerifyOnce { healthSpecialistResourceBundleService.findBy(any()) }
    }

    @Test
    fun `changeAppointmentLevelBond should return suggested procedures`() = runBlocking {
        val specialtyId = UUID.randomUUID()
        val procedureToRecommend = TestModelFactory.buildResourceBundleSpecialty(
            specialtyId = specialtyId,
            appointmentRecommendationLevel = AppointmentRecommendationLevel.NONE
        )
        val procedureToRemove = TestModelFactory.buildResourceBundleSpecialty(
            specialtyId = specialtyId,
            appointmentRecommendationLevel = AppointmentRecommendationLevel.RECOMMENDED
        )

        val defaultProcedure = TestModelFactory.buildResourceBundleSpecialty(
            specialtyId = specialtyId,
            appointmentRecommendationLevel = AppointmentRecommendationLevel.DEFAULT
        )

        val expectedToUpdate = listOf(
            procedureToRecommend.copy(
                appointmentRecommendationLevel = AppointmentRecommendationLevel.RECOMMENDED
            ),
            defaultProcedure,
            procedureToRemove.copy(
                appointmentRecommendationLevel = AppointmentRecommendationLevel.NONE
            )
        )

        val request = AppointmentRecommendationBondRequest(
            suggestedProcedure = listOf(procedureToRecommend.id),
            procedureDefault = defaultProcedure.id
        )

        coEvery { resourceBundleSpecialtyService.findBySpecialtyId(specialtyId) } returns listOf(
            procedureToRecommend,
            procedureToRemove,
            defaultProcedure
        ).success()

        coEvery { resourceBundleSpecialtyService.updateList(expectedToUpdate) } returns expectedToUpdate.success()

        val result = service.changeAppointmentLevelBond(specialtyId, request)
        assertThat(result).isSuccessWithData(expectedToUpdate)

        coVerifyOnce {
            resourceBundleSpecialtyService.findBySpecialtyId(
                specialtyId
            )
        }
        coVerifyOnce {
            resourceBundleSpecialtyService.updateList(
                expectedToUpdate
            )
        }
    }
}
