package br.com.alice.api.limbo.controllers

import br.com.alice.api.limbo.LimboConfiguration
import br.com.alice.api.limbo.RecurrentControllerTestHelper
import br.com.alice.api.limbo.models.ReprocessResponse
import br.com.alice.api.limbo.services.DeadletterQueueService
import br.com.alice.api.limbo.services.ReprocessService
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.data.layer.models.FeatureNamespace
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class RecurrentControllerTest : RecurrentControllerTestHelper() {

    private val deadletterQueueService: DeadletterQueueService = mockk()
    private val reprocessService: ReprocessService = mockk()

    private val recurrentController = RecurrentController(deadletterQueueService, reprocessService)

    @BeforeTest
    override fun setup() {
        super.setup()
        mockkObject(LimboConfiguration)
        module.single { recurrentController }
    }

    @Test
    fun `#reprocessWithNewStrategy - if feature says to report`() = runBlocking {
        coEvery { deadletterQueueService.reportMetrics() } returns mockk()
        internalAuthentication {
            postAsPlainText("/recurring_subscribers/report_metrics") { response ->
                ResponseAssert.assertThat(response).isOK()
            }
        }
        coVerify(exactly = 1) { deadletterQueueService.reportMetrics() }
    }

    @Test
    fun `#purgeOldDLQs`() {
        mockLocalDateTime { date ->
            coEvery { deadletterQueueService.purgeDLQs(date.minusMonths(3L)) } returns mockk()

            internalAuthentication {
                postAsPlainText("/recurring_subscribers/purge_old_dlqs") { response ->
                    ResponseAssert.assertThat(response).isOK()
                }
            }
            coVerify(exactly = 1) { deadletterQueueService.purgeDLQs(any()) }
        }
    }

    @Test
    fun `#reprocessAutoRetryable`() {
        val result = ReprocessResponse(1, 0, 0)
        coEvery { reprocessService.reprocessAutoRetryable(any()) } returns result.success()
        coEvery { LimboConfiguration.secretKey() } returns "BANANA".success()

        internalAuthentication {
            postAsPlainText("/recurring_subscribers/reprocess_auto_retryable") { response ->
                ResponseAssert.assertThat(response).isOKWithData(result)
            }
        }
        coVerify(exactly = 1) { reprocessService.reprocessAutoRetryable(any()) }
    }
}
