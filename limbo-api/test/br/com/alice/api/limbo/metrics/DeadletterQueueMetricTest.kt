package br.com.alice.api.limbo.metrics

import br.com.alice.api.limbo.models.DeadlettersCount
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.common.logging.logger
import io.mockk.mockkObject
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class DeadletterQueueMetricTest {

    @Test
    fun `#reportMetrics - call service and register batch metric`() = runBlocking<Unit> {
        mockkObject(logger) {
            val firstBatch = listOf(
                DeadlettersCount("topic1", "producer1", 2),
                DeadlettersCount("topic1", "producer2", 1),
                DeadlettersCount("topic2", "producer2", 1),
                DeadlettersCount("topic2", "producer3", 2),
                DeadlettersCount("topic3", "producer4", 2),
                DeadlettersCount("topic3", "producer5", 1),
            )
            DeadletterQueueMetric.registerBatchDeadletterQueueGauge(firstBatch)

            firstBatch.forEach {
                verifyOnce {
                    logger.info(
                        "Registering deadletter queue metric",
                        "topic" to it.topic.uppercase(),
                        "producer" to it.producer.lowercase(),
                        "name" to "deadletter_queue",
                        "value" to it.total.toString()
                    )
                }
            }

            val secondBatch = listOf(
                DeadlettersCount("topic1", "producer1", 5),
                DeadlettersCount("topic1", "producer2", 3),
                DeadlettersCount("topic2", "producer2", 15),
                DeadlettersCount("topic2", "producer3", 1),
            )
            DeadletterQueueMetric.registerBatchDeadletterQueueGauge(secondBatch)

            secondBatch.forEach {
                verifyOnce {
                    logger.info(
                        "Registering deadletter queue metric",
                        "topic" to it.topic.uppercase(),
                        "producer" to it.producer.lowercase(),
                        "name" to "deadletter_queue",
                        "value" to it.total.toString()
                    )
                }
            }
        }
    }

    @Test
    fun `#reportMetrics - call service and register metric`() = runBlocking<Unit> {
        mockkObject(logger) {
            DeadletterQueueMetric.registerDeadletterQueueGauge("_test1", 1)
            DeadletterQueueMetric.registerDeadletterQueueGauge("_test2", 10)
            DeadletterQueueMetric.registerDeadletterQueueGauge("_test1", 2)
            DeadletterQueueMetric.registerDeadletterQueueGauge("_test2", 5)

            verifyOnce {
                logger.info(
                    "Registering deadletter queue metric",
                    "name" to "deadletter_queue_test1",
                    "value" to "1"
                )
            }
            verifyOnce {
                logger.info(
                    "Registering deadletter queue metric",
                    "name" to "deadletter_queue_test2",
                    "value" to "10"
                )
            }
            verifyOnce {
                logger.info(
                    "Registering deadletter queue metric",
                    "name" to "deadletter_queue_test1",
                    "value" to "2"
                )
            }
            verifyOnce {
                logger.info(
                    "Registering deadletter queue metric",
                    "name" to "deadletter_queue_test2",
                    "value" to "5"
                )
            }
        }
    }
}
