package br.com.alice.api.limbo.metrics

import br.com.alice.api.limbo.models.DeadlettersCount
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.metrics.Metric
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger

private const val DEADLETTER_QUEUE_METRIC_NAME = "deadletter_queue"

object DeadletterQueueMetric {

    private val ungroupedCounts: ConcurrentHashMap<String, AtomicInteger> = ConcurrentHashMap()
    private val totalCounts: ConcurrentHashMap<Pair<String,String>, AtomicInteger> = ConcurrentHashMap()

    fun registerDeadletterQueueGauge(suffix: String, value: Int) {
        val metricName = DEADLETTER_QUEUE_METRIC_NAME + suffix
        ungroupedCounts[metricName] = AtomicInteger(value)
        registerGauge(
            name = metricName
        ) { ungroupedCounts[metricName]!! }
    }

    fun registerBatchDeadletterQueueGauge(counts: List<DeadlettersCount>) {
        totalCounts.clear()
        counts.map {
            totalCounts[Pair(it.topic, it.producer)] = AtomicInteger(it.total)
            registerGauge(
                name = DEADLETTER_QUEUE_METRIC_NAME,
                labels = mapOf("topic" to it.topic.uppercase(), "producer" to it.producer.lowercase())
            ) { totalCounts[Pair(it.topic, it.producer)]!! }
        }
    }

    private fun registerGauge(
        name: String,
        labels: Map<String, String>? = null,
        counter: () -> AtomicInteger
    ) {
        val args = labels?.toMutableMap() ?: mutableMapOf()
        args.put("name", name)
        args.put("value", counter().get().toString())
        logger.info("Registering deadletter queue metric", *(args.map { it.key to it.value }.toTypedArray()))
    }
}
