package br.com.alice.api.limbo.controllers

import br.com.alice.api.limbo.LimboConfiguration
import br.com.alice.api.limbo.services.DeadletterQueueService
import br.com.alice.api.limbo.services.ReprocessService
import br.com.alice.common.Response
import br.com.alice.common.asyncLayer
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.foldResponse
import br.com.alice.common.logging.logger
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.LIMBO_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.featureconfig.core.FeatureService
import java.time.LocalDateTime

class RecurrentController(
    private val deadletterQueueService: DeadletterQueueService,
    private val reprocessService: ReprocessService
) {

    suspend fun registerMetrics() = asyncLayer {
        withRootServicePolicy(LIMBO_API_ROOT_SERVICE_NAME) {
            withUnauthenticatedTokenWithKey(LIMBO_API_ROOT_SERVICE_NAME) {
                logger.info("Starting reporting metrics")
                deadletterQueueService.reportMetrics()
                    .then { logger.info("Metrics reported successfully") }
                    .thenError { logger.error("Error while reporting metrics", it) }
                Response.OK
            }
        }
    }

    suspend fun purgeOldDLQs() = asyncLayer {
        withRootServicePolicy(LIMBO_API_ROOT_SERVICE_NAME) {
            withUnauthenticatedTokenWithKey(LIMBO_API_ROOT_SERVICE_NAME) {
                deadletterQueueService.purgeDLQs(LocalDateTime.now().minusMonths(3L))
                Response.OK
            }
        }
    }

    suspend fun reprocessAutoRetryable() = asyncLayer {
        withRootServicePolicy(LIMBO_API_ROOT_SERVICE_NAME) {
            withUnauthenticatedTokenWithKey(LIMBO_API_ROOT_SERVICE_NAME) {
                reprocessService.reprocessAutoRetryable(LimboConfiguration.secretKey().get()).foldResponse()
            }
        }
    }

}
