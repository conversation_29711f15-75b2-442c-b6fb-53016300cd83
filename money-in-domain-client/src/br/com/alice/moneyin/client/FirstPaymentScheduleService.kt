package br.com.alice.moneyin.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyContract
import br.com.alice.data.layer.models.CompanySubContract
import br.com.alice.data.layer.models.FirstPaymentSchedule
import br.com.alice.data.layer.models.PreActivationPayment
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface FirstPaymentScheduleService : Service {
    override val namespace get() = "money_in"
    override val serviceName get() = "first_payment_schedule"

    suspend fun create(
        payload: CreatePayload,
    ): Result<FirstPaymentSchedule, Throwable>

    suspend fun get(id: UUID): Result<FirstPaymentSchedule, Throwable>

    suspend fun update(firstPaymentSchedule: FirstPaymentSchedule): Result<FirstPaymentSchedule, Throwable>

    suspend fun findByCompanyIdSubContractIdAndPreActivationPaymentId(
        companyId: UUID,
        companySubcontractId: UUID,
        preActivationPaymentId: UUID
    ): Result<FirstPaymentSchedule, Throwable>

    data class CreatePayload(
        val company: Company,
        val companyContract: CompanyContract,
        val companySubContract: CompanySubContract,
        val preActivationPayment: PreActivationPayment,
    )
}
