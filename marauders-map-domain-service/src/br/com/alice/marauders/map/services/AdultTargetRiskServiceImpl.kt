package br.com.alice.marauders.map.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.data.layer.models.Risk
import br.com.alice.data.layer.models.RiskDescription
import br.com.alice.marauders.map.client.AdultTargetRiskService
import br.com.alice.marauders.map.client.RiskService
import br.com.alice.marauders.map.services.internal.AdultAppointmentCoordinationTargetRiskService
import br.com.alice.marauders.map.services.internal.AdultHealthConditionTargetRiskService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success

class AdultTargetRiskServiceImpl(
    private val riskService: RiskService,
    private val healthConditionTargetRiskService: AdultHealthConditionTargetRiskService,
    private val appointmentCoordinationTargetRiskService: AdultAppointmentCoordinationTargetRiskService
): AdultTargetRiskService, Spannable {

    override suspend fun validateByPersonId(personId: PersonId): Result<Risk, Throwable> =
        riskService.getByPerson(personId).flatMap {
            validateTarget(it)
        }

    override suspend fun validate(risk: Risk): Result<Risk, Throwable> = span("validate") { span ->
        riskService.getByPerson(risk.personId).flatMap { actualRisk ->
            span.setAttribute("actual_risk", actualRisk.id.toString())
            span.setAttribute("actual_risk_description", actualRisk.riskDescription.toString())
            span.setAttribute("risk_parameter", risk.id.toString())
            span.setAttribute("risk_description", risk.riskDescription.toString())

            val itsTarget = shouldCheckIfItsTarget(risk, actualRisk)

            span.setAttribute("should_check_if_its_target", itsTarget.toString())

            if (!itsTarget) return@flatMap actualRisk.success().recordResult(span)

            validateTarget(risk)
                .recordResult(span)
        }
    }

    private suspend fun validateTarget(risk: Risk): Result<Risk, Throwable> =
        isTarget(risk).flatMap { (isTarget, referencedModels) ->
            if (isTarget && referencedModels.isNotEmpty()) {
                riskService.updateByEdit(personId = risk.personId, newRisk = RiskDescription.TARGET, referencedModels = referencedModels)
            } else risk.success()
        }

    private fun shouldCheckIfItsTarget(risk: Risk, actualRisk: Risk) =
        risk.riskDescription != RiskDescription.TARGET && actualRisk.addedAt <= risk.addedAt

    private suspend fun isTarget(risk: Risk): Result<Pair<Boolean, List<Risk.ReferencedModel>>, Throwable>  =
        healthConditionTargetRiskService.isTargetBasedOnCaseRecords(risk).flatMap { (isTarget, referencedModels) ->
            if (!isTarget && referencedModels.isEmpty()) {
                appointmentCoordinationTargetRiskService.isTargetBasedOnAppointmentCoordination(risk)
            } else {
                Pair(isTarget, referencedModels).success()
            }
        }


}
