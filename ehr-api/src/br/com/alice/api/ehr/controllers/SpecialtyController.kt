package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.converters.SpecialtyConverter
import br.com.alice.api.ehr.metrics.Metrics
import br.com.alice.api.ehr.model.SimpleSpecialtyResponse
import br.com.alice.api.ehr.services.specialists.Specialist
import br.com.alice.common.ErrorResponse
import br.com.alice.common.Response
import br.com.alice.common.convertTo
import br.com.alice.common.core.extensions.capitalize
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.redis.GenericCache
import br.com.alice.common.toResponse
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.MedicalSpecialtyType.SPECIALTY
import br.com.alice.data.layer.models.SpecialistType
import br.com.alice.ehr.client.AdvancedAccessService
import br.com.alice.provider.client.MedicalSpecialtyService
import io.ktor.http.HttpStatusCode
import java.util.UUID

class SpecialtyController(
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val advancedAccessService: AdvancedAccessService,
    private val cache: GenericCache,
) {
    private val generalist = "Generalista"

    suspend fun getAllSpecialties(): Response {
        val specialties = cache.getList(
            "specialties",
            MedicalSpecialty::class,
            callbackWithResult = Metrics::incrementCacheSpecialties
        ) {
            medicalSpecialtyService.getActivesByType(
                SPECIALTY,
                false
            ).get()
        }
        return specialties.map {
            it.convertTo(SimpleSpecialtyResponse::class)
        }.sortedBy { it.name }.toResponse()
    }

    suspend fun getSubSpecialistsBySpecialty(specialtyId: UUID): Response {
        val specialty = getSpecialty(specialtyId)

        validateSpecialty(specialty).takeIf { it != null }?.let {
            return Response(
                HttpStatusCode.NotFound,
                ErrorResponse(it)
            )
        }

        var subSpecialties = getSubSpecialties(specialtyId).map {
            it.convertTo(SimpleSpecialtyResponse::class)
        }

        getGeneralistSubSpecialty(specialty, subSpecialties)?.let {
            subSpecialties = subSpecialties.plus(it)
        }

        return subSpecialties
            .sortedBy { it.name }
            .toResponse()
    }

    suspend fun getAdvancedAccessSubSpecialties(personId: String, specialtyId: UUID): Response {
        val specialty = getSpecialty(specialtyId)

        validateSpecialty(specialty).takeIf { it != null }?.let {
            return Response(
                HttpStatusCode.NotFound,
                ErrorResponse(it)
            )
        }

        val advancedAccessData = advancedAccessService.getAdvancedAccessData(personId.toPersonId()).get()

        var subSpecialties = getSubSpecialties(specialtyId).map {
            SpecialtyConverter.convert(it, advancedAccessData)
        }

        getGeneralistSubSpecialty(specialty, subSpecialties)?.let {
            subSpecialties = subSpecialties.plus(it)
        }

        return subSpecialties
            .sortedBy { it.name }
            .toResponse()
    }

    private fun validateSpecialty(specialty: MedicalSpecialty) =
        if (!specialty.active) {
            "specialty_disabled"
        } else if (specialty.parentSpecialtyId != null) {
            "specialty_is_subSpecialty"
        } else {
            null
        }

    private suspend fun getSpecialty(specialtyId: UUID) = cache.get(
        specialtyId.toString(),
        MedicalSpecialty::class,
        callbackWithResult = Metrics::incrementCacheSpecialties
    ) {
        medicalSpecialtyService.getById(specialtyId).get()
    }

    private suspend fun getSubSpecialties(specialtyId: UUID) = cache.getList(
        "subs-$specialtyId",
        MedicalSpecialty::class,
        callbackWithResult = Metrics::incrementCacheSpecialties
    ) {
        medicalSpecialtyService.getByParentId(specialtyId).get()
            .filter { it.active }
    }

    private fun getGeneralistSubSpecialty(
        specialty: MedicalSpecialty,
        subSpecialties: List<SimpleSpecialtyResponse>
    ): SimpleSpecialtyResponse? {
        if (specialty.generateGeneralistSubSpecialty) {
            if (subSpecialties.none {
                    it.name.lowercase() == generalist.lowercase()
                }
            ) {
                return SimpleSpecialtyResponse(
                    name = generalist,
                    isTherapy = specialty.isTherapy
                )
            }
        }
        return null
    }

}

data class SpecialistResponse(
    val name: String? = "",
    val speciality: String,
    val tier: SpecialistTier?,
    val id: UUID,
    val type: SpecialistType,
) {
    companion object {
        fun fromSpecialist(specialist: Specialist) = SpecialistResponse(
            id = specialist.id,
            name = specialist.name.capitalize(),
            speciality = specialist.specialtyName,
            type = specialist.type,
            tier = specialist.tier,
        )
    }
}

