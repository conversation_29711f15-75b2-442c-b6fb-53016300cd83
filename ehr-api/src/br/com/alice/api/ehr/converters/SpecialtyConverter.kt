package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.model.AdvancedAccessResponse
import br.com.alice.api.ehr.model.SimpleSpecialtyResponse
import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.ehr.client.AdvancedAccessData

object SpecialtyConverter : Converter<MedicalSpecialty, SimpleSpecialtyResponse>(
    MedicalSpecialty::class, SimpleSpecialtyResponse::class
) {
    fun convert(source: MedicalSpecialty, advancedAccessData: AdvancedAccessData): SimpleSpecialtyResponse = convert(
        source,
        map(SimpleSpecialtyResponse::advancedAccess) from if (source.isAdvancedAccess) AdvancedAccessResponse(
            durationInDays = advancedAccessData.durationInDays,
            isPediatric = advancedAccessData.isPediatric
        ) else null,
    )
}

