package br.com.alice.api.ehr.controllers

import br.com.alice.api.ehr.ControllerTestHelper
import br.com.alice.api.ehr.model.AdvancedAccessResponse
import br.com.alice.api.ehr.model.SimpleSpecialtyResponse
import br.com.alice.common.ErrorResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.redis.GenericCache
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.MedicalSpecialtyType.SPECIALTY
import br.com.alice.ehr.client.AdvancedAccessData
import br.com.alice.ehr.client.AdvancedAccessService
import br.com.alice.provider.client.MedicalSpecialtyService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class SpecialtyControllerTest : ControllerTestHelper() {
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val advancedAccessService: AdvancedAccessService = mockk()
    private val cacheMock: GenericCache = mockk()

    private val specialtyOrtopedia = TestModelFactory.buildMedicalSpecialty(
        requireSpecialist = true,
        generateGeneralistSubSpecialty = false
    )

    private val specialtyNutricionista = TestModelFactory.buildMedicalSpecialty(
        name = "Nutricão",
        generateGeneralistSubSpecialty = true,
    )

    private val person = TestModelFactory.buildPerson()

    @BeforeTest
    override fun setup() {
        super.setup()
        this.module.single {
            SpecialtyController(
                medicalSpecialtyService,
                advancedAccessService,
                cacheMock
            )
        }
    }

    @Test
    fun `#getAllSpecialties return all specialties`() {
        coEvery {
            medicalSpecialtyService.getActivesByType(SPECIALTY, false)
        } returns listOf(specialtyOrtopedia, specialtyNutricionista).success()

        coEvery {
            cacheMock.getList("specialties", MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> List<MedicalSpecialty>>(4).invoke()
        }

        val expected = listOf(
            SimpleSpecialtyResponse(specialtyOrtopedia.id, specialtyOrtopedia.name,  specialtyOrtopedia.isTherapy, true),
            SimpleSpecialtyResponse(specialtyNutricionista.id, specialtyNutricionista.name,  specialtyNutricionista.isTherapy, false)
        ).sortedBy { it.name }

        authenticatedAs(idToken, staffTest) {
            get("/ehr/specialties/") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<SimpleSpecialtyResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(2)
                assertThat(content).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#getSubSpecialistsBySpecialty should return not found when specialty if not found`() {
        coEvery {
            medicalSpecialtyService.getById(specialtyOrtopedia.id)
        } returns NotFoundException("Not found").failure()

        coEvery {
            cacheMock.get(specialtyOrtopedia.id.toString(), MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> MedicalSpecialty>(4).invoke()
        }

        authenticatedAs(idToken, staffTest) {
            get("/ehr/specialties/${specialtyOrtopedia.id}/sub_specialties") { response ->
                val content: ErrorResponse = response.bodyAsJson()
                assertThat(content).isInstanceOf(ErrorResponse::class.java)
            }
        }
    }

    @Test
    fun `#getSubSpecialistsBySpecialty should return not found when specialty is disabled`() {
        val specialty = TestModelFactory.buildMedicalSpecialty(
            id = specialtyOrtopedia.id,
            type = SPECIALTY,
            name = specialtyOrtopedia.name, active = false
        )
        coEvery {
            medicalSpecialtyService.getById(specialty.id)
        } returns specialty.success()
        coEvery {
            cacheMock.get(specialty.id.toString(), MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> MedicalSpecialty>(4).invoke()
        }

        val expected = ErrorResponse("specialty_disabled")
        authenticatedAs(idToken, staffTest) {
            get("/ehr/specialties/${specialty.id}/sub_specialties") { response ->
                assertThat(response).isNotFound()
                val content: ErrorResponse = response.bodyAsJson()
                assertThat(content.code).isEqualTo(expected.code)
            }
        }
    }

    @Test
    fun `#getSubSpecialistsBySpecialty should return not found when specialty is a subSpecialty`() {
        val specialty = TestModelFactory.buildMedicalSpecialty(
            id = specialtyOrtopedia.id, type = SPECIALTY, parentSpecialtyId = RangeUUID.generate()
        )
        coEvery {
            cacheMock.get(specialty.id.toString(), MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> MedicalSpecialty>(4).invoke()
        }
        coEvery {
            medicalSpecialtyService.getById(specialty.id)
        } returns Result.of { specialty }

        val expected = ErrorResponse("specialty_is_subSpecialty")
        authenticatedAs(idToken, staffTest) {
            get("/ehr/specialties/${specialty.id}/sub_specialties") { response ->
                assertThat(response).isNotFound()
                val content: ErrorResponse = response.bodyAsJson()
                assertThat(content.code).isEqualTo(expected.code)
            }
        }
    }

    @Test
    fun `#getSubSpecialistsBySpecialty should return subSpecialties with generalist`() {
        val specialty = TestModelFactory.buildMedicalSpecialty(
            id = specialtyOrtopedia.id,
            type = SPECIALTY,
            requireSpecialist = false,
            generateGeneralistSubSpecialty = true
        )
        coEvery {
            medicalSpecialtyService.getById(specialty.id)
        } returns Result.of { specialty }

        coEvery {
            cacheMock.get(specialty.id.toString(), MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> MedicalSpecialty>(4).invoke()
            specialty
        }

        val sub01 = TestModelFactory.buildMedicalSpecialty(
            id = RangeUUID.generate(),
            name = "sub 01",
            parentSpecialtyId = specialty.id
        )
        val inactiveSubSpecialty = TestModelFactory.buildMedicalSpecialty(
            id = RangeUUID.generate(),
            name = "inactive sub specialty",
            parentSpecialtyId = specialty.id,
            active = false
        )

        coEvery {
            medicalSpecialtyService.getByParentId(specialty.id)
        } returns listOf(sub01, inactiveSubSpecialty).success()

        coEvery {
            cacheMock.getList("subs-${specialty.id}", MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> List<MedicalSpecialty>>(4).invoke()
        }

        authenticatedAs(idToken, staffTest) {
            get("/ehr/specialties/${specialty.id}/sub_specialties") { response ->
                assertThat(response).isOK()
                val content: List<SimpleSpecialtyResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(2)
                assertThat(content.find { it.name == "Generalista" }).isNotNull
            }
        }
    }

    @Test
    fun `#getSubSpecialistsBySpecialty should return subSpecialties when flags is false`() {
        val specialty = TestModelFactory.buildMedicalSpecialty(
            id = specialtyOrtopedia.id,
            type = SPECIALTY,
            requireSpecialist = false,
            generateGeneralistSubSpecialty = false
        )
        coEvery {
            medicalSpecialtyService.getById(specialty.id)
        } returns Result.of { specialty }

        coEvery {
            cacheMock.get(specialty.id.toString(), MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> MedicalSpecialty>(4).invoke()
            specialty
        }

        val sub01 = TestModelFactory.buildMedicalSpecialty(
            id = RangeUUID.generate(),
            name = "sub 01",
            parentSpecialtyId = specialty.id
        )
        val sub02 = TestModelFactory.buildMedicalSpecialty(
            id = RangeUUID.generate(),
            name = "Generalista",
            parentSpecialtyId = specialty.id
        )
        coEvery {
            medicalSpecialtyService.getByParentId(specialty.id)
        } returns listOf(sub01, sub02).success()

        coEvery {
            cacheMock.getList("subs-${specialty.id}", MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> List<MedicalSpecialty>>(4).invoke()
            listOf(sub01, sub02)
        }

        val expected = listOf(
            SimpleSpecialtyResponse(sub01.id, sub01.name, sub01.isTherapy, false),
            SimpleSpecialtyResponse(sub02.id, sub02.name, sub01.isTherapy, false)
        ).sortedBy { it.name }
        authenticatedAs(idToken, staffTest) {
            get("/ehr/specialties/${specialty.id}/sub_specialties") { response ->
                assertThat(response).isOK()
                val content: List<SimpleSpecialtyResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(2)
                assertThat(content).isEqualTo(expected)
            }
        }
    }
    @Test
    fun `#getAdvancedAccessSubSpecialties should return not found when specialty if not found`() {
        coEvery {
            medicalSpecialtyService.getById(specialtyOrtopedia.id)
        } returns NotFoundException("Not found").failure()

        coEvery {
            cacheMock.get(specialtyOrtopedia.id.toString(), MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> MedicalSpecialty>(4).invoke()
        }

        authenticatedAs(idToken, staffTest) {
            get("/ehr/persons/${person.id}/specialty/${specialtyOrtopedia.id}/sub_specialties/advanced_access") { response ->
                val content: ErrorResponse = response.bodyAsJson()
                assertThat(content).isInstanceOf(ErrorResponse::class.java)
            }
        }
    }

    @Test
    fun `#getAdvancedAccessSubSpecialties should return not found when specialty is disabled`() {
        val specialty = TestModelFactory.buildMedicalSpecialty(
            id = specialtyOrtopedia.id,
            type = SPECIALTY,
            name = specialtyOrtopedia.name, active = false
        )
        coEvery {
            medicalSpecialtyService.getById(specialty.id)
        } returns specialty.success()
        coEvery {
            cacheMock.get(specialty.id.toString(), MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> MedicalSpecialty>(4).invoke()
        }

        val expected = ErrorResponse("specialty_disabled")
        authenticatedAs(idToken, staffTest) {
            get("/ehr/persons/${person.id}/specialty/${specialty.id}/sub_specialties/advanced_access") { response ->
                assertThat(response).isNotFound()
                val content: ErrorResponse = response.bodyAsJson()
                assertThat(content.code).isEqualTo(expected.code)
            }
        }
    }

    @Test
    fun `#getAdvancedAccessSubSpecialties should return not found when specialty is a subSpecialty`() {
        val specialty = TestModelFactory.buildMedicalSpecialty(
            id = specialtyOrtopedia.id, type = SPECIALTY, parentSpecialtyId = RangeUUID.generate()
        )
        coEvery {
            cacheMock.get(specialty.id.toString(), MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> MedicalSpecialty>(4).invoke()
        }
        coEvery {
            medicalSpecialtyService.getById(specialty.id)
        } returns Result.of { specialty }

        val expected = ErrorResponse("specialty_is_subSpecialty")
        authenticatedAs(idToken, staffTest) {
            get("/ehr/persons/${person.id}/specialty/${specialty.id}/sub_specialties/advanced_access") { response ->
                assertThat(response).isNotFound()
                val content: ErrorResponse = response.bodyAsJson()
                assertThat(content.code).isEqualTo(expected.code)
            }
        }
    }

    @Test
    fun `#getAdvancedAccessSubSpecialties should return subSpecialties with generalist`() {
        val specialty = TestModelFactory.buildMedicalSpecialty(
            id = specialtyOrtopedia.id,
            type = SPECIALTY,
            requireSpecialist = false,
            generateGeneralistSubSpecialty = true
        )
        coEvery {
            medicalSpecialtyService.getById(specialty.id)
        } returns Result.of { specialty }

        coEvery {
            advancedAccessService.getAdvancedAccessData(person.id)
        } returns AdvancedAccessData(durationInDays = 3, isPediatric = false).success()

        coEvery {
            cacheMock.get(specialty.id.toString(), MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> MedicalSpecialty>(4).invoke()
            specialty
        }

        val sub01 = TestModelFactory.buildMedicalSpecialty(
            id = RangeUUID.generate(),
            name = "sub 01",
            parentSpecialtyId = specialty.id
        )
        val inactiveSubSpecialty = TestModelFactory.buildMedicalSpecialty(
            id = RangeUUID.generate(),
            name = "inactive sub specialty",
            parentSpecialtyId = specialty.id,
            active = false
        )

        coEvery {
            medicalSpecialtyService.getByParentId(specialty.id)
        } returns listOf(sub01, inactiveSubSpecialty).success()

        coEvery {
            cacheMock.getList("subs-${specialty.id}", MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> List<MedicalSpecialty>>(4).invoke()
        }

        authenticatedAs(idToken, staffTest) {
            get("/ehr/persons/${person.id}/specialty/${specialty.id}/sub_specialties/advanced_access") { response ->
                assertThat(response).isOK()
                val content: List<SimpleSpecialtyResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(2)
                assertThat(content.find { it.name == "Generalista" }).isNotNull
            }
        }
    }

    @Test
    fun `#getAdvancedAccessSubSpecialties should return subSpecialties when flags is false`() {
        val specialty = TestModelFactory.buildMedicalSpecialty(
            id = specialtyOrtopedia.id,
            type = SPECIALTY,
            requireSpecialist = false,
            generateGeneralistSubSpecialty = false
        )
        coEvery {
            medicalSpecialtyService.getById(specialty.id)
        } returns Result.of { specialty }

        coEvery {
            advancedAccessService.getAdvancedAccessData(person.id)
        } returns AdvancedAccessData(durationInDays = 3, isPediatric = false).success()

        coEvery {
            cacheMock.get(specialty.id.toString(), MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> MedicalSpecialty>(4).invoke()
            specialty
        }

        val sub01 = TestModelFactory.buildMedicalSpecialty(
            id = RangeUUID.generate(),
            name = "sub 01",
            parentSpecialtyId = specialty.id
        )
        val sub02 = TestModelFactory.buildMedicalSpecialty(
            id = RangeUUID.generate(),
            name = "Generalista",
            parentSpecialtyId = specialty.id
        )
        coEvery {
            medicalSpecialtyService.getByParentId(specialty.id)
        } returns listOf(sub01, sub02).success()

        coEvery {
            cacheMock.getList("subs-${specialty.id}", MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> List<MedicalSpecialty>>(4).invoke()
            listOf(sub01, sub02)
        }

        val expected = listOf(
            SimpleSpecialtyResponse(sub01.id, sub01.name, sub01.isTherapy, false),
            SimpleSpecialtyResponse(sub02.id, sub02.name, sub01.isTherapy, false)
        ).sortedBy { it.name }
        authenticatedAs(idToken, staffTest) {
            get("/ehr/persons/${person.id}/specialty/${specialty.id}/sub_specialties/advanced_access") { response ->
                assertThat(response).isOK()
                val content: List<SimpleSpecialtyResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(2)
                assertThat(content).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#getAdvancedAccessSubSpecialties should return subSpecialties with advancedAccess filled`() {
        val specialty = TestModelFactory.buildMedicalSpecialty(
            id = specialtyOrtopedia.id,
            type = SPECIALTY,
            requireSpecialist = false,
            generateGeneralistSubSpecialty = true
        )
        coEvery {
            medicalSpecialtyService.getById(specialty.id)
        } returns Result.of { specialty }

        coEvery {
            advancedAccessService.getAdvancedAccessData(person.id)
        } returns AdvancedAccessData(durationInDays = 3, isPediatric = false).success()

        coEvery {
            cacheMock.get(specialty.id.toString(), MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> MedicalSpecialty>(4).invoke()
            specialty
        }

        val sub01 = TestModelFactory.buildMedicalSpecialty(
            id = RangeUUID.generate(),
            name = "sub 01",
            parentSpecialtyId = specialty.id,
            isAdvancedAccess = true
        )
        val sub02 = TestModelFactory.buildMedicalSpecialty(
            id = RangeUUID.generate(),
            name = "sub 02",
            parentSpecialtyId = specialty.id,
        )

        coEvery {
            medicalSpecialtyService.getByParentId(specialty.id)
        } returns listOf(sub01, sub02).success()

        coEvery {
            cacheMock.getList("subs-${specialty.id}", MedicalSpecialty::class, any(), any(), any())
        } coAnswers {
            arg<suspend () -> List<MedicalSpecialty>>(4).invoke()
        }

        val expected = listOf(
            SimpleSpecialtyResponse(name = "Generalista", isTherapy = false),
            SimpleSpecialtyResponse(sub01.id, sub01.name, sub01.isTherapy, false, advancedAccess = AdvancedAccessResponse(durationInDays = 3, isPediatric = false)),
            SimpleSpecialtyResponse(sub02.id, sub02.name, sub01.isTherapy, false)
        ).sortedBy { it.name }

        authenticatedAs(idToken, staffTest) {
            get("/ehr/persons/${person.id}/specialty/${specialty.id}/sub_specialties/advanced_access") { response ->
                assertThat(response).isOK()
                val content: List<SimpleSpecialtyResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(3)
                assertThat(content).isEqualTo(expected)
            }
        }
    }
}
