package br.com.alice.api.ehr.converters

import br.com.alice.api.ehr.model.AdvancedAccessResponse
import br.com.alice.api.ehr.model.SimpleSpecialtyResponse
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MedicalSpecialtyType
import br.com.alice.ehr.client.AdvancedAccessData
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class SpecialtyConverterTest {

    val medicalSpecialty = TestModelFactory.buildMedicalSpecialty(type = MedicalSpecialtyType.SUBSPECIALTY)

    @Test
    fun `#convert converts MedicalSpecialty into SimpleSpecialtyResponse without advanced access`() {
        val result = SpecialtyConverter.convert(medicalSpecialty, AdvancedAccessData(durationInDays = 3, isPediatric = false))

        val expect = SimpleSpecialtyResponse(
            id =  medicalSpecialty.id,
            name = medicalSpecialty.name,
            isTherapy = medicalSpecialty.isTherapy,
            requireSpecialist = medicalSpecialty.requireSpecialist,
            advancedAccess = null
        )
        assertThat(result).isEqualTo(expect)
    }

    @Test
    fun `#convert converts MedicalSpecialty into SimpleSpecialtyResponse with advanced access`() {
        val medicalSpecialtyWithAdvancedAccess = medicalSpecialty.copy(isAdvancedAccess = true)

        val result = SpecialtyConverter.convert(medicalSpecialtyWithAdvancedAccess, AdvancedAccessData(durationInDays = 3, isPediatric = false))

        val expect = SimpleSpecialtyResponse(
            id =  medicalSpecialty.id,
            name = medicalSpecialty.name,
            isTherapy = medicalSpecialty.isTherapy,
            requireSpecialist = medicalSpecialty.requireSpecialist,
            advancedAccess = AdvancedAccessResponse(
                durationInDays = 3,
                isPediatric = false
            )
        )
        assertThat(result).isEqualTo(expect)
    }
}
