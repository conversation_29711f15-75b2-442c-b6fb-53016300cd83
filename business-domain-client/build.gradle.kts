plugins {
    kotlin
    `kotlin-kapt`
}

kapt {
    correctErrorTypes = true
    generateStubs = false
    includeCompileClasspath = false
    useBuildCache = true
}

group = "br.com.alice.business-domain-client"
version = aliceBusinessDomainClientVersion

sourceSets {
    main {
        kotlin.sourceDirs = files("src")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

val api by configurations

dependencies {
    implementation(project(":common"))
    implementation(project(":communication"))
    implementation(project(":data-layer-client"))
    implementation(project(":data-packages:product-domain-service-data-package"))
    implementation(project(":data-packages:person-domain-service-data-package"))
    implementation(project(":data-packages:business-domain-service-data-package"))
    implementation(project(":data-packages:sales-channel-domain-service-data-package"))
    implementation(project(":data-packages:membership-domain-service-data-package"))
    implementation(project(":data-packages:money-in-domain-service-data-package"))

    kapt(project(":common"))
    ktor2Dependencies()


    testImplementation(project(":common-tests"))
    testImplementation(project(":data-layer-common-tests"))

    test2Dependencies()
}
