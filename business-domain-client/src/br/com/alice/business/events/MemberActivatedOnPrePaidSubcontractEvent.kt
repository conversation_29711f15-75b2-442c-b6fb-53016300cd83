package br.com.alice.business.events

import br.com.alice.business.SERVICE_NAME
import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.Company
import br.com.alice.data.layer.models.CompanyContract
import br.com.alice.data.layer.models.CompanySubContract

data class MemberActivatedOnPrePaidSubcontractEvent(
    val companySubcontract: CompanySubContract,
    val companyContract: CompanyContract,
    val company: Company,
) :
    NotificationEvent<MemberActivatedOnPrePaidSubcontractEvent.Payload>(
        producer = SERVICE_NAME,
        name = name,
        payload = Payload(companySubcontract, companyContract, company)
    ) {
    companion object {
        const val name = "member-activated-on-pre-paid-sub-contract"
    }

    data class Payload(
        val companySubcontract: CompanySubContract,
        val companyContract: CompanyContract,
        val company: Company
    )
}
