package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class FirstPaymentSchedule(
    val id: UUID = RangeUUID.generate(),
    val preActivationPaymentId: UUID,
    val companyId: UUID,
    val companySubcontractId: UUID,
    val memberInvoiceGroupId: UUID? = null,
    val statusHistory: List<FirstPaymentScheduleStatusHistoryEntry> = listOf(
        FirstPaymentScheduleStatusHistoryEntry(
            FirstPaymentScheduleStatus.PENDING,
            LocalDateTime.now().toString()
        )
    ),
    val status: FirstPaymentScheduleStatus = FirstPaymentScheduleStatus.PENDING,
    val error: String? = null,
    val scheduledDate: LocalDate,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) {
    private fun appendStatus(
        status: FirstPaymentScheduleStatus,
        date: LocalDateTime = LocalDateTime.now()
    ) = copy(
        status = status,
        statusHistory = statusHistory.plus(
            FirstPaymentScheduleStatusHistoryEntry(status, date.toString())
        )
    )
    
    fun markAsFinished(): FirstPaymentSchedule {
        if (status == FirstPaymentScheduleStatus.FINISHED) return this

        return appendStatus(
            FirstPaymentScheduleStatus.FINISHED,
            LocalDateTime.now()
        )
    }

    fun markAsFailure(error: String): FirstPaymentSchedule {
        if (status == FirstPaymentScheduleStatus.FAILURE) return this

        return appendStatus(
            FirstPaymentScheduleStatus.FAILURE,
            LocalDateTime.now()
        ).copy(error = error)
    }

    fun markAsCanceled(): FirstPaymentSchedule {
        if (status == FirstPaymentScheduleStatus.CANCELED) return this

        return appendStatus(
            FirstPaymentScheduleStatus.CANCELED,
            LocalDateTime.now()
        )
    }
}

data class FirstPaymentScheduleStatusHistoryEntry(
    val status: FirstPaymentScheduleStatus,
    val createdAt: String
) : JsonSerializable

enum class FirstPaymentScheduleStatus {
    PENDING,
    FINISHED,
    FAILURE,
    CANCELED
}
