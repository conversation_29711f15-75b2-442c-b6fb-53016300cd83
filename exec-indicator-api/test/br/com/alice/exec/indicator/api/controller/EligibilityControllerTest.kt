package br.com.alice.exec.indicator.api.controller

import br.com.alice.authentication.Authenticator
import br.com.alice.common.MvUtil
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.storage.AliceFile
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.subjects.EmailDomain
import br.com.alice.exec.indicator.api.models.EligibilityResponse
import br.com.alice.exec.indicator.models.EitaUserType
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.secondary.attention.client.EligibilityService
import br.com.alice.secondary.attention.client.SpecialistEligibility
import br.com.alice.secondary.attention.models.EligibilityCheckedExternallyResponse
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.spyk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

@Suppress("UNCHECKED_CAST")
class EligibilityControllerTest : ControllerTestHelper() {

    private val eligibilityService: EligibilityService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()

    private val controller = spyk(
        EligibilityController(
            eligibilityService,
            healthProfessionalService,
            providerUnitService,
        )
    )

    @BeforeTest
    override fun setup() {
        super.setup()

        module.single { controller }
    }

    @AfterTest
    fun afterTest() {
        clearAllMocks()
    }

    private val person = TestModelFactory.buildPerson(
        firstName = "Sebastião",
        lastName = "Maia",
        nickName = "Tim",
        dateOfBirth = LocalDateTime.now(),
        profilePicture = AliceFile(
            id = RangeUUID.generate(),
            fileName = "Tim_Maia_(1972).tif",
            url = "https://pt.wikipedia.org/wiki/Tim_Maia#/media/Ficheiro:Tim_Maia_(1972).tif",
            type = "tif"
        )
    )

    private val specialty = TestModelFactory.buildMedicalSpecialty()
    private val specialist = TestModelFactory.buildHealthProfessional(specialtyId = specialty.id)

    private val specialistEligibility = SpecialistEligibility(
        id = specialist.id,
        name = specialist.name,
        tier = specialist.tier!!,
        specialtyId = specialist.specialtyId!!,
        specialtyName = specialty.name,
        profileImage = specialist.imageUrl,
        isEligible = true,
        isTherapy = specialty.isTherapy,
        staffId = specialist.staffId
    )

    private val providerUnit = TestModelFactory.buildProviderUnit()

    @Test
    fun `#getPersonSpecialistEligibility - return not found when could not find specialist by email`() {
        coEvery {
            healthProfessionalService.findByEmail(any())
        } returns Exception().failure()

        authenticatedAs(token, staff) {
            get("/eligibility/specialist/${person.id}") { response ->
                ResponseAssert.assertThat(response).isNotFound()
            }
        }

        coVerify(exactly = 0) { eligibilityService.getPersonEligibilityBySpecialist(any(), any(), any(), any()) }
    }

    @Test
    fun `#getPersonSpecialistEligibility - return PersonSpecialistEligibilityResponse correctly if role is HEALTH_SPECIALIST`() {
        val customClaims: Map<String, Any> = mapOf(
            Authenticator.USER_TYPE_KEY to EmailDomain::class.simpleName,
            Authenticator.USER_ID_KEY to "123",
            Authenticator.ROLES_KEY to EitaUserType.HEALTH_SPECIALIST.name,
        ) as Map<String, Any>

        coEvery { controller.currentClaims() } returns customClaims
        coEvery {
            eligibilityService.getPersonEligibilityBySpecialist(any(), any(), any(), any())
        } returns listOf(specialistEligibility).success()

        coEvery {
            healthProfessionalService.findByEmail(any())
        } returns specialist.success()

        authenticatedAs(token, staff) {
            get("/eligibility/specialist/${person.id}/provider-unit/${providerUnit.id}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val responseObj: List<SpecialistEligibility> = response.bodyAsJson()

                Assertions.assertThat(responseObj.first().id).isEqualTo(specialist.id)
                Assertions.assertThat(responseObj.first().specialtyId).isEqualTo(specialty.id)

            }
        }

        coVerify(exactly = 1) { eligibilityService.getPersonEligibilityBySpecialist(any(), any(), any(), any()) }
    }

    @Test
    fun `#getPersonSpecialistEligibility - return PersonSpecialistEligibilityResponse correctly if role is HEALTH_ADMINISTRATIVE`() {
        val customClaims: Map<String, Any> = mapOf(
            Authenticator.USER_TYPE_KEY to EmailDomain::class.simpleName,
            Authenticator.USER_ID_KEY to "123",
            Authenticator.ROLES_KEY to EitaUserType.HEALTH_ADMINISTRATIVE.name,
        ) as Map<String, Any>

        coEvery { controller.currentClaims() } returns customClaims
        coEvery {
            providerUnitService.get(providerUnit.id)
        } returns providerUnit.success()
        coEvery {
            healthProfessionalService.getByStaffIds(any())
        } returns listOf(specialist).success()
        coEvery {
            eligibilityService.getPersonEligibilityBySpecialist(any(), any(), any(), any())
        } returns listOf(specialistEligibility).success()

        authenticatedAs(token, staff) {
            get("/eligibility/specialist/${person.id}/provider-unit/${providerUnit.id}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val responseObj: List<SpecialistEligibility> = response.bodyAsJson()

                Assertions.assertThat(responseObj.first().id).isEqualTo(specialist.id)
                Assertions.assertThat(responseObj.first().specialtyId).isEqualTo(specialty.id)

            }
        }

        coVerify(exactly = 1) { eligibilityService.getPersonEligibilityBySpecialist(any(), any(), any(), any()) }
    }

    @Test
    fun `#getPersonSpecialistEligibility - return PersonSpecialistEligibilityResponse correctly if role is HEALTH_ADMINISTRATIVE_ELIGIBILITY`() {
        val customClaims: Map<String, Any> = mapOf(
            Authenticator.USER_TYPE_KEY to EmailDomain::class.simpleName,
            Authenticator.USER_ID_KEY to "123",
            Authenticator.ROLES_KEY to EitaUserType.HEALTH_ADMINISTRATIVE_ELIGIBILITY.name,
        ) as Map<String, Any>

        coEvery { controller.currentClaims() } returns customClaims
        coEvery {
            providerUnitService.get(providerUnit.id)
        } returns providerUnit.success()
        coEvery {
            healthProfessionalService.getByStaffIds(any())
        } returns listOf(specialist).success()
        coEvery {
            eligibilityService.getPersonEligibilityBySpecialist(any(), any(), any(), any())
        } returns listOf(specialistEligibility).success()

        authenticatedAs(token, staff) {
            get("/eligibility/specialist/${person.id}/provider-unit/${providerUnit.id}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val responseObj: List<SpecialistEligibility> = response.bodyAsJson()

                Assertions.assertThat(responseObj.first().id).isEqualTo(specialist.id)
                Assertions.assertThat(responseObj.first().specialtyId).isEqualTo(specialty.id)

            }
        }

        coVerify(exactly = 1) { eligibilityService.getPersonEligibilityBySpecialist(any(), any(), any(), any()) }
    }

    @Test
    fun `#getPersonSpecialistEligibility - return PersonSpecialistEligibilityResponse error if does not have roles`() {
        val customClaims: Map<String, Any> = mapOf(
            Authenticator.USER_TYPE_KEY to EmailDomain::class.simpleName,
            Authenticator.USER_ID_KEY to "123",
            Authenticator.ROLES_KEY to emptyList<String>(),
        ) as Map<String, Any>

        coEvery { controller.currentClaims() } returns customClaims
        coEvery {
            providerUnitService.getByIds(any())
        } returns listOf(providerUnit).success()
        coEvery {
            healthProfessionalService.getByStaffIds(any())
        } returns listOf(specialist).success()
        coEvery {
            eligibilityService.getPersonEligibilityBySpecialist(any(), any(), any(), any())
        } returns listOf(specialistEligibility).success()

        authenticatedAs(token, staff) {
            get("/eligibility/specialist/${person.id}/provider-unit/${providerUnit.id}") { response ->
                ResponseAssert.assertThat(response).isNotFound()
            }
        }

        coVerify(exactly = 0) { eligibilityService.getPersonEligibilityBySpecialist(any(), any(), any(), any()) }
    }

    @Test
    fun `#checkEligibilityExternally - should call eligibilityService correctly`() = runBlocking {
        val checkResponse = EligibilityCheckedExternallyResponse(
            eligible = true,
            message = null
        )
        coEvery {
            eligibilityService.checkEligibilityExternally(person.id, providerUnit.id, any())
        } returns checkResponse.success()

        authenticatedAs(token, staff) {
            get("/eligibility/check-externally/${person.id}/provider-unit/${providerUnit.id}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val responseObj: EligibilityCheckedExternallyResponse = response.bodyAsJson()

                Assertions.assertThat(responseObj.eligible).isEqualTo(checkResponse.eligible)
                Assertions.assertThat(responseObj.message).isEqualTo(checkResponse.message)
            }
        }

        coVerifyOnce { eligibilityService.checkEligibilityExternally(any(), any(), any()) }
    }

    @Test
    fun `#checkEligibilityExternally - should return true if external communication fails`() = runBlocking {
        coEvery {
            eligibilityService.checkEligibilityExternally(person.id, providerUnit.id, any())
        } returns Exception().failure()

        authenticatedAs(token, staff) {
            get("/eligibility/check-externally/${person.id}/provider-unit/${providerUnit.id}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val responseObj: EligibilityCheckedExternallyResponse = response.bodyAsJson()

                Assertions.assertThat(responseObj.eligible).isEqualTo(true)
                Assertions.assertThat(responseObj.message).isEqualTo(null)
            }
        }

        coVerifyOnce { eligibilityService.checkEligibilityExternally(any(), any(), any()) }
    }

    @Test
    fun `#checkEligibilityByAttendanceTypeExternally - should call eligibilityService correctly`() = runBlocking {
        val expectedResponse = EligibilityResponse(
            eligibilityByAttendanceType = mapOf(
                MvUtil.TISS.PS to true,
                MvUtil.TISS.EXAM to true
            ),
            isEligibleAtProviderUnit = true,
        )

        coEvery {
            eligibilityService.checkEligibilityExternally(person.id, providerUnit.id,"<EMAIL>")
        } returns EligibilityCheckedExternallyResponse(
            eligible = true,
            message = null
        ).success()

        authenticatedAs(token, staff) {
            get("/eligibility/check-by-attendance-type/${person.id}/provider-unit/${providerUnit.id}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val responseObj: EligibilityResponse = response.bodyAsJson()

                Assertions.assertThat(responseObj.eligibilityByAttendanceType).isEqualTo(expectedResponse.eligibilityByAttendanceType)
                Assertions.assertThat(responseObj.isEligibleAtProviderUnit).isEqualTo(expectedResponse.isEligibleAtProviderUnit)
            }
        }
    }

    @Test
    fun `#checkEligibilityByAttendanceTypeExternally - should return true if external communication fails`() = runBlocking {
        val expectedResponse = EligibilityResponse(
            eligibilityByAttendanceType = mapOf(
                MvUtil.TISS.PS to true,
                MvUtil.TISS.EXAM to true
            ),
            isEligibleAtProviderUnit = true
        )

        coEvery {
            eligibilityService.checkEligibilityExternally(person.id, providerUnit.id,"<EMAIL>")
        } returns Exception().failure()

        authenticatedAs(token, staff) {
            get("/eligibility/check-by-attendance-type/${person.id}/provider-unit/${providerUnit.id}") { response ->
                ResponseAssert.assertThat(response).isSuccessfulJson()
                val responseObj: EligibilityResponse = response.bodyAsJson()

                Assertions.assertThat(responseObj.eligibilityByAttendanceType).isEqualTo(expectedResponse.eligibilityByAttendanceType)
                Assertions.assertThat(responseObj.isEligibleAtProviderUnit).isEqualTo(expectedResponse.isEligibleAtProviderUnit)
            }
        }
    }
}

