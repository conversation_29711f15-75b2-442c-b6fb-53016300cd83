package br.com.alice.ehr.services

import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.ehr.client.AdvancedAccessData
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import java.time.LocalDateTime
import kotlin.test.Test

class AdvancedAccessServiceImplTest {
    private val personService: PersonService = mockk()

    private val service = AdvancedAccessServiceImpl(personService)

    private val person = TestModelFactory.buildPerson()

    private val now = LocalDateTime.of(1990,2,20, 0, 0)

    @Test
    fun `#getAdvancedAccessDurationByPersonId - returns the task duration based on person age for adult`() = mockLocalDateTime(now) {
        val adultPerson = person.copy(dateOfBirth = LocalDateTime.of(1920,2,20,0,0))
        coEvery { personService.get(adultPerson.id) } returns adultPerson.success()

        val result = service.getAdvancedAccessDurationByPersonId(adultPerson.id)

        assertThat(result).isSuccessWithData(3)

        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getAdvancedAccessDurationByPersonId - returns the task duration based on person age for child`() = mockLocalDateTime(now) {
        val childPerson = person.copy(dateOfBirth = LocalDateTime.of(1985,2,20,0,0))
        coEvery { personService.get(childPerson.id) } returns childPerson.success()

        val result = service.getAdvancedAccessDurationByPersonId(childPerson.id)

        assertThat(result).isSuccessWithData(2)

        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getAdvancedAccessData - returns the task duration and if is pediatric based on person age for adult`() = mockLocalDateTime(now) {
        val adultPerson = person.copy(dateOfBirth = LocalDateTime.of(1920,2,20,0,0))
        coEvery { personService.get(adultPerson.id) } returns adultPerson.success()

        val result = service.getAdvancedAccessData(adultPerson.id)

        assertThat(result).isSuccessWithData(AdvancedAccessData(3, false))

        coVerifyOnce { personService.get(any()) }
    }

    @Test
    fun `#getAdvancedAccessData - returns the task duration and if is pediatric based on person age for child`() = mockLocalDateTime(now) {
        val childPerson = person.copy(dateOfBirth = LocalDateTime.of(1985,2,20,0,0))
        coEvery { personService.get(childPerson.id) } returns childPerson.success()

        val result = service.getAdvancedAccessData(childPerson.id)

        assertThat(result).isSuccessWithData(AdvancedAccessData(2, true))

        coVerifyOnce { personService.get(any()) }
    }
}
