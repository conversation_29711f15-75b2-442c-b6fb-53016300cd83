package br.com.alice.ehr.consumers

import br.com.alice.common.MvUtil
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CounterReferralTypeOfService
import br.com.alice.data.layer.models.Origin
import br.com.alice.data.layer.models.OriginStatus
import br.com.alice.data.layer.models.TertiaryIntentionLastSeen
import br.com.alice.data.layer.models.TertiaryIntentionNotification
import br.com.alice.data.layer.models.TertiaryIntentionType
import br.com.alice.data.layer.models.TotvsGuiaStatus
import br.com.alice.ehr.client.TertiaryIntentionTouchPointService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.events.GuiaWithProceduresUpsertedEvent
import br.com.alice.exec.indicator.events.GuiaWithProceduresUpsertedEventPayload
import br.com.alice.secondary.attention.events.CounterReferralCreatedEvent
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest

class TertiaryIntentionNotificationConsumerTest : ConsumerTest() {
    private val tertiaryIntentionService: TertiaryIntentionTouchPointService = mockk()
    private val totvsGuiaService: TotvsGuiaService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()

    private val consumer = TertiaryIntentionNotificationConsumer(
        tertiaryIntentionService,
        totvsGuiaService,
        healthProfessionalService
    )

    private val guiaId = RangeUUID.generate()
    private val personId = PersonId()
    private val mockDateTime = LocalDateTime.of(2021, 4, 1, 0, 0, 0)
    private val mockDate = LocalDate.of(2021, 4, 1)
    private val guiaEvent = GuiaWithProceduresUpsertedEvent(GuiaWithProceduresUpsertedEventPayload(guiaId))
    private val counterReferral =
        TestModelFactory.buildCounterReferral(
            personId = personId,
            appointmentDate = mockDateTime.toLocalDate(),
            typeOfService = CounterReferralTypeOfService.SURGICAL_PROCEDURE
        )
    private val counterReferalEvent = CounterReferralCreatedEvent(counterReferral)
    private val types = listOf(
        TertiaryIntentionType.TIT_HOSPITALIZATION,
        TertiaryIntentionType.TIT_SURGERY
    )

    @BeforeTest
    fun setup() {
        mockkStatic(LocalDateTime::class)
        mockkStatic(mockDate::class)
        every { LocalDateTime.now() } returns mockDateTime
        every { LocalDate.now() } returns mockDate
    }

    @AfterTest
    fun after() {
        unmockkStatic(LocalDateTime::class)
        unmockkStatic(LocalDate::class)
        clearAllMocks()
    }

    @Test
    fun `generateNotificationByGuia should register notification`() = runBlocking {
        val hospitalization = TestModelFactory.buildTertiaryIntentionHospitalization(personId = personId)
        val guia = TestModelFactory.buildTotvsGuia(
            id = guiaId,
            personId = personId,
            requestedAt = mockDateTime.toLocalDate(),
            status = TotvsGuiaStatus.PENDING,
            type = MvUtil.TISS.HOSPITALIZATION
        )
        val title = "guia ${guia.externalCode} atualizada para Pendente"
        coEvery {
            tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(
                personId = personId,
                types = types,
                dateInit = match { it == mockDateTime.minusDays(100) },
                dateLimit = match { it == mockDateTime.plusDays(100) }
            )
        } returns listOf(hospitalization).success()
        coEvery { totvsGuiaService.get(guiaId) } returns guia.success()
        coEvery {
            tertiaryIntentionService.update(
                hospitalization.copy(
                    notifications = listOf(
                        TertiaryIntentionNotification(
                            id = guiaId,
                            origin = Origin.TOTVS_GUIA,
                            status = OriginStatus.PENDING,
                            title = title,
                            createdAt = mockDateTime
                        )
                    )
                )
            )
        } returns hospitalization.success()

        val result = consumer.generateNotificationByGuia(guiaEvent)
        assertThat(result).isSuccess()

        coVerifyOnce { tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(any(), any(), any(), any()) }
        coVerifyOnce { totvsGuiaService.get(any()) }
        coVerifyOnce { tertiaryIntentionService.update(any()) }
    }


    @Test
    fun `generateNotificationByGuia should updated notification when guia status is new `() = runBlocking {
        val guia = TestModelFactory.buildTotvsGuia(
            id = guiaId,
            personId = personId,
            requestedAt = mockDateTime.toLocalDate(),
            status = TotvsGuiaStatus.AUTHORIZED,
            type = MvUtil.TISS.HOSPITALIZATION
        )
        val title = "guia ${guia.externalCode} atualizada para Pendente"

        val hospitalization = TestModelFactory.buildTertiaryIntentionHospitalization(
            personId = personId,
            notifications = listOf(
                TertiaryIntentionNotification(
                    id = guiaId,
                    origin = Origin.TOTVS_GUIA,
                    status = OriginStatus.PENDING,
                    title = title,
                    createdAt = mockDateTime
                )
            )
        )
        coEvery {
            tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(
                personId = personId,
                types = types,
                dateInit = match { it == mockDateTime.minusDays(100) },
                dateLimit = match { it == mockDateTime.plusDays(100) }
            )
        } returns listOf(hospitalization).success()
        coEvery { totvsGuiaService.get(guiaId) } returns guia.success()
        coEvery {
            tertiaryIntentionService.update(
                hospitalization.copy(
                    notifications = listOf(
                        TertiaryIntentionNotification(
                            id = guiaId,
                            origin = Origin.TOTVS_GUIA,
                            status = OriginStatus.AUTHORIZED,
                            title = "guia ${guia.externalCode} atualizada para Autorizado",
                            createdAt = mockDateTime
                        )
                    )
                )
            )
        } returns hospitalization.success()

        val result = consumer.generateNotificationByGuia(guiaEvent)
        assertThat(result).isSuccess()

        coVerifyOnce { tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(any(), any(), any(), any()) }
        coVerifyOnce { totvsGuiaService.get(any()) }
        coVerifyOnce { tertiaryIntentionService.update(any()) }
    }

    @Test
    fun `generateNotificationByGuia should ignore notification when guia status is equals`() = runBlocking {
        val seen = TertiaryIntentionLastSeen(
            staffId = RangeUUID.generate(),
            seenAt = LocalDateTime.now()
        )
        val hospitalization = TestModelFactory.buildTertiaryIntentionHospitalization(
            personId = personId,
            notifications = listOf(
                TertiaryIntentionNotification(
                    id = guiaId,
                    origin = Origin.TOTVS_GUIA,
                    status = OriginStatus.AUTHORIZED,
                    seen = seen,
                    title = "guia 123456789 atualizou para Autorizado"
                )
            )
        )
        val guia = TestModelFactory.buildTotvsGuia(
            id = guiaId,
            personId = personId,
            requestedAt = mockDateTime.toLocalDate(),
            status = TotvsGuiaStatus.AUTHORIZED,
            type = MvUtil.TISS.HOSPITALIZATION
        )
        coEvery {
            tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(
                personId = personId,
                types = types,
                dateInit = match { it == mockDateTime.minusDays(100) },
                dateLimit = match { it == mockDateTime.plusDays(100) }
            )
        } returns listOf(hospitalization).success()
        coEvery { totvsGuiaService.get(guiaId) } returns guia.success()

        val result = consumer.generateNotificationByGuia(guiaEvent)
        assertThat(result).isSuccess()

        coVerifyOnce { tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(any(), any(), any(), any()) }
        coVerifyOnce { totvsGuiaService.get(any()) }
        coVerifyNone { tertiaryIntentionService.update(any()) }
    }

    @Test
    fun `generateNotificationByGuia should ignore notification when guia status is new and was seen`() = runBlocking {
        val seen = TertiaryIntentionLastSeen(
            staffId = RangeUUID.generate(),
            seenAt = LocalDateTime.now()
        )
        val hospitalization = TestModelFactory.buildTertiaryIntentionHospitalization(
            personId = personId,
            notifications = listOf(
                TertiaryIntentionNotification(
                    id = guiaId,
                    origin = Origin.TOTVS_GUIA,
                    status = OriginStatus.PENDING,
                    seen = seen,
                    title = "guia 123456789 atualizada para Pendente",
                    createdAt = mockDateTime
                )
            )
        )
        val guia = TestModelFactory.buildTotvsGuia(
            id = guiaId,
            personId = personId,
            requestedAt = mockDateTime.toLocalDate(),
            status = TotvsGuiaStatus.AUTHORIZED,
            type = MvUtil.TISS.HOSPITALIZATION
        )
        coEvery {
            tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(
                personId = personId,
                types = types,
                dateInit = match { it == mockDateTime.minusDays(100) },
                dateLimit = match { it == mockDateTime.plusDays(100) }
            )
        } returns listOf(hospitalization).success()
        coEvery { totvsGuiaService.get(guiaId) } returns guia.success()
        coEvery {
            tertiaryIntentionService.update(
                hospitalization.copy(
                    notifications = listOf(
                        TertiaryIntentionNotification(
                            id = guiaId,
                            origin = Origin.TOTVS_GUIA,
                            status = OriginStatus.PENDING,
                            seen = seen,
                            title = "guia 123456789 atualizada para Pendente",
                            createdAt = mockDateTime
                        ), TertiaryIntentionNotification(
                            id = guiaId,
                            origin = Origin.TOTVS_GUIA,
                            status = OriginStatus.AUTHORIZED,
                            seen = null,
                            title = "guia ${guia.externalCode} atualizada para Autorizado",
                            createdAt = mockDateTime
                        )
                    )
                )
            )
        } returns hospitalization.success()

        val result = consumer.generateNotificationByGuia(guiaEvent)
        assertThat(result).isSuccess()

        coVerifyOnce { tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(any(), any(), any(), any()) }
        coVerifyOnce { totvsGuiaService.get(any()) }
        coVerifyOnce { tertiaryIntentionService.update(any()) }
    }


    @Test
    fun `generateNotificationByGuia should ignore notification when guia type is exam`() = runBlocking {
        val guia = TestModelFactory.buildTotvsGuia(
            id = guiaId,
            personId = personId,
            requestedAt = mockDateTime.toLocalDate(),
            status = TotvsGuiaStatus.PENDING,
            type = MvUtil.TISS.EXAM
        )
        coEvery { totvsGuiaService.get(guiaId) } returns guia.success()

        val result = consumer.generateNotificationByGuia(guiaEvent)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { totvsGuiaService.get(any()) }
        coVerifyNone { tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(any(), any(), any(), any()) }
        coVerifyNone { tertiaryIntentionService.update(any()) }

    }

    @Test
    fun `generateNotificationByCounterReferral should register notification`() = runBlocking {
        val hospitalization = TestModelFactory.buildTertiaryIntentionHospitalization(personId = personId)
        val specialist = TestModelFactory.buildHealthProfessional()
        coEvery {
            tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(
                personId = personId,
                types = types,
                dateInit = match { it == mockDateTime.minusDays(100) },
                dateLimit = match { it == mockDateTime.plusDays(100) }
            )
        } returns listOf(hospitalization).success()
        coEvery { healthProfessionalService.findByStaffId(counterReferral.staffId) } returns specialist.success()
        coEvery {
            tertiaryIntentionService.update(
                hospitalization.copy(
                    notifications = listOf(
                        TertiaryIntentionNotification(
                            id = counterReferral.id,
                            origin = Origin.COUNTER_REFERRAL,
                            status = OriginStatus.COMPLETED,
                            title = "${specialist.name} publicou uma nova contrarreferência",
                            createdAt = mockDateTime
                        )
                    )
                )
            )
        } returns hospitalization.success()

        val result = consumer.generateNotificationByCounterReferral(counterReferalEvent)
        assertThat(result).isSuccess()

        coVerifyOnce { tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(any(), any(), any(), any()) }
        coVerifyOnce { tertiaryIntentionService.update(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
    }

    @Test
    fun `generateNotificationByCounterReferral should ignore notification when type of service is not surgical`() =
        runBlocking {
            val counterReferral = counterReferral.copy(typeOfService = CounterReferralTypeOfService.APPOINTMENT_ONLY)
            val counterReferalEvent = CounterReferralCreatedEvent(counterReferral)

            val result = consumer.generateNotificationByCounterReferral(counterReferalEvent)
            assertThat(result).isSuccess()

            coVerifyNone { tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(any(), any(), any(), any()) }
            coVerifyNone { tertiaryIntentionService.update(any()) }
        }

    @Test
    fun `generateNotificationByCounterReferral should ignore notification when CR is equals`() = runBlocking {
        val hospitalization = TestModelFactory.buildTertiaryIntentionHospitalization(
            personId = personId,
            notifications = listOf(
                TertiaryIntentionNotification(
                    id = counterReferral.id,
                    origin = Origin.COUNTER_REFERRAL,
                    status = OriginStatus.COMPLETED,
                    title = "Dr. Fulano publicou uma nova contrarreferência"
                )
            )
        )
        coEvery {
            tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(
                personId = personId,
                types = types,
                dateInit = match { it == mockDateTime.minusDays(100) },
                dateLimit = match { it == mockDateTime.plusDays(100) }
            )
        } returns listOf(hospitalization).success()

        val result = consumer.generateNotificationByCounterReferral(counterReferalEvent)
        assertThat(result).isSuccess()

        coVerifyOnce { tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(any(), any(), any(), any()) }
        coVerifyNone { tertiaryIntentionService.update(any()) }
    }
}
