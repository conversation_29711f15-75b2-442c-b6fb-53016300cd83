package br.com.alice.ehr.consumers

import br.com.alice.common.MvUtil
import br.com.alice.common.extensions.mapEach
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.CounterReferral
import br.com.alice.data.layer.models.CounterReferralTypeOfService
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.Origin
import br.com.alice.data.layer.models.OriginStatus
import br.com.alice.data.layer.models.TertiaryIntentionNotification
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.data.layer.models.TertiaryIntentionType
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.data.layer.models.TotvsGuiaStatus
import br.com.alice.ehr.client.TertiaryIntentionTouchPointService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.events.GuiaWithProceduresUpsertedEvent
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.secondary.attention.events.CounterReferralCreatedEvent
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.success

class TertiaryIntentionNotificationConsumer(
    private val tertiaryIntentionService: TertiaryIntentionTouchPointService,
    private val totvsGuiaService: TotvsGuiaService,
    private val healthProfessionalService: HealthProfessionalService
) : Consumer() {
    companion object {
        private val TYPES = listOf(
            TertiaryIntentionType.TIT_HOSPITALIZATION,
            TertiaryIntentionType.TIT_SURGERY
        )
        private val ALLOW_TYPES = listOf(
            MvUtil.TISS.HOSPITALIZATION,
            MvUtil.TISS.EXTENSION,
            MvUtil.TISS.SMALL_SURGERY,
            MvUtil.TISS.OPME,
        )
        private val ALLOW_TYPES_COUNTER_REFERRAL = listOf(
            CounterReferralTypeOfService.SURGICAL_PROCEDURE
        )
    }

    suspend fun generateNotificationByGuia(event: GuiaWithProceduresUpsertedEvent): Result<Any, Throwable> =
        span("registerNotificationByGuia") { span ->
            withSubscribersEnvironment {
                span.setAttribute("guia_id", event.payload.totvsGuiaId)
                val guia = totvsGuiaService.get(event.payload.totvsGuiaId).get()
                if (ALLOW_TYPES.contains(guia.type).not())
                    return@withSubscribersEnvironment false.success()

                tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(
                    personId = guia.personId,
                    types = TYPES,
                    dateInit = getStartAt(guia).minusDays(getDays()),
                    dateLimit = getStartAt(guia).plusDays(getDays())
                ).mapEach {
                    updatedNotificationIfNecessary(it, guia).get()
                }
            }
        }

    suspend fun generateNotificationByCounterReferral(event: CounterReferralCreatedEvent): Result<Any, Throwable> =
        span("generateNotificationByCounterReferral") { span ->
            withSubscribersEnvironment {
                val counterReferral = event.payload.counterReferral
                span.setAttribute("counter_referral_id", counterReferral.id)
                if (ALLOW_TYPES_COUNTER_REFERRAL.contains(counterReferral.typeOfService).not())
                    return@withSubscribersEnvironment false.success()

                tertiaryIntentionService.findTertiaryIntentionByPeriodAndTypes(
                    personId = counterReferral.personId,
                    types = TYPES,
                    dateInit = counterReferral.appointmentDate.minusDays(getDays()).atStartOfDay(),
                    dateLimit = counterReferral.appointmentDate.plusDays(getDays()).atStartOfDay()
                ).mapEach {
                    updatedNotificationIfNecessary(it, counterReferral).get()
                }.recordResult(span)
            }
        }

    private suspend fun updatedNotificationIfNecessary(
        tit: TertiaryIntentionTouchPoint,
        guia: TotvsGuia
    ): Result<TertiaryIntentionTouchPoint, Throwable> = span("updatedNotificationIfNecessary") { span ->
        span.setAttribute("guia_id", guia.id)
        span.setAttribute("guia_status", guia.status)
        span.setAttribute("tertiary_id", tit.id)
        val title = "guia ${guia.externalCode} atualizada para ${guia.status.description}"

        if (tit.notifications.any { it.id == guia.id && it.status == guia.status.toNotification() }) {
            span.setAttribute("update", false)
            return@span tit.success()
        }
        val notification = tit.notifications.firstOrNull { it.id == guia.id && it.seen == null }
        tit.notifications.minus(notification).plus(
            TertiaryIntentionNotification(
                id = guia.id,
                status = guia.status.toNotification(),
                origin = Origin.TOTVS_GUIA,
                seen = null,
                title = title
            )
        ).sortedBy {
            it?.createdAt
        }.let { notifications ->
            span.setAttribute("update", true)
            tertiaryIntentionService.update(tit.copy(notifications = notifications.filterNotNull()))
        }.recordResult(span)
    }

    private suspend fun updatedNotificationIfNecessary(
        tit: TertiaryIntentionTouchPoint,
        cr: CounterReferral
    ): Result<TertiaryIntentionTouchPoint, Throwable> = span("updatedNotificationIfNecessary") { span ->
        span.setAttribute("counter_referral_id", cr.id)
        span.setAttribute("tertiary_id", tit.id)
        if (tit.notifications.any { it.id == cr.id }) {
            span.setAttribute("update", false)
            return@span tit.success()
        }
        val specialist = healthProfessionalService.findByStaffId(cr.staffId).get()
        val title = "${specialist.name} publicou uma nova contrarreferência"

        tit.notifications.plus(
            TertiaryIntentionNotification(
                id = cr.id,
                status = OriginStatus.COMPLETED,
                origin = Origin.COUNTER_REFERRAL,
                seen = null,
                title = title
            )
        ).let { notifications ->
            span.setAttribute("update", true)
            tertiaryIntentionService.update(tit.copy(notifications = notifications))
        }.recordResult(span)
    }

    private fun getDays() = FeatureService.get(FeatureNamespace.EHR, "days_to_notification", 100).toLong()

    private fun getStartAt(guia: TotvsGuia) = guia.requestedAt.atStartOfDay()

    private fun TotvsGuiaStatus?.toNotification(): OriginStatus = when (this) {
        TotvsGuiaStatus.AUTHORIZED -> OriginStatus.AUTHORIZED
        TotvsGuiaStatus.CANCELLED -> OriginStatus.CANCELLED
        TotvsGuiaStatus.PARTIALLY_AUTHORIZED -> OriginStatus.PARTIALLY_AUTHORIZED
        TotvsGuiaStatus.PENDING -> OriginStatus.PENDING
        TotvsGuiaStatus.UNAUTHORIZED -> OriginStatus.UNAUTHORIZED
        TotvsGuiaStatus.UNKNOWN -> OriginStatus.UNKNOWN
        null -> OriginStatus.UNKNOWN
    }
}
