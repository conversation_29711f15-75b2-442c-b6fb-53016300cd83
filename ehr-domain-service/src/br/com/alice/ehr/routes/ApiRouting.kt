package br.com.alice.ehr.routes

import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.ehr.controllers.CounterReferralBackfillController
import br.com.alice.ehr.controllers.ExternalReferralBackfillController
import br.com.alice.ehr.controllers.MemedLayoutConfigurationController
import br.com.alice.ehr.controllers.MemedSyncController
import br.com.alice.ehr.controllers.StatusController
import br.com.alice.ehr.controllers.SyncConsolidatedRewardsController
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Routing.apiRoutes() {
    val statusController by inject<StatusController>()
    get("/status") { coHandler(statusController::status) }

    val memedLayoutConfigurationController by inject<MemedLayoutConfigurationController>()
    val memedSyncController by inject<MemedSyncController>()
    route("/memed"){
        route("/configure_layout") {
            post("/") { coHandler(memedLayoutConfigurationController::createLayoutForStaff) }
            post("/health_professionals") { coHandler(memedLayoutConfigurationController::syncLayoutForStaffs) }
        }
        post("/configure_empty_layout") { coHandler(memedLayoutConfigurationController::removeLayoutForStaff) }
        post("/force_sync") { coHandler(memedSyncController::forceMemedUserSync) }
    }

    val syncConsolidatedRewardsController by inject<SyncConsolidatedRewardsController>()
    post("/rewards/fill_success_reason") {
        coHandler(syncConsolidatedRewardsController::updatedSuccessReason)
    }

    route("/backfill") {
        val counterReferralBackfillController by inject<CounterReferralBackfillController>()

        post("/appointment/counter_referral") {
            coHandler(counterReferralBackfillController::doBackfill)
        }
        post("/counter_referral/appointment_date") {
            coHandler(counterReferralBackfillController::changeAppointmentDate)
        }

        val externalReferralBackfillController by inject<ExternalReferralBackfillController>()
        post("/external_referral/produce_created_event") {
            coHandler(externalReferralBackfillController::doBackfill)
        }

        post("/external_referral/produce_created_event_by_ids") {
            coHandler(externalReferralBackfillController::doBackfillById)
        }
    }
}
