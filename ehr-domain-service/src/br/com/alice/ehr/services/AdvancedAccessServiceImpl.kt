package br.com.alice.ehr.services

import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.Person
import br.com.alice.ehr.client.AdvancedAccessData
import br.com.alice.ehr.client.AdvancedAccessService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.map

class AdvancedAccessServiceImpl(
    private val personService: PersonService,
) : AdvancedAccessService {

    companion object {
        const val TWO_DAYS = 2
        const val THREE_DAYS = 3
    }

    override suspend fun getAdvancedAccessDurationByPersonId(personId: PersonId) =
        personService.get(personId).map { getDurationBasedOnAge(it) }

    override suspend fun getAdvancedAccessData(personId: PersonId) =
        personService.get(personId).map {
             AdvancedAccessData(
                durationInDays = getDurationBasedOnAge(it),
                isPediatric = it.isChild
            )
        }

    private fun getDurationBasedOnAge(person: Person) = if (person.isChild) TWO_DAYS else THREE_DAYS
}


