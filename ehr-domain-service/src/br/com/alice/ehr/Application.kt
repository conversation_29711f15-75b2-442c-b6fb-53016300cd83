package br.com.alice.ehr

import br.com.alice.appointment.ioc.AppointmentDomainClientModule
import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.business.ioc.BusinessDomainClientModule
import br.com.alice.channel.ioc.ChannelDomainClientModule
import br.com.alice.clinicalaccount.ioc.ClinicalAccountDomainClientModule
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.RunningMode
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.ioc.NotificationModule
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.notification.NotificationSubscription
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.common.storage.FileStorage
import br.com.alice.common.storage.LocalFileStorage
import br.com.alice.common.storage.S3FileStorage
import br.com.alice.communication.email.sender.EmailSenderClient
import br.com.alice.communication.email.sender.SimpleEmailServiceClient
import br.com.alice.communication.email.template.EmailTemplateClient
import br.com.alice.communication.email.template.SimpleEmailServiceTemplateClient
import br.com.alice.communication.ioc.CommunicationModule
import br.com.alice.coverage.ioc.CoverageDomainClientModule
import br.com.alice.data.layer.EHR_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.services.*
import br.com.alice.documentsigner.ioc.DocumentSignerModule
import br.com.alice.ehr.client.*
import br.com.alice.ehr.clients.memed.MemedClient
import br.com.alice.ehr.clients.memed.MemedClientImpl
import br.com.alice.ehr.communication.Mailer
import br.com.alice.ehr.consumers.*
import br.com.alice.ehr.controllers.CounterReferralBackfillController
import br.com.alice.ehr.controllers.ExternalReferralBackfillController
import br.com.alice.ehr.controllers.MemedLayoutConfigurationController
import br.com.alice.ehr.controllers.MemedSyncController
import br.com.alice.ehr.controllers.StatusController
import br.com.alice.ehr.controllers.SyncConsolidatedRewardsController
import br.com.alice.ehr.metrics.Metrics.registerMetrics
import br.com.alice.ehr.routes.apiRoutes
import br.com.alice.ehr.routes.kafkaRoutes
import br.com.alice.ehr.services.*
import br.com.alice.ehr.services.internal.CounterReferralInternalService
import br.com.alice.ehr.services.internal.InternalHealthcareTeamService
import br.com.alice.ehr.services.internal.assistance_summary.AssistanceSummaryBMIService
import br.com.alice.ehr.services.internal.assistance_summary.AssistanceSummaryComorbidityService
import br.com.alice.ehr.services.internal.assistance_summary.AssistanceSummaryPregnancyService
import br.com.alice.ehr.services.internal.assistance_summary.CreateAssistanceSummaryService
import br.com.alice.ehr.services.internal.consolidated_rewards.ConsolidatedRewardsService
import br.com.alice.ehr.services.internal.consolidated_rewards.ConsolidatedRewardsServiceImpl
import br.com.alice.ehr.services.internal.ehr_state.EhrStateService
import br.com.alice.ehr.services.internal.product_enrichment.ProductWithProvidersService
import br.com.alice.einsteinintegrationclient.ioc.EinsteinIntegrationClientModule
import br.com.alice.exec.indicator.ioc.ExecIndicatorDomainClientModule
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.fhir.ioc.FhirDomainClientModule
import br.com.alice.haoc.ioc.HaocIntegrationClientModule
import br.com.alice.healthcondition.ioc.HealthConditionDomainClientModule
import br.com.alice.healthlogic.ioc.HealthLogicDomainClientModule
import br.com.alice.healthplan.ioc.HealthPlanDomainClientModule
import br.com.alice.marauders.map.ioc.MaraudersMapDomainClientModule
import br.com.alice.membership.ioc.MembershipClientModule
import br.com.alice.onboarding.ioc.OnboardingClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.product.ioc.ProductDomainClientModule
import br.com.alice.provider.ioc.ProviderDomainClientModule
import br.com.alice.questionnaire.ioc.QuestionnaireDomainClientModule
import br.com.alice.sales_channel.ioc.SalesChannelDomainClientModule
import br.com.alice.schedule.ioc.AppointmentScheduleDomainClientModule
import br.com.alice.staff.ioc.StaffDomainClientModule
import com.typesafe.config.ConfigFactory
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.application.Application
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider
import software.amazon.awssdk.auth.credentials.AwsSessionCredentials
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.ses.SesClient

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))
    private const val timeoutInMillis = 300L

    val dependencyInjectionModules = DocumentSignerModule + listOf(
        ChannelDomainClientModule,
        CommunicationModule,
        FeatureConfigDomainClientModule,
        HaocIntegrationClientModule,
        QuestionnaireDomainClientModule,
        KafkaProducerModule,
        MembershipClientModule,
        AppointmentScheduleDomainClientModule,
        NotificationModule,
        ProviderDomainClientModule,
        HealthLogicDomainClientModule,
        HealthConditionDomainClientModule,
        EinsteinIntegrationClientModule,
        OnboardingClientModule,
        StaffDomainClientModule,
        FhirDomainClientModule,
        PersonDomainClientModule,
        ExecIndicatorDomainClientModule,
        BusinessDomainClientModule,
        MaraudersMapDomainClientModule,
        CoverageDomainClientModule,
        HealthPlanDomainClientModule,
        ClinicalAccountDomainClientModule,
        AppointmentDomainClientModule,
        ProductDomainClientModule,
        SalesChannelDomainClientModule,

        module(createdAtStart = true) {
            // Configuration
            single { config }
            single {
                DefaultHttpClient({
                    install(ContentNegotiation) {
                        simpleGson()
                    }
                }, timeoutInMillis = timeoutInMillis)
            }
            single { Mailer(get()) }

            when (val environment = BaseConfig.instance.runningMode) {
                RunningMode.DEVELOPMENT, RunningMode.TEST -> {
                    // create SimpleEmailServiceClient for development if there are credentials
                    val awsCredentials = AwsSessionCredentials.create(
                        config.property(
                            "${environment.value.lowercase()}.AWS_ACCESS_KEY_ID"
                        ).getString(),
                        config.property(
                            "${environment.value.lowercase()}.AWS_SECRET_ACCESS_KEY"
                        ).getString(),
                        config.property(
                            "${environment.value.lowercase()}.AWS_SESSION_TOKEN"
                        ).getString()
                    )
                    val awsCredentialsProvider = AwsCredentialsProvider { awsCredentials }
                    val client = SesClient.builder().credentialsProvider(
                        awsCredentialsProvider
                    ).region(Region.US_EAST_1).build()

                    single<FileStorage> { LocalFileStorage() }
                    single<EmailSenderClient> { SimpleEmailServiceClient(client) }
                    single<EmailTemplateClient> { SimpleEmailServiceTemplateClient(client) }
                }

                else -> {
                    single<FileStorage> { S3FileStorage() }
                    single<EmailSenderClient> { SimpleEmailServiceClient(get()) }
                    single<EmailTemplateClient> { SimpleEmailServiceTemplateClient(get()) }
                }
            }

            // Internal
            single { InternalHealthcareTeamService(get(), get()) }
            single { AssistanceSummaryPregnancyService(get()) }
            single { AssistanceSummaryBMIService(get()) }
            single { AssistanceSummaryComorbidityService(get(), get(), get()) }
            single { CreateAssistanceSummaryService(get(), get(), get(), get(), get(), get()) }
            single<MemedClient> { MemedClientImpl() }
            single { ProductWithProvidersService(get(), get()) }
            single { CounterReferralInternalService(get(), get(), get(), get(), get()) }
            single<ConsolidatedRewardsService> { ConsolidatedRewardsServiceImpl(get()) }
            single { EhrStateService() }

            // Controllers
            single { HealthController(SERVICE_NAME) }
            single { StatusController() }
            single { MemedLayoutConfigurationController(get(), get()) }
            single { MemedSyncController(get(), get()) }
            single { CounterReferralBackfillController(get(), get(), get()) }
            single { SyncConsolidatedRewardsController(get(), get()) }
            single { ExternalReferralBackfillController(get(), get()) }

            // Exposed Services
            single<ExternalReferralService> { ExternalReferralServiceImpl(get(), get()) }
            single<AppointmentAggregateService> {
                AppointmentAggregateServiceImpl(
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                )
            }
            single<AssistanceSummaryService> { AssistanceSummaryServiceImpl(get()) }
            single<CertificateSigningService> { CertificateSigningServiceImpl(get(), get()) }
            single<ClinicalBackgroundService> {
                ClinicalBackgroundServiceImpl(get(), get(), get(), get())
            }
            single<CounterReferralService> { CounterReferralServiceImpl(get(), get()) }
            single<CuriosityNoteService> { CuriosityNoteServiceImpl(get()) }
            single<HaocDocumentService> { HaocDocumentServiceImpl(get()) }
            single<HealthCommunityUnreferencedAccessService> {
                HealthCommunityUnreferencedAccessServiceImpl(
                    get()
                )
            }
            single<HealthMeasurementService> {
                HealthMeasurementServiceImpl(get(), get(), get(), get(), get(), get())
            }
            single<LaboratoryTestResultService> { LaboratoryTestResultServiceImpl(get()) }
            single<MailerService> { MailerServiceImpl(get()) }
            single<MedicineService> { MedicineServiceImpl(get()) }
            single<MemberCptsService> { MemberCptsServiceImpl(get(), get(), get(), get(), get(), get()) }
            single<MemberHealthMetricService> { MemberHealthMetricServiceImpl(get(), get(), get()) }
            single<PersonAdditionalInfoService> { PersonAdditionalInfoServiceImpl(get()) }
            single<PersonClinicalAccountStaffService> {
                PersonClinicalAccountStaffServiceImpl(
                    get(),
                    get(),
                )
            }
            single<PrescriptionSentenceService> { PrescriptionSentenceServiceImpl(get(), get()) }
            single<TestResultFileService> { TestResultFileServiceImpl(get(), get(), get(), get()) }
            single<TestPreparationService> { TestPreparationServiceImpl(get()) }
            single<ThirdPartyAppointmentService> {
                ThirdPartyAppointmentServiceImpl(get(), get(), get(), get())
            }
            single<DischargeSummaryFileService> { DischargeSummaryFileServiceImpl(get(), get()) }
            single<PregnancyService> { PregnancyServiceImpl(get(), get(), get()) }
            single<PrescriptionService> { MemedPrescriptionService(get(), get()) }
            single<PersonLightTagsService> { PersonLightTagsServiceImpl(get(), get(), get(), get(), get(), get()) }
            single<PersonHealthTagsService> { PersonHealthTagsServiceImpl(get(), get()) }
            single<TertiaryIntentionTouchPointService> {
                TertiaryIntentionTouchPointServiceImpl(get(), get())
            }
            single<GoalRewardsService> { GoalRewardsServiceImpl(get(), get(), get()) }
            single<AdvancedAccessService> { AdvancedAccessServiceImpl(get()) }

            loadServiceServers("br.com.alice.ehr.services")

            // Data Services
            val invoker = DataLayerClientConfiguration.build()
            single<AssistanceSummaryModelDataService> { AssistanceSummaryModelDataServiceClient(invoker) }
            single<ClinicalBackgroundDataService> { ClinicalBackgroundDataServiceClient(invoker) }
            single<ExternalReferralModelDataService> { ExternalReferralModelDataServiceClient(invoker) }
            single<CounterReferralDataService> { CounterReferralDataServiceClient(invoker) }
            single<HealthFormQuestionAnswerDataService> { HealthFormQuestionAnswerDataServiceClient(invoker) }
            single<HealthFormAnswerGroupDataService> { HealthFormAnswerGroupDataServiceClient(invoker) }
            single<CuriosityNoteModelDataService> { CuriosityNoteModelDataServiceClient(invoker) }
            single<HaocDocumentDataService> { HaocDocumentDataServiceClient(invoker) }
            single<HealthCommunityUnreferencedAccessModelDataService> {
                HealthCommunityUnreferencedAccessModelDataServiceClient(invoker)
            }
            single<LaboratoryTestResultModelDataService> { LaboratoryTestResultModelDataServiceClient(invoker) }
            single<MedicineModelDataService> { MedicineModelDataServiceClient(invoker) }
            single<MemberHealthMetricModelDataService> { MemberHealthMetricModelDataServiceClient(invoker) }
            single<TestResultFileModelDataService> { TestResultFileModelDataServiceClient(invoker) }
            single<TestPreparationModelDataService> { TestPreparationModelDataServiceClient(invoker) }
            single<PersonAdditionalInfoModelDataService> { PersonAdditionalInfoModelDataServiceClient(invoker) }
            single<PrescriptionSentenceModelDataService> { PrescriptionSentenceModelDataServiceClient(invoker) }
            single<HealthMeasurementModelDataService> { HealthMeasurementModelDataServiceClient(invoker) }
            single<HealthMeasurementTypeModelDataService> {
                HealthMeasurementTypeModelDataServiceClient(invoker)
            }
            single<HealthMeasurementCategoryModelDataService> {
                HealthMeasurementCategoryModelDataServiceClient(invoker)
            }
            single<PregnancyModelDataService> { PregnancyModelDataServiceClient(invoker) }
            single<TertiaryIntentionTouchPointDataService> {
                TertiaryIntentionTouchPointDataServiceClient(invoker)
            }
            single<DraftCommandModelDataService> { DraftCommandModelDataServiceClient(invoker) }
            single<StaffSignTokenModelDataService> { StaffSignTokenModelDataServiceClient(invoker) }
            single<ConsolidatedRewardsModelDataService> { ConsolidatedRewardsModelDataServiceClient(invoker) }
            single<HealthProfessionalModelDataService> { HealthProfessionalModelDataServiceClient(invoker) }

            // Kafka Consumers
            single { AppointmentConsumer(get(), get(), get(), get(), get()) }
            single { AppointmentScheduleConsumer(get(), get(), get()) }
            single { AutoCompleteTaskConsumer(get(), get(), get()) }
            single { ClinicalOutcomeRecordCreatedConsumer(get(), get(), get()) }
            single { HealthDeclarationFinishedConsumer(get(), get()) }
            single { HealthFormConsumer(get(), get()) }
            single {
                HealthLogicResultConsumer(
                    get(), get(), get(), get(), get(), get(), get(), get()
                )
            }
            single { HealthLogicTransformConsumer(get(), get(), get()) }
            single { MemberConsumer(get(), get()) }
            single { PregnancyConsumer(get()) }
            single { ReferralUpsertedConsumer(get()) }
            single { MemedStaffConsumer(get(), get()) }
            single { DraftAppointmentFinishedConsumer(get(), get(), get(), get(), get()) }
            single { DraftAppointmentDeletedConsumer(get()) }
            single { TertiaryIntentionTouchPointCreationConsumer(get(), get(), get(), get(), get()) }
            single { DischargeSummaryUpsertConsumer(get(), get()) }
            single { TestRequestExecutedConsumer(get()) }
            single { PersonCaseUpdatedConsumer(get()) }
            single { AssistanceScreeningChatCreatedConsumer(get()) }
            single { TertiaryIntentionNotificationConsumer(get(), get(), get()) }
            single { EmergencyExecutedConsumer(get(), get()) }
            single { CounterReferralConsumer(get(), get()) }
            single { HealthFormConfidenceQuestionAnswerConsumer(get(), get(), get(), get(), get()) }
            single { EhrStateConsumer(get()) }
        }
    )
}

@JvmOverloads
fun Application.module(dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules) {
    setupDomainService(dependencyInjectionModules) {
        authenticationBootstrap()

        install(NotificationSubscription.plugin)

        routing {
            application.attributes.put(PolicyRootServiceKey, EHR_DOMAIN_ROOT_SERVICE_NAME)
            apiRoutes()
        }

        kafkaConsumer(startRoutesSync = true) {
            serviceName = SERVICE_NAME
            kafkaRoutes()
        }

        featureConfigBootstrap(
            FeatureNamespace.EHR,
            FeatureNamespace.INTEROP,
            FeatureNamespace.EXEC_INDICATOR
        )
        registerMetrics()
    }
}
