package br.com.alice.common.core.extensions

import io.ktor.util.date.WeekDay
import org.assertj.core.api.Assertions.assertThat
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.SATURDAY
import java.time.DayOfWeek.SUNDAY
import java.time.DayOfWeek.THURSDAY
import java.time.DayOfWeek.TUESDAY
import java.time.DayOfWeek.WEDNESDAY
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.YearMonth
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.Date
import kotlin.test.Test
import kotlin.test.assertFailsWith

class DateExtensionsTest {

    @Test
    fun `LocalDateTime#toBrazilianDateFormat should return a format dd MM yyyy`() {
        val dateTime = LocalDateTime.of(2020, 12, 10, 20, 10)
        assertThat(dateTime.toBrazilianDateFormat()).isEqualTo("10/12/2020")
    }

    @Test
    fun `LocalDateTime#toBrazilianDateTimeFormat should return a format dd MM yyyy HH mm ss`() {
        val dateTime = LocalDateTime.of(2020, 12, 10, 20, 10, 32)
        assertThat(dateTime.toBrazilianDateTimeFormat()).isEqualTo("10/12/2020 20:10:32")
    }

    @Test
    fun `LocalDateTime#toFullDateFormat should return a format dd MMMM yyyy in pt-BR`() {
        val dateTime = LocalDateTime.of(2020, 12, 10, 20, 10, 32)
        assertThat(dateTime.toFullDateFormat()).isEqualTo("10 de dezembro de 2020")
    }

    @Test
    fun `LocalDate#atEndOfTheDay return a datetime at 23 59 59`() {
        val dateTime = LocalDate.now().atEndOfTheDay()
        assertThat(dateTime.hour).isEqualTo(23)
        assertThat(dateTime.minute).isEqualTo(59)
        assertThat(dateTime.second).isEqualTo(59)
    }

    @Test
    fun `LocalDateTime#atEndOfTheDay return end of day `() {
        val dateTime = LocalDateTime.of(2020, 12, 10, 20, 10, 32).atEndOfTheDay()
        assertThat(dateTime.hour).isEqualTo(23)
        assertThat(dateTime.minute).isEqualTo(59)
        assertThat(dateTime.second).isEqualTo(59)
    }

    @Test
    fun `LocalDateTime#atBeginningOfTheDay Beginning end of day `() {
        val dateTime = LocalDateTime.of(2020, 12, 10, 20, 10, 32).atBeginningOfTheDay()
        assertThat(dateTime.hour).isEqualTo(0)
        assertThat(dateTime.minute).isEqualTo(0)
        assertThat(dateTime.second).isEqualTo(0)
    }

    @Test
    fun `YearMonth#atStartOfTheMonth return a datetime at 23 59 59`() {
        val now = YearMonth.now()
        val date = now.atStartOfTheMonth()
        assertThat(date.dayOfMonth).isEqualTo(1)
        assertThat(date.month).isEqualTo(now.month)
        assertThat(date.year).isEqualTo(now.year)
    }

    @Test
    fun `#atSaoPauloZone`() {
        val date = LocalDateTime.parse("2021-03-10T10:00:00")
        val formatted = date.atSaoPauloZone()
        assertThat(formatted).isEqualTo(ZonedDateTime.parse("2021-03-10T10:00:00-03:00"))
    }

    @Test
    fun `LocalDate toCustomFormat returns at informed format`() {
        val date = LocalDate.parse("2018-12-31")
        val formatted = date.toCustomFormat("MM/yyyy/dd")
        assertThat(formatted).isEqualTo("12/2018/31")
    }

    @Test
    fun `LocalDate#isBeforeEq compares two dates, returns true when first period is before or equal second one, otherwise, false`() {
        val firstDate = LocalDate.parse("2022-11-15")
        val secondDate = LocalDate.parse("2022-11-16")

        assertThat(firstDate.isBeforeEq(secondDate)).isTrue
        assertThat(secondDate.isBeforeEq(firstDate)).isFalse
    }

    @Test
    fun `toCustomFormat returns at informed format`() {
        val date = LocalDateTime.parse("2018-12-31T10:00:00")
        val formatted = date.toCustomFormat("MM/yyyy/dd")
        assertThat(formatted).isEqualTo("12/2018/31")
    }

    @Test
    fun `#minLocalDateTimeWithSaoPauloTimeZone`() {
        val date = LocalDateTime.parse("2018-12-31T10:00:00")
        val formatted = date.minLocalDateTimeWithSaoPauloTimeZone()
        assertThat(formatted).isEqualTo(LocalDateTime.parse("2018-12-31T00:00:00").fromSaoPauloToUTCTimeZone())
    }

    @Test
    fun `#maxLocalDateTimeWithSaoPauloTimeZone`() {
        val date = LocalDateTime.parse("2018-12-31T10:00:00")
        val formatted = date.maxLocalDateTimeWithSaoPauloTimeZone()
        assertThat(formatted).isEqualTo(LocalDateTime.parse("2018-12-31T23:59:59").fromSaoPauloToUTCTimeZone())
    }

    @Test
    fun `LocalDateTime#toQuarterHour should round LocalDateTime to the nearest 15 minutes window`() {
        assertThat(
            LocalDateTime.parse("2020-01-01T10:00:00").toQuarterHour()
        ).isEqualTo(LocalDateTime.parse("2020-01-01T10:00:00"))
        assertThat(
            LocalDateTime.parse("2020-01-01T10:01:00").toQuarterHour()
        ).isEqualTo(LocalDateTime.parse("2020-01-01T10:00:00"))
        assertThat(
            LocalDateTime.parse("2020-01-01T10:07:00").toQuarterHour()
        ).isEqualTo(LocalDateTime.parse("2020-01-01T10:00:00"))
        assertThat(
            LocalDateTime.parse("2020-01-01T10:08:00").toQuarterHour()
        ).isEqualTo(LocalDateTime.parse("2020-01-01T10:15:00"))
        assertThat(
            LocalDateTime.parse("2020-01-01T10:10:00").toQuarterHour()
        ).isEqualTo(LocalDateTime.parse("2020-01-01T10:15:00"))
        assertThat(
            LocalDateTime.parse("2020-01-01T10:15:00").toQuarterHour()
        ).isEqualTo(LocalDateTime.parse("2020-01-01T10:15:00"))
        assertThat(
            LocalDateTime.parse("2020-01-01T10:16:00").toQuarterHour()
        ).isEqualTo(LocalDateTime.parse("2020-01-01T10:15:00"))
        assertThat(
            LocalDateTime.parse("2020-01-01T10:22:00").toQuarterHour()
        ).isEqualTo(LocalDateTime.parse("2020-01-01T10:15:00"))
        assertThat(
            LocalDateTime.parse("2020-01-01T10:23:00").toQuarterHour()
        ).isEqualTo(LocalDateTime.parse("2020-01-01T10:30:00"))
    }

    @Test
    fun `#roundDownToClosestMinute - minutes is valid`() {
        val dates = listOf(
            LocalDateTime.parse("2021-09-27T20:19:54.000"),
            LocalDateTime.parse("2021-09-27T20:20:13.000"),
            LocalDateTime.parse("2021-09-27T20:17:54.000"),
            LocalDateTime.parse("2021-09-27T20:16:54.000"),
            LocalDateTime.parse("2021-09-27T20:20:01.000"),
        )
        val expected = listOf(
            LocalDateTime.parse("2021-09-27T20:15:00"),
            LocalDateTime.parse("2021-09-27T20:20:00"),
            LocalDateTime.parse("2021-09-27T20:15:00"),
            LocalDateTime.parse("2021-09-27T20:15:00"),
            LocalDateTime.parse("2021-09-27T20:20:00"),
        )
        val formatted = dates.map { it.roundDownToClosestMinute(5) }
        assertThat(formatted).isEqualTo(expected)
    }

    @Test
    fun `#roundDownToClosestMinute - invalid minutes`() {
        val date = LocalDateTime.parse("2021-09-27T20:19:54.000")
        assertFailsWith<IllegalArgumentException> { date.roundDownToClosestMinute(0) }
        assertFailsWith<IllegalArgumentException> { date.roundDownToClosestMinute(61) }
    }

    @Test
    fun `#roundDownToClosestHour - valid hour`() {
        val date = LocalDateTime.parse("2021-09-27T20:19:54.000")
        val formatted1 = date.roundDownToClosestHour(4)
        assertThat(formatted1).isEqualTo(LocalDateTime.parse("2021-09-27T20:00:00.000"))

        val formatted2 = date.roundDownToClosestHour(8)
        assertThat(formatted2).isEqualTo(LocalDateTime.parse("2021-09-27T16:00:00.000"))

        val formatted3 = date.roundDownToClosestHour(6)
        assertThat(formatted3).isEqualTo(LocalDateTime.parse("2021-09-27T18:00:00.000"))
    }

    @Test
    fun `#roundDownToClosestMinute - invalid hour`() {
        val date = LocalDateTime.parse("2021-09-27T20:19:54.000")
        assertFailsWith<IllegalArgumentException> { date.roundDownToClosestHour(0) }
        assertFailsWith<IllegalArgumentException> { date.roundDownToClosestHour(25) }
    }

    @Test
    fun `LocalDate#atEndOfTheMonth should return the last day of the month from the given date`() {
        val date = LocalDate.of(2021, 2, 1)
        assertThat(date.atEndOfTheMonth()).isEqualTo(LocalDate.of(2021, 2, 28))
    }

    @Test
    fun `LocalDate#atBeginningOfTheMonth should return the first day of the mont from the given date`() {
        val date = LocalDate.of(2021, 2, 20)
        assertThat(date.atBeginningOfTheMonth()).isEqualTo(LocalDate.of(2021, 2, 1))
    }

    @Test
    fun `LocalDate#isSameMonth should return correct value`() {
        val date = LocalDate.of(2021, 2, 20)

        assertThat(LocalDate.of(2021, 2, 10).isSameMonth(date)).isTrue
        assertThat(LocalDate.of(2021, 2, 1).isSameMonth(date)).isTrue
        assertThat(LocalDate.of(2021, 2, 28).isSameMonth(date)).isTrue
        assertThat(LocalDate.of(2021, 2, 20).isSameMonth(date)).isTrue
        assertThat(LocalDate.of(2021, 1, 20).isSameMonth(date)).isFalse
        assertThat(LocalDate.of(2022, 2, 20).isSameMonth(date)).isFalse
    }

    @Test
    fun `LocalDate#toNextWorkingDayExcludingWeekends`() {
        val dateOnSaturday = LocalDate.of(2021, 2, 20)
        val dateOnFriday = LocalDate.of(2021, 2, 19)
        val dateOnSunday = LocalDate.of(2021, 2, 21)

        assertThat(dateOnSaturday.toNextWorkingDayExcludingWeekends().dayOfWeek).isEqualTo(MONDAY)
        assertThat(dateOnFriday.toNextWorkingDayExcludingWeekends().dayOfWeek).isEqualTo(MONDAY)
        assertThat(dateOnSunday.toNextWorkingDayExcludingWeekends().dayOfWeek).isEqualTo(MONDAY)
        assertThat(dateOnSunday.dayOfWeek).isEqualTo(SUNDAY)
        assertThat(dateOnFriday.dayOfWeek).isEqualTo(FRIDAY)
        assertThat(dateOnSaturday.dayOfWeek).isEqualTo(SATURDAY)
    }

    @Test
    fun `LocalDateTime#toMonthDayDateFormat should return a format dd MMMM yyyy in pt-BR`() {
        val dateTime = LocalDateTime.of(2021, 12, 10, 20, 10, 32)

        assertThat(dateTime.toMonthDayDateFormat()).isEqualTo("Sexta-feira, 10 de dezembro")
    }

    @Test
    fun `LocalDateTime#toWeekDayMonthDateFormat should return a format resumed EEEE, dd de MMMM`() {
        val dateTime = LocalDateTime.of(2021, 12, 10, 20, 10, 32)
        assertThat(dateTime.toWeekDayMonthDateFormat()).isEqualTo("Sexta, 10 de dezembro")

        val dateTime2 = LocalDateTime.of(2021, 12, 12, 20, 10, 32)
        assertThat(dateTime2.toWeekDayMonthDateFormat()).isEqualTo("Domingo, 12 de dezembro")
    }

    @Test
    fun `LocalDateTime#toWeekDayFullDateFormat should return a format dd MMMM yyyy in pt-BR`() {
        val dateTime = LocalDateTime.of(2021, 12, 10, 20, 10, 32)

        assertThat(dateTime.toWeekDayFullDateFormat()).isEqualTo("Sexta-feira, 10 de dezembro de 2021")
    }

    @Test
    fun `LocalTime#fromSaoPauloToUTCTimeZone should add 3 hours to LocalTime`() {
        val time = LocalTime.of(8, 0).fromSaoPauloToUTCTimeZone()

        assertThat(time).hasSameHourAs(LocalTime.of(11, 0))
    }

    @Test
    fun `LocalTime#fromSaoPauloToUTCTimeZone should add 3 hours to LocalTime with time greater than 12h`() {
        val time = LocalTime.of(15, 0).fromSaoPauloToUTCTimeZone()

        assertThat(time).hasSameHourAs(LocalTime.of(18, 0))
    }

    @Test
    fun `LocalTime#fromSaoPauloToUTCTimeZone should add 3 hours to LocalTime and move to next day`() {
        val time = LocalTime.of(22, 0).fromSaoPauloToUTCTimeZone()

        assertThat(time).hasSameHourAs(LocalTime.of(1, 0))
    }

    @Test
    fun `#PeriodsOverlap should return false when first period is before second period`() {
        val first = Pair(
            LocalDateTime.now(), LocalDateTime.now().plusHours(1)
        )
        val second = Pair(
            LocalDateTime.now().plusHours(2), LocalDateTime.now().plusHours(3)
        )

        assertThat(periodsOverlap(first, second)).isFalse
    }

    @Test
    fun `#PeriodsOverlap should return true when they overlap at the end of first period`() {
        val first = Pair(
            LocalDateTime.now(), LocalDateTime.now().plusHours(1)
        )
        val second = Pair(
            LocalDateTime.now().plusMinutes(45), LocalDateTime.now().plusHours(3)
        )

        assertThat(periodsOverlap(first, second)).isTrue
    }

    @Test
    fun `#PeriodsOverlap should return true when they have same time`() {
        val first = Pair(
            LocalDateTime.now(), LocalDateTime.now().plusHours(1)
        )
        val second = Pair(
            LocalDateTime.now(), LocalDateTime.now().plusHours(1)
        )

        assertThat(periodsOverlap(first, second)).isTrue
    }

    @Test
    fun `#PeriodsOverlap should return true when second period contains first period`() {
        val first = Pair(
            LocalDateTime.now(), LocalDateTime.now().plusHours(1)
        )
        val second = Pair(
            LocalDateTime.now().minusMinutes(15), LocalDateTime.now().plusHours(1).plusMinutes(15)
        )

        assertThat(periodsOverlap(first, second)).isTrue
    }

    @Test
    fun `#PeriodsOverlap should return true when they overlap at the end of second period`() {
        val first = Pair(
            LocalDateTime.now(), LocalDateTime.now().plusHours(1)
        )
        val second = Pair(
            LocalDateTime.now().minusHours(1), LocalDateTime.now().plusMinutes(15)
        )

        assertThat(periodsOverlap(first, second)).isTrue
    }

    @Test
    fun `#timesOverlap should return false when first time is before second time`() {
        val date = LocalTime.of(13, 0, 0)
        val first = Pair(
            date, date.plusHours(1)
        )
        val second = Pair(
            date.plusHours(2), date.plusHours(3)
        )

        assertThat(timesOverlap(first, second)).isFalse
    }

    @Test
    fun `#timesOverlap should return true when they overlap at the end of first time`() {
        val date = LocalTime.of(13, 0, 0)
        val first = Pair(
            date, date.plusHours(1)
        )
        val second = Pair(
            date.plusMinutes(45), date.plusHours(3)
        )

        assertThat(timesOverlap(first, second)).isTrue
    }

    @Test
    fun `#timesOverlap should return true when they have same time`() {
        val now = LocalTime.of(13, 0, 0)
        val first = Pair(
            now, now.plusHours(1)
        )
        val second = Pair(
            now, now.plusHours(1)
        )

        assertThat(timesOverlap(first, second)).isTrue
    }

    @Test
    fun `#timesOverlap should return true when second period contains first period`() {
        val date = LocalTime.of(10, 0, 0)
        val first = Pair(
            date, date.plusHours(1)
        )
        val second = Pair(
            date.minusMinutes(15), date.plusHours(1).plusMinutes(15)
        )

        assertThat(timesOverlap(first, second)).isTrue
    }

    @Test
    fun `#timesOverlap should return true when they overlap at the end of second period`() {
        val date = LocalTime.of(13, 0, 0)
        val first = Pair(
            date, date.plusHours(1)
        )
        val second = Pair(
            date.minusHours(1), date.plusMinutes(15)
        )

        assertThat(timesOverlap(first, second)).isTrue
    }

    @Test
    fun `#timesOverlap should return false when it involves passing day but do not overlap goes to next day different order`() {
        val first = Pair(
            LocalTime.of(12, 0), LocalTime.of(13, 0)
        )
        val second = Pair(
            LocalTime.of(23, 0), LocalTime.of(0, 0)
        )

        assertThat(timesOverlap(second, first)).isFalse
    }

    @Test
    fun `#timesOverlap should return false when it involves passing day but do not overlap goes to next day`() {
        val first = Pair(
            LocalTime.of(12, 0), LocalTime.of(13, 0)
        )
        val second = Pair(
            LocalTime.of(23, 0), LocalTime.of(0, 0)
        )

        assertThat(timesOverlap(first, second)).isFalse
    }

    @Test
    fun `#timesOverlap should return true when it involves passing day and overlap`() {
        val first = Pair(
            LocalTime.of(0, 0), LocalTime.of(2, 0)
        )
        val second = Pair(
            LocalTime.of(23, 0), LocalTime.of(1, 0)
        )

        assertThat(timesOverlap(first, second)).isFalse
    }

    @Test
    fun `#ZonedDateTime#toZoomDateStringFormat should return date string in zoom format`() {
        val date = LocalDateTime.of(2022, 1, 1, 10, 0).atZone(ZoneId.of("UTC"))
        val expectedDateString = "2022-01-01T10:00:00Z"

        assertThat(date.toZoomDateStringFormat()).isEqualTo(expectedDateString)
    }

    @Test
    fun `#nextWeekDay should return right week day according current week day`() {
        val mondayWeekDay = WeekDay.MONDAY
        val thursdayWeekDay = WeekDay.THURSDAY

        assertThat(mondayWeekDay.nextDay()).isEqualTo(WeekDay.TUESDAY)
        assertThat(thursdayWeekDay.nextDay()).isEqualTo(WeekDay.FRIDAY)
    }

    @Test
    fun `#previousWeekDay should return right week day according current week day`() {
        val mondayWeekDay = WeekDay.MONDAY
        val thursdayWeekDay = WeekDay.THURSDAY

        assertThat(mondayWeekDay.previousDay()).isEqualTo(WeekDay.SUNDAY)
        assertThat(thursdayWeekDay.previousDay()).isEqualTo(WeekDay.WEDNESDAY)
    }

    @Test
    fun `#betweenDates should return true when date is equals`() {

        val initialDate = LocalDate.of(2022, 10, 20)
        val limitDate = initialDate.plusDays(1)

        val date = LocalDate.of(2022, 10, 20)

        val response = date.betweenDates(initialDate, limitDate)

        assertThat(response).isTrue
    }

    @Test
    fun `#betweenDates should return true when is in between dates`() {
        val initialDate = LocalDate.of(2022, 10, 20)
        val limitDate = initialDate.plusDays(1)
        val date = LocalDate.of(2022, 10, 21)

        val response = date.betweenDates(initialDate, limitDate)

        assertThat(response).isTrue
    }

    @Test
    fun `#betweenDates should return true when isn't in between dates`() {
        val initialDate = LocalDate.of(2022, 10, 20)
        val limitDate = initialDate.plusDays(1)
        val date = LocalDate.of(2022, 10, 22)

        val response = date.betweenDates(initialDate, limitDate)

        assertThat(response).isFalse
    }

    @Test
    fun `#isAfterEq should return true when is equals date`() {
        val otherDate = LocalDate.of(2022, 10, 20)
        val date = LocalDate.of(2022, 10, 20)

        val response = date.isAfterEq(otherDate)

        assertThat(response).isTrue
    }

    @Test
    fun `#isAfterEq should return true when is after date`() {
        val otherDate = LocalDate.of(2022, 10, 19)
        val date = LocalDate.of(2022, 10, 20)

        val response = date.isAfterEq(otherDate)

        assertThat(response).isTrue
    }

    @Test
    fun `#isAfterEq should return false when is before date`() {
        val otherDate = LocalDate.of(2022, 10, 21)
        val date = LocalDate.of(2022, 10, 20)

        val response = date.isAfterEq(otherDate)

        assertThat(response).isFalse
    }

    @Test
    fun `#monthSize should return monthSize`() {
        val januaryDate = LocalDateTime.of(2022, 1, 21, 0, 0, 0)
        val aprilDate = LocalDateTime.of(2022, 4, 21, 0, 0, 0)
        val febuaryDate = LocalDateTime.of(2023, 2, 21, 0, 0, 0)
        val leapYearFebuaryDate = LocalDateTime.of(2024, 2, 21, 0, 0, 0)


        val januaryDateResponse = januaryDate.monthSize()
        val aprilDateResponse = aprilDate.monthSize()
        val febuaryDateResponse = febuaryDate.monthSize()
        val leapYearFebuaryDateResponse = leapYearFebuaryDate.monthSize()

        assertThat(januaryDateResponse).isEqualTo(31)
        assertThat(aprilDateResponse).isEqualTo(30)
        assertThat(febuaryDateResponse).isEqualTo(28)
        assertThat(leapYearFebuaryDateResponse).isEqualTo(29)
    }

    @Test
    fun `beginningOfTheMonthWithSaoPauloTimezone works correctly`() {
        val date = LocalDateTime.of(2020, 1, 10, 10, 0, 0)
        val beginningOfTheMonthWithSaoPauloTimezone = beginningOfTheMonthWithSaoPauloTimezone(date)
        assertThat(beginningOfTheMonthWithSaoPauloTimezone).isEqualTo(LocalDate.of(2020, 1, 1))
    }

    @Test
    fun `endOfTheMonthWithSaoPauloTimezone works correctly`() {
        val date = LocalDateTime.of(2020, 1, 10, 10, 0, 0)
        val endOfTheMonthWithSaoPauloTimezone = endOfTheMonthWithSaoPauloTimezone(date)
        assertThat(endOfTheMonthWithSaoPauloTimezone).isEqualTo(LocalDate.of(2020, 1, 31))
    }

    @Test
    fun `LocalDateTime#toMonthName should return month name in brazilian locale`() {
        val date = LocalDateTime.of(2020, 2, 10, 10, 0, 0)
        assertThat(date.toMonthName()).isEqualTo("Fevereiro")
    }

    @Test
    fun `#isWorkingDay returns true from monday to friday`() {
        val workingDays = listOf(
            MONDAY,
            TUESDAY,
            WEDNESDAY,
            THURSDAY,
            FRIDAY
        )

        workingDays.forEach {
            assertThat(it.isWorkingDay()).isTrue()
        }
    }

    @Test
    fun `#isWorkingDay returns false if weekends`() {
        val workingDays = listOf(
            SATURDAY, SUNDAY
        )

        workingDays.forEach {
            assertThat(it.isWorkingDay()).isFalse()
        }
    }

    @Test
    fun `#toDate returns a Date instance for LocalDateTime`() {
        val instant = Instant.ofEpochMilli(1721038272000) // 2024-07-15 10:11:12
        val localDateTime = LocalDateTime.ofInstant(instant, ZoneOffset.systemDefault())
        val date = Date.from(instant)

        assertThat(localDateTime.toDate()).isEqualTo(date)
    }

    @Test
    fun `#daysUntil - should return the number of days until the given date`() {

        val today = LocalDate.now()
        val tomorrow = today.plusDays(1)
        val dayAfterTomorrow = today.plusDays(2)

        assertThat(daysDiff(today, today)).isEqualTo(0)
        assertThat(daysDiff(today, tomorrow)).isEqualTo(1)
        assertThat(daysDiff(today, dayAfterTomorrow)).isEqualTo(2)
    }
}
