package br.com.alice.common.core.extensions

import io.ktor.util.date.WeekDay
import java.time.DayOfWeek
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.SATURDAY
import java.time.DayOfWeek.SUNDAY
import java.time.DayOfWeek.THURSDAY
import java.time.DayOfWeek.TUESDAY
import java.time.DayOfWeek.WEDNESDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.YearMonth
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeFormatterBuilder
import java.time.temporal.ChronoField
import java.time.temporal.ChronoUnit
import java.util.Date
import java.util.Locale

val formatter: DateTimeFormatter = baseFormatter("dd/MM/yyyy")
fun baseFormatter(stringFormat: String): DateTimeFormatter = DateTimeFormatter.ofPattern(stringFormat)

val yearMonthFormatter: DateTimeFormatter = DateTimeFormatterBuilder()
    .appendPattern("yyyy-MM")
    .parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
    .toFormatter()

fun LocalDate.toBrazilianDateFormat(): String = format(formatter)

fun LocalDate.toCustomFormat(format: String): String = format(baseFormatter(format))

fun LocalDateTime.toBrazilianDateFormat(): String = format(formatter)

fun LocalDateTime.toCustomFormat(format: String): String = format(baseFormatter(format))

fun LocalDateTime.toBrazilianDateTimeFormat(): String = format(baseFormatter("dd/MM/yyyy HH:mm:ss"))

fun LocalDateTime.toBrazilianTimeFormat(): String = format(baseFormatter("HH:mm"))

fun LocalDateTime.toSaoPauloTimeZone(): LocalDateTime =
    LocalDateTime.ofInstant(
        this.atZone(ZoneId.of("UTC"))
            .toInstant(), ZoneId.of("America/Sao_Paulo")
    )

fun LocalDateTime.fromSaoPauloToUTCTimeZone(): LocalDateTime =
    LocalDateTime.ofInstant(
        this.atZone(ZoneId.of("America/Sao_Paulo"))
            .toInstant(), ZoneId.of("UTC")
    )

fun LocalDateTime.atSaoPauloZone(): ZonedDateTime =
    this.atZone(ZoneId.of("America/Sao_Paulo"))

fun LocalDateTime.minLocalDateTimeWithSaoPauloTimeZone(): LocalDateTime =
    this.with(LocalTime.MIN).fromSaoPauloToUTCTimeZone()

fun LocalDateTime.maxLocalDateTimeWithSaoPauloTimeZone(): LocalDateTime =
    this.with(LocalTime.MAX).truncatedTo(ChronoUnit.SECONDS).fromSaoPauloToUTCTimeZone()

fun LocalDateTime.toFullDateFormat(locale: Locale = Locale.forLanguageTag("pt-BR")): String {
    val brazilianFormatter = DateTimeFormatter
        .ofPattern("dd 'de' MMMM 'de' yyyy")
        .withLocale(locale)

    return this.format(brazilianFormatter).lowercase()
}

fun LocalDateTime.toMonthDayDateFormat(locale: Locale = Locale.forLanguageTag("pt-BR")): String {
    val brazilianFormatter = DateTimeFormatter
        .ofPattern("EEEE, dd 'de' MMMM")
        .withLocale(locale)

    return this.format(brazilianFormatter).lowercase().capitalize()
}

fun LocalDateTime.toWeekDayMonthDateFormat(locale: Locale = Locale.forLanguageTag("pt-BR")): String {
    val informalWeekDayName = when (this.dayOfWeek) {
        MONDAY -> "segunda"
        TUESDAY -> "terça"
        WEDNESDAY -> "quarta"
        THURSDAY -> "quinta"
        FRIDAY -> "sexta"
        SATURDAY -> "sábado"
        SUNDAY -> "domingo"
        else -> ""
    }.capitalize()

    val dayMonthBrazilianFormatter = DateTimeFormatter.ofPattern("dd 'de' MMMM").withLocale(locale)
    return "$informalWeekDayName, ${this.format(dayMonthBrazilianFormatter).lowercase().capitalize()}"
}

fun LocalDateTime.toWeekDayFullDateFormat(locale: Locale = Locale.forLanguageTag("pt-BR")): String {
    val brazilianFormatter = DateTimeFormatter
        .ofPattern("EEEE, dd 'de' MMMM 'de' yyyy")
        .withLocale(locale)

    return this.format(brazilianFormatter).lowercase().capitalize()
}

fun LocalDateTime.toQuarterHour(): LocalDateTime {
    return this.truncatedTo(ChronoUnit.HOURS).plusMinutes(((this.minute + 7) / 15L) * 15L)
}

fun LocalDate.atBeginningOfTheDay(): LocalDateTime = this.atTime(0, 0, 0)
fun LocalDate.atBeginningOfTheMonth(): LocalDate = this.withDayOfMonth(1)
fun LocalDate.atEndOfTheDay(): LocalDateTime = this.atTime(23, 59, 59)
fun LocalDateTime.atBeginningOfTheDay(): LocalDateTime = this.toLocalDate().atBeginningOfTheDay()
fun LocalDateTime.atEndOfTheDay(): LocalDateTime = this.toLocalDate().atEndOfTheDay()
fun LocalDate.atEndOfTheMonth(): LocalDate = this.withDayOfMonth(this.lengthOfMonth())
fun YearMonth.atStartOfTheMonth(): LocalDate = this.atDay(1)

fun LocalDateTime.roundDownToClosestMinute(minute: Int): LocalDateTime {
    require(minute in 1..60) { "Invalid number of minute to round down" }
    return this.truncatedTo(ChronoUnit.MINUTES).minusMinutes(this.minute.toLong() % minute)
}

fun LocalDateTime.roundDownToClosestHour(hour: Int): LocalDateTime {
    require(hour in 1..24) { "Invalid number of minute to round down" }
    return this.truncatedTo(ChronoUnit.HOURS).minusHours(this.hour.toLong() % hour)
}

fun LocalDate.isSameMonthOrBefore(referenceDate: LocalDate) =
    this.isBeforeEq(referenceDate.atEndOfTheMonth())

fun LocalDate.isSameMonthOrAfter(referenceDate: LocalDate) =
    this.isAfterEq(referenceDate.atBeginningOfTheMonth())

fun LocalDate.isSameMonth(date: LocalDate): Boolean =
    this.monthValue == date.monthValue && this.year == date.year

fun LocalDate.toNextWorkingDayExcludingWeekends(): LocalDate =
    when (this.dayOfWeek) {
        FRIDAY -> this.plusDays(3)
        SATURDAY -> this.plusDays(2)
        else -> this.plusDays(1)
    }

fun LocalTime.fromSaoPauloToUTCTimeZone(): LocalTime {
    val now = LocalDateTime.now()
    val zone = ZoneId.of("America/Sao_Paulo")
    val zoneOffsetBrazil = zone.rules.getOffset(now)
    return this.atOffset(zoneOffsetBrazil).withOffsetSameInstant(ZoneOffset.UTC).toLocalTime()
}

fun LocalTime.fromUTCToSaoPauloTimeZone(): LocalTime {
    val now = LocalDateTime.now()
    val zone = ZoneId.of("America/Sao_Paulo")
    val zoneOffsetBrazil = zone.rules.getOffset(now)
    return this.atOffset(ZoneOffset.UTC).withOffsetSameInstant(zoneOffsetBrazil).toLocalTime()
}

fun LocalDate.isBeforeEq(date: LocalDate): Boolean =
    this.isBefore(date) || this.isEqual(date)

fun LocalDate.isAfterEq(date: LocalDate): Boolean =
    this.isAfter(date) || this.isEqual(date)

fun LocalDate.betweenDates(initialDate: LocalDate, limitDate: LocalDate): Boolean =
    this.isAfterEq(initialDate) && this.isBeforeEq(limitDate)

fun LocalDateTime.isBeforeEq(date: LocalDateTime): Boolean =
    this.isBefore(date) || this.isEqual(date)

fun LocalDateTime.isAfterEq(date: LocalDateTime): Boolean =
    this.isAfter(date) || this.isEqual(date)

fun LocalDateTime.monthSize(): Int =
    this.month.length(this.toLocalDate().isLeapYear)

fun LocalTime.isBeforeEq(time: LocalTime): Boolean =
    this.isBefore(time) || this == time

fun LocalDateTime.toMonthName(locale: Locale = Locale.forLanguageTag("pt-BR")): String {
    val localeFormatter = DateTimeFormatter
        .ofPattern("MMMM")
        .withLocale(locale)

    return this.format(localeFormatter).lowercase().capitalize()
}

fun periodsOverlap(
    firstPeriod: Pair<LocalDateTime, LocalDateTime>,
    secondPeriod: Pair<LocalDateTime, LocalDateTime>
): Boolean {
    val firstPeriodStartDateIsBeforeSecondPeriodStartDate = firstPeriod.first.isBefore(secondPeriod.first)
    val firstPeriodEndDateIsAfterSecondPeriodStartDate = firstPeriod.second.isAfter(secondPeriod.first)

    val firstPeriodStartDateIsAfterEqSecondPeriodStartDate = firstPeriod.first.isAfterEq(secondPeriod.first)
    val firstPeriodEndDateIsBeforeEqSecondPeriodEndDate = firstPeriod.second.isBeforeEq(secondPeriod.second)

    val firstPeriodStartDateIsBeforeSecondPeriodEndDate = firstPeriod.first.isBefore(secondPeriod.second)
    val firstPeriodEndDateIsAfterSecondPeriodEndDate = firstPeriod.second.isAfter(secondPeriod.second)

    return firstPeriodStartDateIsBeforeSecondPeriodStartDate.and(firstPeriodEndDateIsAfterSecondPeriodStartDate) ||
            firstPeriodStartDateIsAfterEqSecondPeriodStartDate.and(firstPeriodEndDateIsBeforeEqSecondPeriodEndDate) ||
            firstPeriodStartDateIsBeforeSecondPeriodEndDate.and(firstPeriodEndDateIsAfterSecondPeriodEndDate)
}

fun timesOverlap(firstPeriod: Pair<LocalTime, LocalTime>, secondPeriod: Pair<LocalTime, LocalTime>): Boolean {
    val today = LocalDate.now()
    val tomorrow = LocalDate.now().plusDays(1)
    val firstPeriodDate = Pair(
        firstPeriod.first.atDate(today),
        if (firstPeriod.second.isBefore(firstPeriod.first)) firstPeriod.second.atDate(tomorrow)
        else firstPeriod.second.atDate(today)
    )
    val secondPeriodDate = Pair(
        secondPeriod.first.atDate(today),
        if (secondPeriod.second.isBefore(secondPeriod.first)) secondPeriod.second.atDate(tomorrow)
        else secondPeriod.second.atDate(today)
    )
    return periodsOverlap(firstPeriodDate, secondPeriodDate)
}

fun timeDiff(startTime: LocalTime, endTime: LocalTime): Long {
    val today = LocalDate.now()
    val tomorrow = LocalDate.now().plusDays(1)

    val startDateTime = startTime.atDate(today)
    val endDateTime = if (endTime.isBefore(startTime)) endTime.atDate(tomorrow)
    else endTime.atDate(today)


    return ChronoUnit.MINUTES.between(startDateTime, endDateTime)
}

fun daysDiff(startDate: LocalDate, endDate: LocalDate): Long =
    ChronoUnit.DAYS.between(startDate, endDate)

fun ZonedDateTime.toZoomDateStringFormat(): String {
    val dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")
    return this.format(dateFormat)
}

fun WeekDay.nextDay(): WeekDay =
    when (this) {
        WeekDay.MONDAY -> WeekDay.TUESDAY
        WeekDay.TUESDAY -> WeekDay.WEDNESDAY
        WeekDay.WEDNESDAY -> WeekDay.THURSDAY
        WeekDay.THURSDAY -> WeekDay.FRIDAY
        WeekDay.FRIDAY -> WeekDay.SATURDAY
        WeekDay.SATURDAY -> WeekDay.SUNDAY
        WeekDay.SUNDAY -> WeekDay.MONDAY
        else -> this
    }

fun WeekDay.previousDay(): WeekDay =
    when (this) {
        WeekDay.MONDAY -> WeekDay.SUNDAY
        WeekDay.TUESDAY -> WeekDay.MONDAY
        WeekDay.WEDNESDAY -> WeekDay.TUESDAY
        WeekDay.THURSDAY -> WeekDay.WEDNESDAY
        WeekDay.FRIDAY -> WeekDay.THURSDAY
        WeekDay.SATURDAY -> WeekDay.FRIDAY
        WeekDay.SUNDAY -> WeekDay.SATURDAY
        else -> this
    }

fun DayOfWeek.isWorkingDay(): Boolean =
    when (this) {
        MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY -> true
        else -> false
    }

fun beginningOfTheMonthWithSaoPauloTimezone(localDateTimeNow: LocalDateTime) =
    localDateTimeNow.toSaoPauloTimeZone().toLocalDate().atBeginningOfTheMonth()

fun endOfTheMonthWithSaoPauloTimezone(localDateTimeNow: LocalDateTime) =
    localDateTimeNow.toSaoPauloTimeZone().toLocalDate().atEndOfTheMonth()

fun LocalDateTime.toDate(): Date = Date.from(atZone(ZoneOffset.systemDefault()).toInstant())

fun OffsetDateTime.toBrazilianDateFormat(): String = format(formatter)
fun OffsetDateTime.toBrazilianDateTimeFormat(): String = format(baseFormatter("dd/MM/yyyy HH:mm:ss"))
