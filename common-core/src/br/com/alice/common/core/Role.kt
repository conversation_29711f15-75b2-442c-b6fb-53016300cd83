package br.com.alice.common.core

import br.com.alice.common.core.StaffType.COMMUNITY_SPECIALIST
import br.com.alice.common.core.StaffType.EXTERNAL_PAID_HEALTH_PROFESSIONAL
import br.com.alice.common.core.StaffType.GENERAL_SPECIALTY
import br.com.alice.common.core.StaffType.HEALTH_PROFESSIONAL
import br.com.alice.common.core.StaffType.PARTNER_HEALTH_PROFESSIONAL
import br.com.alice.common.core.StaffType.PITAYA
import br.com.alice.common.core.StaffType.TALK_CIRCLE

enum class StaffType(val description: String) {
    PITAYA("Pitaya"),
    COMMUNITY_SPECIALIST("Especialista da Comunidade"),
    TALK_CIRCLE("Roda de Conversa"),
    GENERAL_SPECIALTY("Especialidade Geral"),
    HEALTH_ADMINISTRATIVE("Administrador de Unidade de Saúde"),
    PARTNER_HEALTH_PROFESSIONAL("Profissional de Saúde Parceiro"),
    HEALTH_PROFESSIONAL("Profissional de Saúde (Pitaya)"),
    EXTERNAL_PAID_HEALTH_PROFESSIONAL("Profissional de saúde externo remunerado"),
}

enum class Role(val description: String, vararg val types: StaffType) {
    B2B_OPS("B2B - Ops", PITAYA),
    CARE_COORD_NURSE("Coordenação de Cuidados", HEALTH_PROFESSIONAL),
    CHIEF_DIGITAL_CARE_NURSE("Enfermeiro(a) Chefe Alice Agora", HEALTH_PROFESSIONAL),
    CHIEF_DIGITAL_CARE_PHYSICIAN("Médico(a) Chefe Alice Agora", HEALTH_PROFESSIONAL),
    CHIEF_NAVIGATOR("TL de Naveg", PITAYA),
    CHIEF_NAVIGATOR_OPS("TL de Naveg de BLC", PITAYA),
    CHIEF_PHYSICIAN("Médico(a) Chefe", HEALTH_PROFESSIONAL, PARTNER_HEALTH_PROFESSIONAL),
    CHIEF_RISK("Enfermeiro(a) de Risk - Chefe", HEALTH_PROFESSIONAL),
    COMMUNITY("Especialista", COMMUNITY_SPECIALIST),
    CX_OPS("Ops - Customer Experience", PITAYA),
    DE_IDENTIFIED_HI_VIEWER("HI de-identificada", PITAYA),
    DIGITAL_CARE_NURSE("Enfermeiro(a) Alice Agora", HEALTH_PROFESSIONAL),
    DIGITAL_CARE_PHYSICIAN("Médico(a) Alice Agora", HEALTH_PROFESSIONAL),
    DIGITAL_SCREENING_NURSE("Enfermeiro(a) (E1) de Acolhimento Digital", HEALTH_PROFESSIONAL),
    FIN_OPS("Fin Ops", PITAYA),
    HEALTHCARE_TEAM_NURSE("Enfermeiro(a) Time de Saúde", HEALTH_PROFESSIONAL, GENERAL_SPECIALTY),
    HEALTH_ADMINISTRATIVE("Profissional administrativo da unidade de Saúde", StaffType.HEALTH_ADMINISTRATIVE),
    HEALTH_ADMINISTRATIVE_ELIGIBILITY(
        "Profissional administrativo de elegibilidade da unidade de Saúde",
        StaffType.HEALTH_ADMINISTRATIVE
    ),
    HEALTH_COMMUNITY("Health Community", COMMUNITY_SPECIALIST),
    // This role is used by the Enfermeiro(a) de Risk team. We can change the enum value and update the Staffs to reflect this.
    HEALTH_DECLARATION_NURSE("Enfermeiro(a) de Risk", HEALTH_PROFESSIONAL, PARTNER_HEALTH_PROFESSIONAL),
    HEALTH_OPS("Health Ops", PITAYA),
    HEALTH_OPS_LEAD("Health Ops Lead", PITAYA),
    HEALTH_OPS_MULTI("Health Ops - Operations Multi", PITAYA),
    INSURANCE_OPS_COMMUNITY_SUCCESS("Insurance Ops - Community Success", PITAYA),
    INSURANCE_OPS_HEALTH_INSTITUTION_OPS("Insurance Ops - Health Institution Ops", PITAYA),
    MANAGER_NUTRITIONIST("Nutricionista Gestor(a)", HEALTH_PROFESSIONAL),
    MANAGER_PHYSICAL_EDUCATOR("Preparador(a) Físico(a) Gestor(a)", HEALTH_PROFESSIONAL),
    MANAGER_PHYSICIAN("Médico(a) Gestor(a)", HEALTH_PROFESSIONAL, PARTNER_HEALTH_PROFESSIONAL, GENERAL_SPECIALTY),
    MANAGER_PSYCHOLOGIST("Psicólogo(a) Gestor(a)", HEALTH_PROFESSIONAL),
    MED_EX("MedEx", PITAYA),
    MED_RISK("Médico de Regulação / Auditoria / Perícia", HEALTH_PROFESSIONAL, PARTNER_HEALTH_PROFESSIONAL),
    MEMBER_OPS("Member Ops", PITAYA),
    NAVIGATOR("Naveg", PITAYA),
    NAVIGATOR_OPS("Naveg de BLC", PITAYA),
    NUTRITIONIST("Nutricionista", HEALTH_PROFESSIONAL, PARTNER_HEALTH_PROFESSIONAL, TALK_CIRCLE),
    OBSTETRICIAN("Obstetriz", HEALTH_PROFESSIONAL, PARTNER_HEALTH_PROFESSIONAL),
    ON_SITE_NURSE("Enfermeiro(a) On Site", HEALTH_PROFESSIONAL, PARTNER_HEALTH_PROFESSIONAL),
    ON_SITE_PHYSICIAN("Médico(a) On Site", HEALTH_PROFESSIONAL, PARTNER_HEALTH_PROFESSIONAL),
    OPS("Ops", PITAYA),
    PHYSICAL_EDUCATOR("Preparador(a) Físico(a)", HEALTH_PROFESSIONAL, PARTNER_HEALTH_PROFESSIONAL, TALK_CIRCLE),
    PRODUCT_TECH("Produto/Tecnologia", PITAYA),
    PRODUCT_TECH_STAFF_EDITOR("Produto/Tecnologia - Staff Editor", PITAYA),
    PRODUCT_TECH_HEALTH("Produto/Tecnologia - Health", PITAYA),
    PRODUCT_TECH_HEALTH_STAFF_EDITOR("Produto/Tecnologia - Health Staff Editor", PITAYA),
    PSYCHOLOGIST("Psicólogo(a)", HEALTH_PROFESSIONAL, PARTNER_HEALTH_PROFESSIONAL, TALK_CIRCLE),
    QUALITY_NURSE("Enfermeiro(a) de Qualidade", PITAYA, HEALTH_PROFESSIONAL),
    RISK_INTERMITTENT_NURSE("Enfermeiro(a) de Risk Intermitente", PARTNER_HEALTH_PROFESSIONAL),
    // This role is used by the Enfermeiro(a) de Regulação team. We can change the enum value and update the Staffs to reflect this.
    RISK_NURSE("Enfermeiro(a) de Regulação", HEALTH_PROFESSIONAL, PARTNER_HEALTH_PROFESSIONAL),
    SCREENING_NURSE("Enfermeiro(a) de triagem", HEALTH_PROFESSIONAL, PARTNER_HEALTH_PROFESSIONAL),
    TECHNIQUE_NURSE("Health - Técnica(o) de Enfermagem Casa Alice", HEALTH_PROFESSIONAL),
    VIRTUAL_CLINIC_PEDIATRIC_PHYSICIAN(
        "Médico(a) PA Virtual infantil",
        HEALTH_PROFESSIONAL,
        PARTNER_HEALTH_PROFESSIONAL
    ),
    VIRTUAL_CLINIC_PHYSICIAN("Médico(a) PA Virtual", HEALTH_PROFESSIONAL, PARTNER_HEALTH_PROFESSIONAL),
    ANESTHETIST("Anestesista", EXTERNAL_PAID_HEALTH_PROFESSIONAL),

    DEPRECATED("Descontinuado", PITAYA);

    companion object {
        fun healthProfessionalStaffs(): List<Role> = values().filter {
            listOf(
                CARE_COORD_NURSE,
                CHIEF_PHYSICIAN,
                CHIEF_DIGITAL_CARE_NURSE,
                CHIEF_DIGITAL_CARE_PHYSICIAN,
                MED_RISK,
                DIGITAL_CARE_NURSE,
                DIGITAL_CARE_PHYSICIAN,
                DIGITAL_SCREENING_NURSE,
                HEALTHCARE_TEAM_NURSE,
                HEALTH_DECLARATION_NURSE,
                RISK_NURSE,
                RISK_INTERMITTENT_NURSE,
                TECHNIQUE_NURSE,
                NUTRITIONIST,
                PHYSICAL_EDUCATOR,
                PSYCHOLOGIST,
                ON_SITE_PHYSICIAN,
                ON_SITE_NURSE,
                MANAGER_PHYSICIAN,
                COMMUNITY,
                OBSTETRICIAN,
                SCREENING_NURSE,
                MANAGER_NUTRITIONIST,
                MANAGER_PSYCHOLOGIST,
                MANAGER_PHYSICAL_EDUCATOR,
                VIRTUAL_CLINIC_PHYSICIAN,
                VIRTUAL_CLINIC_PEDIATRIC_PHYSICIAN
            ).contains(it)
        }

        fun physicianRoles(): List<Role> =
            listOf(
                CHIEF_PHYSICIAN,
                CHIEF_DIGITAL_CARE_PHYSICIAN,
                DIGITAL_CARE_PHYSICIAN,
                MANAGER_PHYSICIAN,
                MED_RISK,
                ON_SITE_PHYSICIAN,
                VIRTUAL_CLINIC_PHYSICIAN,
                VIRTUAL_CLINIC_PEDIATRIC_PHYSICIAN
            )

    }
}
