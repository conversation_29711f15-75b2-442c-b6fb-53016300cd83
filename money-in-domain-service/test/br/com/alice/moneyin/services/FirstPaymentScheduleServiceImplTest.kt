package br.com.alice.moneyin.services

import br.com.alice.business.events.FirstPaymentScheduleCreatedEvent
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.returns
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.services.FirstPaymentScheduleModelDataService
import br.com.alice.moneyin.client.FirstPaymentScheduleService
import br.com.alice.moneyin.converters.toModel
import br.com.alice.moneyin.converters.toTransport
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class FirstPaymentScheduleServiceImplTest : MockedTestHelper() {
    private val dataService: FirstPaymentScheduleModelDataService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val service = FirstPaymentScheduleServiceImpl(dataService, kafkaProducerService)

    @Test
    fun `#add should add new FirstPaymentSchedule`() = runBlocking {
        val company = TestModelFactory.buildCompany()
        val companyContract = TestModelFactory.buildCompanyContract()
        val companySubContract = TestModelFactory.buildCompanySubContract()
        val preActivationPayment = TestModelFactory.buildPreActivationPayment()

        val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule()
        val firstPaymentScheduleModel = firstPaymentSchedule.toModel()

        coEvery { dataService.add(match { it.companyId == company.id && it.companySubcontractId == companySubContract.id && it.preActivationPaymentId == preActivationPayment.id }) } returns firstPaymentScheduleModel
        coEvery {
            kafkaProducerService.produce(match<FirstPaymentScheduleCreatedEvent> {
                it.payload.firstPaymentScheduleCreated.preActivationPayment == preActivationPayment
                        && it.payload.firstPaymentScheduleCreated.company == company
                        && it.payload.firstPaymentScheduleCreated.companyContract == companyContract
                        && it.payload.firstPaymentScheduleCreated.companySubcontract == companySubContract
            })
        } returns mockk()

        val result = service.create(
            FirstPaymentScheduleService.CreatePayload(
                company,
                companyContract,
                companySubContract,
                preActivationPayment,
            )
        )

        ResultAssert.assertThat(result).isSuccessWithData(firstPaymentSchedule)

        coVerifyOnce {
            dataService.add(any())
            kafkaProducerService.produce(any<FirstPaymentScheduleCreatedEvent>())
        }
    }

    @Test
    fun `#get should retrieve FirstPaymentSchedule`() = runBlocking {
        val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule()
        val firstPaymentScheduleModel = firstPaymentSchedule.toModel()

        coEvery { dataService.get(firstPaymentScheduleModel.id) } returns firstPaymentScheduleModel

        val result = service.get(firstPaymentScheduleModel.id)

        ResultAssert.assertThat(result).isSuccessWithData(firstPaymentSchedule)

        coVerifyOnce { dataService.get(any()) }
    }

    @Test
    fun `#update should retrieve FirstPaymentSchedule`() = runBlocking {
        val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule()
        val firstPaymentScheduleModel = firstPaymentSchedule.toModel()
        val firstPaymentScheduleModelUpdated =
            firstPaymentScheduleModel.copy(memberInvoiceGroupId = RangeUUID.generate())
        val firstPaymentScheduleUpdated = firstPaymentScheduleModelUpdated.toTransport()

        coEvery { dataService.update(firstPaymentScheduleModelUpdated) } returns firstPaymentScheduleModelUpdated

        val result = service.update(firstPaymentScheduleUpdated)

        ResultAssert.assertThat(result).isSuccessWithData(firstPaymentScheduleUpdated)

        coVerifyOnce { dataService.update(any()) }
    }

    @Test
    fun `#findByCompanyIdSubContractIdAndPreActivationPaymentId should retrieve FirstPaymentSchedule`() = runBlocking {
        val company = TestModelFactory.buildCompany()
        val companySubcontract = TestModelFactory.buildCompanySubContract()
        val preActivationPayment = TestModelFactory.buildPreActivationPayment()

        val firstPaymentSchedule = TestModelFactory.buildFirstPaymentSchedule(
            companyId = company.id,
            companySubcontractId = companySubcontract.id,
            preActivationPaymentId = preActivationPayment.id
        )

        val firstPaymentScheduleModel = firstPaymentSchedule.toModel()

        coEvery {
            dataService.findOne(
                queryEq {
                    where {
                        this.companyId.eq(company.id) and
                                this.companySubContractId.eq(companySubcontract.id) and
                                this.preActivationPaymentId.eq(preActivationPayment.id)
                    }
                }
            )
        } returns firstPaymentScheduleModel

        val result = service.findByCompanyIdSubContractIdAndPreActivationPaymentId(
            companyId = company.id,
            companySubcontractId = companySubcontract.id,
            preActivationPaymentId = preActivationPayment.id
        )

        ResultAssert.assertThat(result).isSuccessWithData(firstPaymentSchedule)

        coVerifyOnce { dataService.findOne(any()) }
    }
}
