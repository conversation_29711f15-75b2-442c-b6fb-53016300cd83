package br.com.alice.moneyin.helpers

import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.core.extensions.toMoneyStringWithoutCurrencySign
import br.com.alice.data.layer.models.BeneficiaryInfo
import br.com.alice.common.BeneficiaryType
import br.com.alice.data.layer.models.BillingAccountableParty
import br.com.alice.data.layer.models.InvoiceItemOperation
import br.com.alice.data.layer.models.InvoiceItemResponse
import br.com.alice.data.layer.models.MemberInvoiceGroupInfoResponse
import br.com.alice.data.layer.models.MemberInvoiceInfo
import br.com.alice.moneyin.model.AdditionalItem
import br.com.alice.moneyin.model.MembersReport
import br.com.alice.moneyin.model.XlsxFields
import br.com.alice.moneyin.model.XlsxLocations
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.usermodel.Sheet
import java.time.format.TextStyle
import java.util.Locale

private const val invoiceType = "PRODUTO/PLANO (MENSALIDADE)"

object MemberInvoiceGroupXlsxHelpers {
    private const val SECTION_HEADER_SIZE = 3 //number of rows that make up a section header
    private const val FIRST_ADDITIONAL_ROW = 13
    private const val FIRST_MEMBER_ROW = FIRST_ADDITIONAL_ROW + SECTION_HEADER_SIZE + 2 // spaces

    fun getValueByLocation(location: XlsxLocations, xlsxFields: XlsxFields): String {
        return when (location) {
            XlsxLocations.COMPANY_NAME -> xlsxFields.companyName
            XlsxLocations.CNPJ -> xlsxFields.cnpj
            XlsxLocations.REFERENCE_DATE -> xlsxFields.referenceMonth
            XlsxLocations.NUMBER_OF_MEMBERS -> xlsxFields.numberOfMembers.toString()
            XlsxLocations.ISSUED_MONTH_HEADER -> xlsxFields.issuedMonth
            XlsxLocations.ISSUED_MONTH_RESUME -> xlsxFields.issuedMonth
            XlsxLocations.DUE_DATE -> xlsxFields.dueDate
            XlsxLocations.AMOUNT -> xlsxFields.totalAmount
        }
    }

    fun writeMemberReportsRows(sheet: Sheet, membersReport: List<MembersReport>, additionalRows: Int) {
        var rowIndex = FIRST_MEMBER_ROW + additionalRows
        var rowNumber = 1

        for (report in membersReport) {
            val dataRow = sheet.getRow(rowIndex) ?: sheet.createRow(rowIndex)
            addDataToRow(dataRow, 1, (rowNumber++).toString())
            addDataToRow(dataRow, 2, report.invoiceType)
            addDataToRow(dataRow, 3, report.memberName)
            addDataToRow(dataRow, 4, report.nationalId)
            addDataToRow(dataRow, 5, report.nationalIdHolder ?: "")
            addDataToRow(dataRow, 6, report.beneficiaryType)
            addDataToRow(dataRow, 7, report.productTitle)
            addDataToRow(dataRow, 8, report.productDisplayName ?: "")
            addDataToRow(dataRow, 9, report.memberStatus)
            addDataToRow(dataRow, 10, report.amount)
            addDataToRow(dataRow, 11, report.referenceDate)

            rowIndex++
        }
    }

    fun writeAdditionalReportRows(sheet: Sheet, additionalReport: List<AdditionalItem>?): Int {
        var rowIndex = FIRST_ADDITIONAL_ROW
        var rowNumber = 0

        if (additionalReport != null) {
            for (report in additionalReport) {
                sheet.shiftRows(rowIndex, sheet.lastRowNum, 1)
                val dataRow = sheet.createRow(rowIndex)
                addDataToRow(dataRow, 1, (++rowNumber).toString())
                addDataToRow(dataRow, 2, report.invoiceType)
                addDataToRow(dataRow, 3, report.description ?: "")
                addDataToRow(dataRow, 4, report.amount)

                rowIndex++
            }
        }

        return rowNumber
    }

    private fun addDataToRow(row: Row, columnIndex: Int, data: String) {
        val cell = row.createCell(columnIndex)
        cell.setCellValue(data)
    }

    fun parseMemberInvoiceGroupDetails(
        billingAccountableParty: BillingAccountableParty,
        memberInvoiceGroupInfo: MemberInvoiceGroupInfoResponse
    ): XlsxFields {
        val referenceMonth = "${
            memberInvoiceGroupInfo.referenceDate.month.getDisplayName(
                TextStyle.FULL,
                Locale("pt", "BR")
            )
        }/" +
                "${memberInvoiceGroupInfo.referenceDate.year}"

        val issuedMonth =
            "${memberInvoiceGroupInfo.dueDate.month.getDisplayName(TextStyle.FULL, Locale("pt", "BR"))}/" +
                    "${memberInvoiceGroupInfo.dueDate.year}"

        return XlsxFields(
            companyName = billingAccountableParty.fullName,
            cnpj = billingAccountableParty.nationalId,
            referenceMonth = referenceMonth,
            numberOfMembers = memberInvoiceGroupInfo.memberInvoicesInfo.size,
            issuedMonth = issuedMonth,
            dueDate = memberInvoiceGroupInfo.dueDate.toBrazilianDateFormat(),
            totalAmount = memberInvoiceGroupInfo.totalAmount.toMoneyStringWithoutCurrencySign(),
            membersReport = parseMembersReport(memberInvoiceGroupInfo.memberInvoicesInfo),
            additionalReport = parseAdditionalReport(memberInvoiceGroupInfo.globalItems)
        )
    }

    private fun parseMembersReport(memberInvoicesInfo: List<MemberInvoiceInfo>): List<MembersReport> {
        return memberInvoicesInfo.flatMap { memberInvoiceInfo ->
            val beneficiaryInfo = memberInvoiceInfo.beneficiaryInfo
            val invoiceBreakdownItems = memberInvoiceInfo.invoiceBreakdown
            val memberReport = MembersReport(
                invoiceType = invoiceType,
                product = beneficiaryInfo.type.toString(),
                memberName = "${beneficiaryInfo.firstName} ${beneficiaryInfo.lastName}",
                nationalId = beneficiaryInfo.nationalId,
                nationalIdHolder = beneficiaryInfo.nationalIdHolder,
                beneficiaryType = parseBeneficiaryType(beneficiaryInfo),
                productTitle = beneficiaryInfo.productTitle,
                productDisplayName = beneficiaryInfo.productDisplayName,
                memberStatus = if (beneficiaryInfo.isMemberActive) "ATIVO" else "INATIVO",
                amount = memberInvoiceInfo.amount.toMoneyStringWithoutCurrencySign(),
                referenceDate = memberInvoiceInfo.referenceDate.toBrazilianDateFormat()
            )

            invoiceBreakdownItems?.map { breakdownItem ->
                memberReport.copy(
                    invoiceType = breakdownItem.title,
                    amount = breakdownItem.amount.toMoneyStringWithoutCurrencySign(),
                )
            }
                ?: listOf(memberReport)
        }
    }

    private fun parseBeneficiaryType(beneficiaryInfo: BeneficiaryInfo) =
        when (beneficiaryInfo.type) {
            BeneficiaryType.EMPLOYEE -> "FUNCIONARIO"
            BeneficiaryType.DEPENDENT -> "DEPENDENTE"
            BeneficiaryType.UNDEFINED -> "NAO DEFINIDO"
        }

    private fun parseAdditionalReport(globalItems: List<InvoiceItemResponse>?): List<AdditionalItem>? {
        return globalItems?.map { globalItem ->
            AdditionalItem(
                invoiceType = globalItem.type.description,
                description = globalItem.notes,
                amount = globalItem.displayValue?.toMoneyStringWithoutCurrencySign() ?: valueFallback(globalItem)
            )
        }
    }

    private fun valueFallback(globalItem: InvoiceItemResponse): String =
        (if (globalItem.operation == InvoiceItemOperation.DISCOUNT) "-" else "") + globalItem.value.toMoneyStringWithoutCurrencySign()
}
