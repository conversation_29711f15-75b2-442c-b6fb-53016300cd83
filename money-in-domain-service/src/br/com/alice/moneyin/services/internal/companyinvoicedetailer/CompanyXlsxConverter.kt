package br.com.alice.moneyin.services.internal.companyinvoicedetailer

import br.com.alice.common.BeneficiaryType
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.core.extensions.toMoneyStringWithoutCurrencySign
import br.com.alice.data.layer.models.InvoiceItem
import br.com.alice.data.layer.models.InvoiceItemOperation
import br.com.alice.moneyin.models.CompanyInvoiceResponse
import br.com.alice.moneyin.models.MemberInvoiceBeneficiaryDetail
import br.com.alice.moneyin.models.MemberInvoiceDetail
import java.time.format.TextStyle
import java.util.Locale

object CompanyXlsxConverter {

    fun getReferenceMonth(companyInvoice: CompanyInvoiceResponse) = "${
        companyInvoice.referenceDate.month.getDisplayName(
            TextStyle.FULL,
            Locale("pt", "BR")
        )
    }/${companyInvoice.referenceDate.year}"

    fun getIssuedMonth(companyInvoice: CompanyInvoiceResponse) =
        "${companyInvoice.dueDate.month.getDisplayName(TextStyle.FULL, Locale("pt", "BR"))}/" +
                "${companyInvoice.dueDate.year}"


    private const val invoiceType = "PRODUTO/PLANO (MENSALIDADE)"

    private fun parseBeneficiaryType(beneficiaryInfo: MemberInvoiceBeneficiaryDetail) =
        when (beneficiaryInfo.type) {
            BeneficiaryType.EMPLOYEE -> "FUNCIONARIO"
            BeneficiaryType.DEPENDENT -> "DEPENDENTE"
            BeneficiaryType.UNDEFINED -> "NAO DEFINIDO"
        }

    fun List<InvoiceItem>.toXlsxAdditionalItem() = map { item ->
        XlsxAdditionalItem(
            invoiceType = item.type.description,
            description = item.notes,
            amount = item.resolvedValue?.toMoneyStringWithoutCurrencySign() ?: valueFallback(
                item
            )
        )
    }

    private fun valueFallback(globalItem: InvoiceItem): String =
        (if (globalItem.operation == InvoiceItemOperation.DISCOUNT) "-" else "") + globalItem.value.toMoneyStringWithoutCurrencySign()

    fun List<MemberInvoiceDetail>.toMemberReports() = map { memberInvoiceDetail ->
        val beneficiaryDet = memberInvoiceDetail.beneficiary
        val invoiceBreakdownItems = memberInvoiceDetail.invoiceBreakdownItems

        val xlsxMemberReport = XlsxMemberReport(
            invoiceType = invoiceType,
            product = beneficiaryDet.type.toString(),
            memberName = "${beneficiaryDet.firstName} ${beneficiaryDet.lastName}",
            nationalId = beneficiaryDet.nationalId,
            nationalIdHolder = beneficiaryDet.nationalIdHolder,
            beneficiaryType = parseBeneficiaryType(beneficiaryDet),
            productTitle = beneficiaryDet.productTitle,
            productDisplayName = beneficiaryDet.productDisplayName,
            memberStatus = if (beneficiaryDet.isMemberActive) "ATIVO" else "INATIVO",
            amount = memberInvoiceDetail.amount.toMoneyStringWithoutCurrencySign(),
            referenceDate = memberInvoiceDetail.referenceDate.toBrazilianDateFormat()
        )

        invoiceBreakdownItems?.map { breakdownItem ->
            xlsxMemberReport.copy(
                invoiceType = breakdownItem.title,
                amount = breakdownItem.amount.toMoneyStringWithoutCurrencySign(),
            )
        }
            ?: listOf(xlsxMemberReport)
    }.flatten()
}
