package br.com.alice.moneyin.services

import br.com.alice.business.events.FirstPaymentScheduleCreated
import br.com.alice.business.events.FirstPaymentScheduleCreatedEvent
import br.com.alice.common.core.extensions.toNextWorkingDayExcludingWeekends
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.models.FirstPaymentSchedule
import br.com.alice.data.layer.services.FirstPaymentScheduleModelDataService
import br.com.alice.moneyin.client.FirstPaymentScheduleService
import br.com.alice.moneyin.converters.toModel
import br.com.alice.moneyin.converters.toTransport
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import java.time.LocalDate
import java.util.UUID

class FirstPaymentScheduleServiceImpl(
    private val firstPaymentScheduleModelDataService: FirstPaymentScheduleModelDataService,
    private val kafkaProducerService: KafkaProducerService,
) : FirstPaymentScheduleService {

    override suspend fun create(payload: FirstPaymentScheduleService.CreatePayload): Result<FirstPaymentSchedule, Throwable> {
        val company = payload.company
        val companyContract = payload.companyContract
        val companySubContract = payload.companySubContract
        val preActivationPayment = payload.preActivationPayment

        logger.info(
            "FirstPaymentScheduleServiceImpl::create -",
            "company_id" to company.id,
            "company_contract_id" to companyContract.id,
            "company_subcontract_id" to companySubContract.id,
            "pre_activation_payment_id" to preActivationPayment.id,
        )

        val firstPaymentSchedule = FirstPaymentSchedule(
            preActivationPaymentId = preActivationPayment.id,
            companyId = company.id,
            companySubcontractId = companySubContract.id,
            scheduledDate = LocalDate.now().toNextWorkingDayExcludingWeekends()
        )

        return firstPaymentScheduleModelDataService.add(firstPaymentSchedule.toModel())
            .then {
                logger.info(
                    "FirstPaymentSchedule created successfully",
                    "first_payment_schedule_id" to it.id,
                    "pre_activation_payment_id" to it.preActivationPaymentId,
                    "company_id" to it.companyId,
                    "company_subcontract_id" to it.companySubcontractId,
                    "scheduled_date" to it.scheduledDate
                )

                kafkaProducerService.produce(
                    FirstPaymentScheduleCreatedEvent(
                        FirstPaymentScheduleCreated(
                            companySubcontract = companySubContract,
                            company = company,
                            preActivationPayment = preActivationPayment,
                            companyContract = companyContract
                        )
                    )
                )
            }
            .map { it.toTransport() }
    }

    override suspend fun get(id: UUID): Result<FirstPaymentSchedule, Throwable> =
        firstPaymentScheduleModelDataService.get(id).map { it.toTransport() }

    override suspend fun update(firstPaymentSchedule: FirstPaymentSchedule): Result<FirstPaymentSchedule, Throwable> =
        firstPaymentScheduleModelDataService.update(firstPaymentSchedule.toModel()).map { it.toTransport() }

    override suspend fun findByCompanyIdSubContractIdAndPreActivationPaymentId(
        companyId: UUID,
        companySubcontractId: UUID,
        preActivationPaymentId: UUID
    ): Result<FirstPaymentSchedule, Throwable> =
        firstPaymentScheduleModelDataService.findOne {
            where {
                this.companyId.eq(companyId) and this.companySubContractId.eq(companySubcontractId) and this.preActivationPaymentId.eq(
                    preActivationPaymentId
                )
            }
        }.map { it.toTransport() }
}
