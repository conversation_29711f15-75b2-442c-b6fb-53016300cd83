package br.com.alice.moneyin.converters

import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.FirstPaymentSchedule
import br.com.alice.data.layer.models.FirstPaymentScheduleModel
import br.com.alice.data.layer.models.FirstPaymentScheduleStatus
import br.com.alice.data.layer.models.FirstPaymentScheduleStatusHistoryEntry
import br.com.alice.data.layer.models.FirstPaymentScheduleStatusHistoryEntryModel

object FirstPaymentScheduleConverter : Converter<FirstPaymentScheduleModel, FirstPaymentSchedule>(
    FirstPaymentScheduleModel::class,
    FirstPaymentSchedule::class,
) {
    fun convert(source: FirstPaymentScheduleModel) = FirstPaymentScheduleConverter.convert(
        source,
        map(FirstPaymentSchedule::statusHistory) from (source.statusHistory.map {
            FirstPaymentScheduleStatusHistoryEntry(
                status = FirstPaymentScheduleStatus.valueOf(it.status.name),
                createdAt = it.createdAt
            )
        }
                ),
    )


    fun unconvert(source: FirstPaymentSchedule) = FirstPaymentScheduleConverter.unconvert(
        source,
        map(FirstPaymentScheduleModel::statusHistory) from (source.statusHistory.map {
            FirstPaymentScheduleStatusHistoryEntryModel(
                status = FirstPaymentScheduleStatus.valueOf(it.status.name),
                createdAt = it.createdAt
            )
        }),
    )
}

fun FirstPaymentSchedule.toModel() = FirstPaymentScheduleConverter.unconvert(this)
fun FirstPaymentScheduleModel.toTransport() = FirstPaymentScheduleConverter.convert(this)
