package br.com.alice.common.service.serialization

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toLocalDateTime
import br.com.alice.common.serialization.DurationAdapter
import br.com.alice.common.serialization.LocalDateAdapter
import br.com.alice.common.serialization.LocalDateTimeAdapter
import br.com.alice.common.serialization.LocalDateTimeAtEpochAdapter
import br.com.alice.common.serialization.LocalTimeAdapter
import br.com.alice.common.serialization.NullableTypeAdapterFactory
import br.com.alice.common.serialization.OPAExclusionStrategy
import br.com.alice.common.serialization.OffsetDateTimeAdapter
import br.com.alice.common.serialization.PersonIdTypeAdapter
import br.com.alice.common.serialization.ZonedLocalDateTimeAdapter
import br.com.alice.common.serialization.gsonBFFBuilder
import br.com.alice.common.serialization.gsonBuilder
import br.com.alice.common.serialization.gsonIdentityBuilder
import com.google.cloud.Timestamp
import com.google.gson.FieldNamingPolicy
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializer
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializer
import io.gsonfire.GsonFireBuilder
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.serialization.gson.gson
import io.ktor.server.plugins.contentnegotiation.ContentNegotiationConfig
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.Date

val isoDateWithoutFieldPolicyGson: Gson = GsonBuilder()
    .registerDateApiAdapter()
    .create()

val isoDateGson: Gson = gsonBuilder()
    .registerTimestampDeserializerAdapter()
    .registerTimestampTimeSerializerAdapter()
    .create()

val gsonBFF: Gson = gsonBFFBuilder()
    .registerTypeAdapterFactory(NullableTypeAdapterFactory())
    .create()

val gsonIdentity: Gson = gsonIdentityBuilder()
    .registerDateApiAdapter()
    .registerTypeAdapterFactory(NullableTypeAdapterFactory())
    .create()

//assuming we won't need to send dates and times for authorization evaluation
val gsonOPA = GsonFireBuilder().enableExposeMethodResult()
    .createGsonBuilder()
    .setFieldNamingPolicy(FieldNamingPolicy.IDENTITY)
    .setExclusionStrategies(OPAExclusionStrategy())
    .registerTypeAdapter(PersonId::class.java, PersonIdTypeAdapter())
    .create()

val gsonCompleteSerializer: Gson = gsonBuilder()
    .registerTypeAdapterFactory(NullableTypeAdapterFactory())
    .registerTimestampDeserializerAdapter()
    .registerTimestampTimeSerializerAdapter()
    .registerOffsetDateTimeAdapter()
    .create()

fun GsonBuilder.registerLocalDateTimeAtEpochAdapter(): GsonBuilder =
    this.registerTypeAdapter(LocalDateTime::class.java, LocalDateTimeAtEpochAdapter())

fun GsonBuilder.registerLocalDateTimeAdapter(): GsonBuilder =
    this.registerTypeAdapter(LocalDateTime::class.java, LocalDateTimeAdapter())

fun GsonBuilder.registerLocalDateAdapter(): GsonBuilder =
    this.registerTypeAdapter(LocalDate::class.java, LocalDateAdapter())

fun GsonBuilder.registerLocalTimeAdapter(): GsonBuilder =
    this.registerTypeAdapter(LocalTime::class.java, LocalTimeAdapter())

fun GsonBuilder.registerOffsetDateTimeAdapter(): GsonBuilder =
    this.registerTypeAdapter(OffsetDateTime::class.java, OffsetDateTimeAdapter())

fun GsonBuilder.registerZonedLocalDateTimeAdapter(): GsonBuilder =
    this.registerTypeAdapter(LocalDateTime::class.java, ZonedLocalDateTimeAdapter())

fun GsonBuilder.registerDurationAdapter(): GsonBuilder =
    this.registerTypeAdapter(Duration::class.java, DurationAdapter())

fun GsonBuilder.registerTimestampDeserializerAdapter(): GsonBuilder =
    this.registerTypeAdapter(Timestamp::class.java, JsonDeserializer { json, _, _ ->
        try {
            val localDateTime = json.asJsonPrimitive.asString.replace("(Z)\$".toRegex(), "").toLocalDateTime()
            Timestamp.of(Date.from(localDateTime.toInstant(ZoneOffset.UTC)))
        } catch (ex: Exception) {
            val jsonObject = json.asJsonObject
            val seconds = jsonObject.get("seconds").asLong
            val nanos = jsonObject.get("nanos").asInt

            Timestamp.ofTimeSecondsAndNanos(seconds, nanos)
        }
    } as JsonDeserializer<Timestamp?>?)

fun GsonBuilder.registerTimestampTimeSerializerAdapter(): GsonBuilder =
    this.registerTypeAdapter(Timestamp::class.java, JsonSerializer<Timestamp?> { date, _, _ ->
        JsonPrimitive(Instant.parse(date.toString()).atOffset(ZoneOffset.UTC).toString())
    } as JsonSerializer<Timestamp?>?)

fun GsonBuilder.registerDateApiAdapter(): GsonBuilder =
    this.registerLocalDateTimeAdapter()
        .registerLocalDateAdapter()
        .registerLocalTimeAdapter()
        .registerTimestampDeserializerAdapter()
        .registerTimestampTimeSerializerAdapter()
        .registerDurationAdapter()

fun ContentNegotiation.Config.simpleGson(fieldNamingPolicy: FieldNamingPolicy? = null, block: GsonBuilder.() -> Unit = {}) {
    gson {
        fieldNamingPolicy?.let { setFieldNamingPolicy(it) }
        registerTypeAdapterFactory(NullableTypeAdapterFactory())
        registerDateApiAdapter()

        block(this)
    }
}

fun ContentNegotiationConfig.simpleGson(fieldNamingPolicy: FieldNamingPolicy? = null, block: GsonBuilder.() -> Unit = {}) {
    gson {
        fieldNamingPolicy?.let { setFieldNamingPolicy(it) }
        registerTypeAdapterFactory(NullableTypeAdapterFactory())
        registerDateApiAdapter()

        block(this)
    }
}

fun ContentNegotiation.Config.gsonSnakeCase(block: GsonBuilder.() -> Unit = {}) {
    simpleGson(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES, block)
}

fun ContentNegotiationConfig.gsonSnakeCase(block: GsonBuilder.() -> Unit = {}) {
    simpleGson(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES, block)
}

fun ContentNegotiation.Config.gsonIdentity(block: GsonBuilder.() -> Unit = {}) {
    simpleGson(FieldNamingPolicy.IDENTITY, block)
}

fun ContentNegotiationConfig.gsonIdentity(block: GsonBuilder.() -> Unit = {}) {
    simpleGson(FieldNamingPolicy.IDENTITY, block)
}
