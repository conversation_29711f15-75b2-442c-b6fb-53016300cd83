package br.com.alice.clinicalaccount.consumers

import br.com.alice.business.client.BeneficiaryService
import br.com.alice.business.events.BeneficiaryOnboardingPhaseChangedEvent
import br.com.alice.business.events.MoveToPhaseEvent
import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.member.onboarding.client.MemberOnboardingService
import br.com.alice.member.onboarding.model.OnboardingVersion
import br.com.alice.member.onboarding.notifier.MemberOnboardingReadyToTeamAssociationEvent
import br.com.alice.person.client.PersonService
import br.com.alice.person.model.events.MemberActivatedEvent
import br.com.alice.sortinghat.client.PersonTeamAssociationService
import br.com.alice.sortinghat.client.RoutingClient
import br.com.alice.sortinghat.models.input.HealthcareTeamModel
import br.com.alice.sortinghat.models.output.HealthcareTeamOutputModel
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class MemberConsumer(
    private val routingClient: RoutingClient,
    private val personClinicalAccountService: PersonClinicalAccountService,
    private val personTeamAssociationService: PersonTeamAssociationService,
    private val beneficiaryService: BeneficiaryService,
    private val memberOnboardingService: MemberOnboardingService,
    private val personService: PersonService
) : Consumer() {

    companion object {
        private val VALID_PHASES = listOf(
            BeneficiaryOnboardingPhaseType.WAITING_FOR_REVIEW,
            BeneficiaryOnboardingPhaseType.FINISHED
        )
    }

    suspend fun processAssociationByOnboarding(event: MemberOnboardingReadyToTeamAssociationEvent) =
        withSubscribersEnvironment {
            if (shouldExecuteAssociation(event.payload.personId)) executeAssociation(event.payload.personId)
            else false.success()
        }

    suspend fun processAssociationByMoveToPhase(event: MoveToPhaseEvent) =
        span("processAssociationByMoveToPhase") { span ->
            withSubscribersEnvironment {
                processAssociationByBeneficiaryAndPhase(event.payload.beneficiaryId, event.payload.requiredPhaseType)
            }.recordResult(span)
        }

    suspend fun processAssociationByBeneficiaryOnboardingPhaseChanged(event: BeneficiaryOnboardingPhaseChangedEvent) =
        span("processAssociationByMoveToPhase") { span ->
            withSubscribersEnvironment {
                processAssociationByBeneficiaryAndPhase(event.payload.beneficiaryId, event.payload.newPhase.phase)
            }.recordResult(span)
        }

    suspend fun processAssociationByMember(event: MemberActivatedEvent) =
        span("processAssociationByMember") { span ->
            withSubscribersEnvironment {
                val personId = event.payload.personId
                val isB2B = event.payload.isB2B

                span.setAttribute("isMemberOnboardingV2Enabled", isMemberOnboardingV2Enabled())
                span.setAttribute("person_id", personId)
                span.setAttribute("is_b2b", isB2B)

                if (isB2B && isMemberOnboardingV2Enabled())
                    processByMemberActivationUsingOnboardingV2Flow(personId)
                else
                    processByMemberUsingDefaultFlow(personId)
            }.recordResult(span)
        }

    private suspend fun processByMemberUsingDefaultFlow(personId: PersonId) =
        span("processByMemberUsingDefaultFlow") { span ->
            span.setAttribute("person_id", personId)

            personTeamAssociationService.getByPersonId(personId, null).flatMap {
                if (shouldExecuteAssociation(personId)) executeAssociation(personId)
                else false.success()
            }.recordResult(span)
        }

    private suspend fun processByMemberActivationUsingOnboardingV2Flow(personId: PersonId) =
        span("processByMemberActivationUsingOnboardingV2Flow") { span ->
            span.setAttribute("person_id", personId)

            beneficiaryService.findByPersonId(
                personId = personId,
                findOptions = BeneficiaryService.FindOptions(withOnboarding = true)
            ).flatMap { beneficiary ->
                span.setAttribute("beneficiary_id", beneficiary.id)
                span.setAttribute("member_status", beneficiary.memberStatus.toString())
                span.setAttribute("current_phase", beneficiary.onboarding?.currentPhase?.phase)

                if (beneficiary.onboarding?.currentPhase?.phase?.isValid() == true && shouldExecuteAssociation(personId))
                    executeAssociation(beneficiary.personId)
                else
                    false.success()
            }.recordResult(span)
        }

    private suspend fun processAssociationByBeneficiaryAndPhase(
        beneficiaryId: UUID,
        phaseType: BeneficiaryOnboardingPhaseType
    ) =
        span("processAssociationByBeneficiaryAndPhase") { span ->
            span.setAttribute("isMemberOnboardingV2Enabled", isMemberOnboardingV2Enabled())
            span.setAttribute("beneficiary_id", beneficiaryId)
            span.setAttribute("phase", phaseType)

            if (!isMemberOnboardingV2Enabled() || !phaseType.isValid()) return@span false.success()

            beneficiaryService.get(beneficiaryId)
                .flatMap { beneficiary ->
                    span.setAttribute("member_status", beneficiary.memberStatus.toString())

                    if (beneficiary.isActive() && shouldExecuteAssociation(beneficiary.personId)) executeAssociation(beneficiary.personId)
                    else false.success()
                }.recordResult(span)
        }

    private suspend fun executeAssociation(personId: PersonId) =
        routingClient.execute<HealthcareTeamOutputModel>(
            HealthcareTeamModel(
                id = personId.toString(),
                personId = personId
            )
        ).map {
            it.first()
        }.flatMap { output ->
            personClinicalAccountService.associatePerson(
                personId = personId,
                healthcareTeamId = output.id.toUUID()
            )
        }

    private suspend fun shouldExecuteAssociationByOnboardingVersion(personId: PersonId) =
        memberOnboardingService.getOrCreateAbOnboardingVersion(personId).map {
            it !== OnboardingVersion.V3
        }.get()

    private fun isMemberOnboardingV2Enabled(): Boolean =
        FeatureService.get(
            namespace = FeatureNamespace.MEMBERSHIP,
            key = "should-use-onboarding-v2",
            defaultValue = false
        )

    private suspend fun shouldExecuteAssociation(personId: PersonId) =
        personService.get(personId).map {
            it.productInfo?.brand?.isDuquesa() == true || shouldExecuteAssociationByOnboardingVersion(personId)
        }.get()

    private fun BeneficiaryOnboardingPhaseType?.isValid() =
        this?.let { VALID_PHASES.contains(it) } ?: false

    private fun Beneficiary.isActive() =
        this.memberStatus == MemberStatus.ACTIVE
}
