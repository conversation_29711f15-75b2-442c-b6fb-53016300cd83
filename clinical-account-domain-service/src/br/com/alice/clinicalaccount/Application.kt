package br.com.alice.clinicalaccount

import br.com.alice.authentication.authenticationBootstrap
import br.com.alice.authentication.firebase
import br.com.alice.business.ioc.BusinessDomainClientModule
import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.clinicalaccount.consumers.MemberConsumer
import br.com.alice.clinicalaccount.consumers.PersonClinicalAccountHistoryConsumer
import br.com.alice.clinicalaccount.controllers.CaseRecordCreateBackfillController
import br.com.alice.clinicalaccount.controllers.InternalFeaturesController
import br.com.alice.clinicalaccount.controllers.PersonInternalReferenceBackfillController
import br.com.alice.clinicalaccount.routes.apiRoutes
import br.com.alice.clinicalaccount.routes.kafkaRoutes
import br.com.alice.clinicalaccount.services.PersonClinicalAccountServiceImpl
import br.com.alice.clinicalaccount.services.PersonInternalReferenceServiceImpl
import br.com.alice.common.PolicyRootServiceKey
import br.com.alice.common.application.setupDomainService
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.extensions.loadServiceServers
import br.com.alice.common.kafka.internals.kafkaConsumer
import br.com.alice.common.kafka.ioc.KafkaProducerModule
import br.com.alice.common.service.data.layer.DataLayerClientConfiguration
import br.com.alice.common.service.serialization.gsonSnakeCase
import br.com.alice.data.layer.CLINICAL_ACCOUNT_ROOT_SERVICE_NAME
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.services.PersonClinicalAccountDataService
import br.com.alice.data.layer.services.PersonClinicalAccountDataServiceClient
import br.com.alice.data.layer.services.PersonClinicalAccountHistoryDataService
import br.com.alice.data.layer.services.PersonClinicalAccountHistoryDataServiceClient
import br.com.alice.data.layer.services.PersonInternalReferenceDataService
import br.com.alice.data.layer.services.PersonInternalReferenceDataServiceClient
import br.com.alice.featureconfig.core.featureConfigBootstrap
import br.com.alice.featureconfig.ioc.FeatureConfigDomainClientModule
import br.com.alice.member.onboarding.ioc.MemberOnboardingDomainClientModule
import br.com.alice.person.ioc.PersonDomainClientModule
import br.com.alice.sortinghat.ioc.SortingHatDomainClientModule
import com.typesafe.config.ConfigFactory
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.application.Application
import io.ktor.server.auth.Authentication
import io.ktor.server.config.HoconApplicationConfig
import io.ktor.server.routing.routing
import org.koin.core.module.Module
import org.koin.dsl.module

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

object ApplicationModule {

    private val config = HoconApplicationConfig(ConfigFactory.load("application.conf"))

    fun dependencyInjectionModules() = listOf(
        FeatureConfigDomainClientModule,
        KafkaProducerModule,
        SortingHatDomainClientModule,
        PersonDomainClientModule,
        BusinessDomainClientModule,
        MemberOnboardingDomainClientModule,

        module(createdAtStart = true) {
            // Configuration
            single { config }
            single {
                DefaultHttpClient({
                    install(ContentNegotiation) {
                        gsonSnakeCase()
                    }
                }, timeoutInMillis = 15_000)
            }

            // Services
            single<PersonClinicalAccountService> { PersonClinicalAccountServiceImpl(get(), get(), get()) }
            single<PersonInternalReferenceService> { PersonInternalReferenceServiceImpl(get()) }

            // Consumers
            single { MemberConsumer(get(), get(), get(), get(), get(), get()) }
            single { PersonClinicalAccountHistoryConsumer(get()) }

            // DataService
            val invoker = DataLayerClientConfiguration.build()
            single<PersonClinicalAccountDataService> { PersonClinicalAccountDataServiceClient(invoker) }
            single<PersonClinicalAccountHistoryDataService> { PersonClinicalAccountHistoryDataServiceClient(invoker) }
            single<PersonInternalReferenceDataService> { PersonInternalReferenceDataServiceClient(invoker) }

            // Internal
            single { InternalFeaturesController(get(), get(), get(), get(), get()) }
            single { CaseRecordCreateBackfillController(get(), get()) }
            single { PersonInternalReferenceBackfillController(get(), get()) }

            loadServiceServers("br.com.alice.clinicalaccount.services")
        }
    )
}

@JvmOverloads
fun Application.module(
    dependencyInjectionModules: List<Module> = ApplicationModule.dependencyInjectionModules(),
    startRoutesSync: Boolean = true
) {
    setupDomainService(dependencyInjectionModules) {
        install(Authentication) {
            <EMAIL>()
            firebase()
        }

        featureConfigBootstrap(FeatureNamespace.CLINICAL_ACCOUNT, FeatureNamespace.MEMBERSHIP)

        routing {
            application.attributes.put(PolicyRootServiceKey, CLINICAL_ACCOUNT_ROOT_SERVICE_NAME)
            apiRoutes()
        }

        kafkaConsumer(startRoutesSync = startRoutesSync) {
            serviceName = CLINICAL_ACCOUNT_ROOT_SERVICE_NAME
            kafkaRoutes()
        }
    }
}
