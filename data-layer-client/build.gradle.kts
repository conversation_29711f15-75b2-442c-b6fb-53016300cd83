plugins {
    kotlin
    `kotlin-kapt`
}

kapt {
    correctErrorTypes = false
    generateStubs = false
    includeCompileClasspath = false
    useBuildCache = true
}

group = "br.com.alice.data.layer.client"
version = aliceDataLayerVersion

sourceSets {
    main {
        kotlin.sourceDirs = files("src")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

val api by configurations

dependencies {
    implementation(project(":common"))
    implementation(project(":common-core"))
    implementation(project(":common-service-client"))
    implementation(project(":feature-config-domain-client"))
    api(project(":data-packages:business-domain-service-shared-data-package"))
    api(project(":data-packages:feature-config-domain-service-data-package"))

    kapt(project(":common"))

    implementation("com.google.code.gson:gson:$gsonExtrasVersion")
    api("io.gsonfire:gson-fire:1.9.0") //OPA

    api("com.github.kittinunf.result:result:$resultVersion")
    api("ch.qos.logback:logback-classic:$logbackVersion")

    test2Dependencies()

    testImplementation(project(":common-tests"))
    testImplementation(project(":data-layer-common-tests"))
}
