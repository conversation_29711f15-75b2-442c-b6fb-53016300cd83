package br.com.alice.api.backoffice.routes

import br.com.alice.api.backoffice.controllers.staff.StaffController
import br.com.alice.common.coHandler
import br.com.alice.common.multipartHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import org.koin.ktor.ext.inject

fun Routing.staffRoutes() {

    authenticate {
        val staffController by inject<StaffController>()
        route("staff") {
            get("/") {
                coHandler(staffController::index)
            }
            get("/build_staff") {
                coHandler(staffController::buildFormByStaffType)
            }
            get("/search_provider_units") {
                coHandler(staffController::searchProviderUnits)
            }
            get("/sub_specialties") {
                coHandler(staffController::getSubSpecialty)
            }
            get("/address/search") {
                co<PERSON><PERSON><PERSON>(staffController::autocompleteAddress)
            }
            post("/upload_profile_image") {
                multipart<PERSON>andler(staffController::upload_profile_image)
            }
        }
    }
}
