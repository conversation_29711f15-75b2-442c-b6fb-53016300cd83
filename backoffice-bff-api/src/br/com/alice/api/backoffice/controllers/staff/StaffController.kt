package br.com.alice.api.backoffice.controllers.staff

import br.com.alice.common.controllers.Controller
import br.com.alice.staff.client.StaffService
import br.com.alice.common.Response
import br.com.alice.common.MultipartRequest
import br.com.alice.common.ValidationErrorResponse
import br.com.alice.common.storage.FileStorage
import br.com.alice.common.storage.FileStoreRequest
import br.com.alice.common.storage.FileType
import br.com.alice.common.toResponse
import br.com.alice.api.backoffice.utils.ImageRequestValidator
import br.com.alice.api.backoffice.ServiceConfig
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import br.com.alice.common.mappers.CommonInputMapper
import br.com.alice.api.backoffice.mappers.staff.StaffOutputMapper
import br.com.alice.api.backoffice.transfers.StaffFormResponse
import br.com.alice.common.core.StaffType
import br.com.alice.api.backoffice.mappers.staff.StaffFormInputMapper
import br.com.alice.api.backoffice.mappers.staff.StaffFormOutputMapper
import br.com.alice.api.backoffice.services.StaffBackofficeService
import br.com.alice.common.core.Status
import br.com.alice.data.layer.models.MedicalSpecialtyType.SPECIALTY
import br.com.alice.data.layer.models.MedicalSpecialtyType.SUBSPECIALTY
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.core.extensions.toSafeUUID
import br.com.alice.common.RangeUUID
import br.com.alice.common.foldResponse
import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.googlemaps.services.GoogleMapsService
import io.ktor.http.HttpHeaders.ContentRange
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.provider.client.ProviderUnitFilter
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.map
import com.github.kittinunf.result.Result
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.withTimeout
import kotlinx.coroutines.TimeoutCancellationException
import java.util.concurrent.ConcurrentHashMap
import java.time.Duration
import java.time.Instant
import br.com.alice.api.backoffice.transfers.StaffTiersResponse
import br.com.alice.api.backoffice.transfers.StaffScoreResponse
import br.com.alice.api.backoffice.transfers.CouncilTypeResponse

class StaffController(
    private val staffService: StaffService,
    private val staffBackofficeService: StaffBackofficeService,
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val providerUnitService: ProviderUnitService,
    private val googleMapsService: GoogleMapsService,
    private val fileStorage: FileStorage,
) : Controller() {

    private data class CacheEntry<T>(val data: T, val timestamp: Instant)

    private val specialtiesCache = ConcurrentHashMap<String, CacheEntry<List<MedicalSpecialty>>>()
    private val providerUnitsCache = ConcurrentHashMap<String, CacheEntry<List<ProviderUnit>>>()
    private val staffTiersCache = ConcurrentHashMap<String, CacheEntry<List<StaffTiersResponse>>>()
    private val staffScoreCache = ConcurrentHashMap<String, CacheEntry<List<StaffScoreResponse>>>()
    private val councilTypesCache = ConcurrentHashMap<String, CacheEntry<List<CouncilTypeResponse>>>()

    private val cacheTTL = Duration.ofHours(1)

    private val requestTimeout = 5000L

    suspend fun index(queryParams: Parameters): Response = coroutineScope {
        val range = CommonInputMapper.getPaginationParams(queryParams)
        val filter = CommonInputMapper.getFilterParams<String>(queryParams, "q")

        val (items, count) = when {
            filter != null -> {
                val countDeferred = async { staffService.countActiveByToken(filter).get() }

                staffService.findByTokenAndRange(filter, range).map { items -> Pair(items, countDeferred.await()) }
            }
            else -> {
                val countDeferred = async { staffService.count().get() }
                staffService.findByRange(range).map { items -> Pair(items, countDeferred.await()) }
            }
        }.get()

        Response(
            status = HttpStatusCode.OK,
            message = StaffOutputMapper.toPaginatedResponse(items, count, queryParams),
        )
    }

    suspend fun searchProviderUnits(queryParams: Parameters): Response = coroutineScope {
        val filter = StaffFormInputMapper.toFilter(queryParams)
        val searchToken = filter.searchToken

        val result = providerUnitService.getByFilterWithRange(
            filter = ProviderUnitFilter(
                searchToken = searchToken,
                status = listOf(Status.ACTIVE),
                type = emptyList(),
                ids = emptyList()
            ),
            range = 0..100
        )

        val units = when (result) {
            is Result.Success -> result.get()
            else -> emptyList()
        }

        return@coroutineScope Response(
            status = HttpStatusCode.OK,
            message = units
        )
    }

    suspend fun buildFormByStaffType(queryParams: Parameters): Response = coroutineScope {
        val staffType = StaffFormInputMapper.getStaffType(queryParams)
        val response = buildResponseByStaffType(staffType)

        Response(
            status = HttpStatusCode.OK,
            message = response
        )
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private suspend fun buildResponseByStaffType(staffType: StaffType): StaffFormResponse = coroutineScope {
        val needsSpecialties = staffType in listOf(
            StaffType.COMMUNITY_SPECIALIST,
            StaffType.HEALTH_PROFESSIONAL,
            StaffType.PARTNER_HEALTH_PROFESSIONAL
        )

        val needsCouncilTypes = staffType == StaffType.COMMUNITY_SPECIALIST
        val deferredResults = mutableListOf<Deferred<*>>()

        val specialtiesDeferred = if (needsSpecialties) {
            async {
                fetchWithCacheAndTimeout(
                    "specialties",
                    specialtiesCache,
                    { medicalSpecialtyService.getActivesByType(SPECIALTY, false).get().sortedBy { it.name } }
                )
            }.also { deferredResults.add(it) }
        } else null

        val providerUnitsDeferred = if (needsSpecialties) {
            async {
                fetchWithCacheAndTimeout(
                    "providerUnits",
                    providerUnitsCache,
                    {
                        providerUnitService.getByFilterWithRange(
                            filter = ProviderUnitFilter(
                                searchToken = null,
                                status = listOf(Status.ACTIVE),
                                type = listOf(ProviderUnit.Type.HOSPITAL),
                                ids = emptyList()
                            ),
                            range = 0..100
                        ).get()
                    }
                )
            }.also { deferredResults.add(it) }
        } else null

        val staffRolesDeferred = async {
            try {
                withTimeout(requestTimeout) {
                    staffBackofficeService.getStaffRoles(staffType)
                }
            } catch (e: TimeoutCancellationException) {
                emptyList()
            }
        }.also { deferredResults.add(it) }

        val staffTiersDeferred = if (needsSpecialties) {
            async {
                fetchWithCacheAndTimeout(
                    "staffTiers",
                    staffTiersCache,
                    { staffBackofficeService.getStaffTiers() }
                )
            }.also { deferredResults.add(it) }
        } else null

        val staffScoreDeferred = if (needsSpecialties) {
            async {
                fetchWithCacheAndTimeout(
                    "staffScore",
                    staffScoreCache,
                    { staffBackofficeService.getStaffScore() }
                )
            }.also { deferredResults.add(it) }
        } else null

        val councilTypesDeferred = if (needsCouncilTypes) {
            async {
                fetchWithCacheAndTimeout(
                    "councilTypes",
                    councilTypesCache,
                    { staffBackofficeService.getCouncilTypes() }
                )
            }.also { deferredResults.add(it) }
        } else null

        deferredResults.awaitAll()

        return@coroutineScope StaffFormOutputMapper.toResponse(
            specialties = specialtiesDeferred?.getCompleted(),
            providerUnits = providerUnitsDeferred?.getCompleted(),
            staffRoles = staffRolesDeferred.getCompleted(),
            staffTiers = staffTiersDeferred?.getCompleted(),
            staffScore = staffScoreDeferred?.getCompleted(),
            councilTypes = councilTypesDeferred?.getCompleted()
        )
    }

    /**
     * Search data with cache and timeout
     *
     * @param key
     * @param cache
     * @param fetcher
     */
    private suspend fun <T> fetchWithCacheAndTimeout(
        key: String,
        cache: ConcurrentHashMap<String, CacheEntry<T>>,
        fetcher: suspend () -> T
    ): T {
        val cachedEntry = cache[key]
        if (cachedEntry != null && Duration.between(cachedEntry.timestamp, Instant.now()) < cacheTTL) {
            return cachedEntry.data
        }

        return try {
            withTimeout(requestTimeout) {
                val data = fetcher()

                cache[key] = CacheEntry(data, Instant.now())
                data
            }
        } catch (e: TimeoutCancellationException) {
            @Suppress("UNCHECKED_CAST")
            cachedEntry?.data ?: (emptyList<Any>() as T)
        }
    }

    suspend fun getSubSpecialty(queryParams: Parameters): Response {
        val filterQuery = CommonInputMapper.getFilterParams<String>(queryParams, "q")
        val id = CommonInputMapper.getFilterParams<String>(queryParams, "parent_specialty_id")
        val ids = CommonInputMapper.getFilterParams<List<String>>(queryParams, "ids")

        val filtered = when {
            filterQuery != null -> medicalSpecialtyService.getByName(filterQuery, SUBSPECIALTY).get()
            id != null -> medicalSpecialtyService.getByParentId(id.toUUID()).get()
            ids != null -> medicalSpecialtyService.getByIds(ids.map { it.toUUID() }).get()
            else -> null
        }

        return if (filtered != null) {
            Response(
                status = HttpStatusCode.OK,
                message = filtered,
                headers = mapOf(ContentRange to filtered.size.toString())
            )
        } else {
            val range = CommonInputMapper.getPaginationParams(queryParams)
            val byRange = medicalSpecialtyService.getByRange(range, listOf(SUBSPECIALTY)).get()
            val totalCount = medicalSpecialtyService.count().get()

            Response(
                status = HttpStatusCode.OK,
                message = byRange,
                headers = mapOf(ContentRange to totalCount.toString())
            )
        }
    }

    suspend fun autocompleteAddress(queryParams: Parameters): Response {
        val session = header("Session-Id")?.toSafeUUID() ?: RangeUUID.generate()
        return googleMapsService.autocompleteByText(getQueryText(queryParams), session).foldResponse()
    }

    private fun getQueryText(queryParams: Parameters): String =
        queryParams["q"] ?: throw InvalidArgumentException(code = "missing_params", message = "missing parameters q for the search")

    fun upload_profile_image(multipartRequest: MultipartRequest): Response {
        val validator = ImageRequestValidator(multipartRequest)

        if (!validator.isValid()) {
            return ValidationErrorResponse("invalid_file")
        }

        return storeImage(
            multipartRequest, validator.getFileType()!!
        ).toResponse()
    }

    private fun storeImage(multipartRequest: MultipartRequest, extension: FileType): String =
        fileStorage.store(
            FileStoreRequest(
                bucketName = ServiceConfig.bucket("publicAssetsBucket"),
                fileContent = multipartRequest.fileContent!!,
                filePath = "healthcare-team-assets/${multipartRequest.file!!.name}",
                fileType = extension,
                pii = false,
                phi = false
            )
        )
}
