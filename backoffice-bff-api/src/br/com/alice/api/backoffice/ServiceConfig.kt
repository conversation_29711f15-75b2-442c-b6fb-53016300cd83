package br.com.alice.api.backoffice

import br.com.alice.common.core.BaseConfig
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object ServiceConfig : BaseConfig(HoconApplicationConfig(ConfigFactory.load("application.conf"))) {

    val baseUrl get() = config.property("${runningMode.value.lowercase()}.baseUrl").getString()

    fun bucket(name: String) = config
        .property("${runningMode.toString().lowercase()}.buckets.$name")
        .getString()

}
