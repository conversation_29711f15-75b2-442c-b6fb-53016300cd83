package br.com.alice.api.backoffice.controllers.execIndicator

import br.com.alice.api.backoffice.ControllerTestHelper
import br.com.alice.api.backoffice.mappers.execIndicator.HealthSpecialistResourceBundleOutputMapper
import br.com.alice.api.backoffice.transfers.Colors
import br.com.alice.api.backoffice.transfers.FriendlyEnumResponse
import br.com.alice.api.backoffice.transfers.execIndicator.HealthSpecialistResourceBundleCreateRequest
import br.com.alice.api.backoffice.transfers.execIndicator.HealthSpecialistResourceBundleUpdateRequest
import br.com.alice.api.backoffice.transfers.execIndicator.ResourceBundleSpecialtyBffResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.BFFResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.transfers.ListPaginatedResponse
import br.com.alice.common.transfers.PaginationResponse
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthSpecialistProcedureExecutionEnvironment
import br.com.alice.data.layer.models.PricingStatus
import br.com.alice.data.layer.models.ResourceBundleSpecialty
import br.com.alice.data.layer.models.ResourceBundleSpecialtyPrice
import br.com.alice.data.layer.models.TierType
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleManagementService
import br.com.alice.exec.indicator.client.HealthSpecialistResourceBundleService
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePaginatedResponse
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePatchRequest
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePatchResponse
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundlePricingStatus
import br.com.alice.exec.indicator.models.HealthSpecialistResourceBundleWithPricingData
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistMedicalSpecialtyResponse
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistResourceBundleResponse
import br.com.alice.exec.indicator.models.PricingForHealthSpecialistResourceBundleWithCountResponse
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtiesWithCount
import br.com.alice.exec.indicator.models.ResourceBundleSpecialtyResponse
import br.com.alice.exec.indicator.models.SecondaryResourcesTransport
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.http.Parameters
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthSpecialistResourceBundleControllerTest : ControllerTestHelper() {

    private val healthSpecialistResourceBundleService: HealthSpecialistResourceBundleService = mockk()
    private val healthcareResourceService: HealthcareResourceService = mockk()
    private val healthSpecialistResourceBundleManagementService: HealthSpecialistResourceBundleManagementService = mockk()
    private val healthSpecialistResourceBundleController = HealthSpecialistResourceBundleController(
        healthSpecialistResourceBundleService,
        healthcareResourceService,
        healthSpecialistResourceBundleManagementService,
    )

    private val healthcareResource = TestModelFactory.buildHealthcareResource()

    private val healthSpecialistResourceBundle = TestModelFactory.buildHealthSpecialistResourceBundle(
        secondaryResources = listOf(healthcareResource.id)
    )

    private val bundle = TestModelFactory.buildHealthSpecialistResourceBundle()
    private val bundle2 = TestModelFactory.buildHealthSpecialistResourceBundle()
    private val bundles = listOf(bundle, bundle2)

    private val healthSpecialistResourceBundleWithPricing = HealthSpecialistResourceBundleWithPricingData(
        healthSpecialistResourceBundle = bundle,
        pricingStatus = HealthSpecialistResourceBundlePricingStatus.PRICED,
        specialtiesCount = 1,
        allSpecialtiesCount = 1,
        medicalSpecialtyIds = listOf(
            RangeUUID.generate()
        ),
    )

    private val healthSpecialistResourceBundleWithPricing2 = HealthSpecialistResourceBundleWithPricingData(
        healthSpecialistResourceBundle = bundle2,
        pricingStatus = HealthSpecialistResourceBundlePricingStatus.PRICED,
        specialtiesCount = 1,
        allSpecialtiesCount = 1,
        medicalSpecialtyIds = listOf(
            RangeUUID.generate()
        ),
    )

    private val healthSpecialistResourceBundleWithPricingList = listOf(
        healthSpecialistResourceBundleWithPricing,
        healthSpecialistResourceBundleWithPricing2
    )

    @BeforeTest
    override fun setup() {
        clearAllMocks()
        super.setup()
        module.single { healthSpecialistResourceBundleController }
    }

    @Test
    fun `#upsert should add new health specialist resource bundle`() = runBlocking {
        val request = HealthSpecialistResourceBundleCreateRequest(
            primaryTuss = healthSpecialistResourceBundle.primaryTuss,
            secondaryResources = healthSpecialistResourceBundle.secondaryResources.map { SecondaryResourcesTransport(it) },
            executionAmount = healthSpecialistResourceBundle.executionAmount,
            executionEnvironment = healthSpecialistResourceBundle.executionEnvironment,
            aliceDescription = healthSpecialistResourceBundle.description,
            status = healthSpecialistResourceBundle.status,
            serviceType = healthSpecialistResourceBundle.serviceType,
            aliceCode = healthSpecialistResourceBundle.code
        )

        val expectedResponse = HealthSpecialistResourceBundleOutputMapper.toResponse(healthSpecialistResourceBundle)

        coEvery {
            healthSpecialistResourceBundleService.upsert(match {
                it.primaryTuss == request.primaryTuss &&
                        it.secondaryResources == request.secondaryResources?.map { it.id } &&
                        it.executionAmount == request.executionAmount &&
                        it.executionEnvironment == request.executionEnvironment &&
                        it.description == request.aliceDescription &&
                        it.status == request.status &&
                        it.serviceType == request.serviceType &&
                        it.code == request.aliceCode
            })
        } returns healthSpecialistResourceBundle.success()

        authenticatedAs(idToken, staff) {
            post(to = "/healthSpecialistResourceBundle", body = request) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { healthSpecialistResourceBundleService.upsert(any()) }
    }


    @Test
    fun `#upsert returns Bad Request when duplicated`() {
        val request = HealthSpecialistResourceBundleCreateRequest(
            primaryTuss = healthSpecialistResourceBundle.primaryTuss,
            secondaryResources = healthSpecialistResourceBundle.secondaryResources.map { SecondaryResourcesTransport(it) },
            executionAmount = healthSpecialistResourceBundle.executionAmount,
            executionEnvironment = healthSpecialistResourceBundle.executionEnvironment,
            aliceDescription = healthSpecialistResourceBundle.description,
            status = healthSpecialistResourceBundle.status,
            serviceType = healthSpecialistResourceBundle.serviceType,
            aliceCode = ""
        )

        coEvery {
            healthSpecialistResourceBundleService.upsert(match {
                it.primaryTuss == request.primaryTuss &&
                        it.secondaryResources == request.secondaryResources?.map { it.id } &&
                        it.executionAmount == request.executionAmount &&
                        it.executionEnvironment == request.executionEnvironment &&
                        it.description == request.aliceDescription &&
                        it.status == request.status &&
                        it.serviceType == request.serviceType &&
                        it.code != request.aliceCode
            })
        } returns DuplicatedItemException().failure()

        authenticatedAs(idToken, staff) {
            post(to = "/healthSpecialistResourceBundle", body = request) { response ->
                assertThat(response).isBadRequestWithErrorCode("health_specialist_resource_bundle_duplicated", "health_specialist_resource_bundle_duplicated")
            }
        }

        coVerifyOnce { healthSpecialistResourceBundleService.upsert(any()) }
    }

    @Test
    fun `#index returns HealthSpecialistResourceBundle paginated`() {
        val range = 0..19
        coEvery {
            healthSpecialistResourceBundleManagementService.list(null, range)
        } returns HealthSpecialistResourceBundlePaginatedResponse(
            items = healthSpecialistResourceBundleWithPricingList,
            total = 2,
        ).success()

        val queryParams = Parameters.build {
            append("page", "1")
            append("pageSize", "20")
        }

        val expectedResponse = HealthSpecialistResourceBundleOutputMapper.toPaginatedResponse(
            healthSpecialistResourceBundleWithPricingList,
            healthSpecialistResourceBundleWithPricingList.count(),
            queryParams
        )

        authenticatedAs(idToken, staff) {
            get("/healthSpecialistResourceBundle/?page=1&pageSize=20") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#index returns HealthSpecialistResourceBundle paginated by query`() {
        val range = 0..19
        val query = "query"

        coEvery {
            healthSpecialistResourceBundleManagementService.list(query, range)
        } returns HealthSpecialistResourceBundlePaginatedResponse(
            items = healthSpecialistResourceBundleWithPricingList,
            total = 2,
        ).success()

        val queryParams = Parameters.build {
            append("page", "1")
            append("pageSize", "20")
        }

        val expectedResponse = HealthSpecialistResourceBundleOutputMapper.toPaginatedResponse(
            healthSpecialistResourceBundleWithPricingList,
            healthSpecialistResourceBundleWithPricingList.count(),
            queryParams
        )

        authenticatedAs(idToken, staff) {
            get("/healthSpecialistResourceBundle/?filter={q:\"$query\"}&page=1&pageSize=20") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

    @Test
    fun `#update should update health specialist resource bundle`() = runBlocking {
        val request = HealthSpecialistResourceBundleUpdateRequest(
            secondaryResources = healthSpecialistResourceBundle.secondaryResources.map { SecondaryResourcesTransport(it) },
            executionAmount = 5,
            executionEnvironment = HealthSpecialistProcedureExecutionEnvironment.SURGICAL,
            aliceDescription = "nova descricao",
            status = healthSpecialistResourceBundle.status,
            serviceType = healthSpecialistResourceBundle.serviceType,
        )
        val updated = healthSpecialistResourceBundle.copy(
            executionAmount = request.executionAmount,
            executionEnvironment = request.executionEnvironment,
            description = request.aliceDescription,
        )
        val expectedResponse = HealthSpecialistResourceBundleOutputMapper.toResponse(updated)

        coEvery {
            healthSpecialistResourceBundleService.get(healthSpecialistResourceBundle.id)
        } returns healthSpecialistResourceBundle.success()
        coEvery {
            healthSpecialistResourceBundleService.update(updated)
        } returns updated.success()

        authenticatedAs(idToken, staff) {
            put(
                to = "/healthSpecialistResourceBundle/${healthSpecialistResourceBundle.id}",
                body = request
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { healthSpecialistResourceBundleService.get(healthSpecialistResourceBundle.id) }
        coVerifyOnce { healthSpecialistResourceBundleService.update(any()) }
    }

    @Test
    fun `#update returns Not Found when health specialist resource bundle does not exist`() = runBlocking {
        val request = HealthSpecialistResourceBundleUpdateRequest(
            secondaryResources = healthSpecialistResourceBundle.secondaryResources.map { SecondaryResourcesTransport(it) },
            executionAmount = 5,
            executionEnvironment = HealthSpecialistProcedureExecutionEnvironment.SURGICAL,
            aliceDescription = "nova descricao",
            status = healthSpecialistResourceBundle.status,
            serviceType = healthSpecialistResourceBundle.serviceType,
        )

        coEvery { healthSpecialistResourceBundleService.get(healthSpecialistResourceBundle.id) } returns NotFoundException().failure()

        authenticatedAs(idToken, staff) {
            put(
                to = "/healthSpecialistResourceBundle/${healthSpecialistResourceBundle.id}",
                body = request
            ) { response ->
                assertThat(response).isBadRequestWithErrorCode(
                    "health_specialist_resource_bundle_not_found",
                    "health_specialist_resource_bundle_not_found"
                )
            }
        }

        coVerifyOnce { healthSpecialistResourceBundleService.get(healthSpecialistResourceBundle.id) }
        coVerifyNone { healthSpecialistResourceBundleService.update(any()) }
    }

    @Test
    fun `#get should return health specialist resource bundle by id`() = runBlocking {
        coEvery { healthSpecialistResourceBundleService.get(healthSpecialistResourceBundle.id) } returns healthSpecialistResourceBundle.success()
        coEvery {
            healthcareResourceService.getByIds(listOf(healthcareResource.id))
        } returns listOf(healthcareResource).success()

        val expectedResponse = HealthSpecialistResourceBundleOutputMapper.toResponse(
            healthSpecialistResourceBundle,
            listOf(healthcareResource),
        )

        authenticatedAs(idToken, staff) {
            get("/healthSpecialistResourceBundle/${healthSpecialistResourceBundle.id}") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { healthSpecialistResourceBundleService.get(healthSpecialistResourceBundle.id) }
    }

    @Test
    fun `#patch should update health specialist resource bundle`() = runBlocking {
        val request = HealthSpecialistResourceBundlePatchRequest(
            aliceDescription = "nova descricao",
            status = healthSpecialistResourceBundle.status,
            medicalSpecialtyIds = listOf(RangeUUID.generate()),
        )
        val updated = healthSpecialistResourceBundle.copy(
            description = request.aliceDescription.toString(),
            status = request.status ?: healthSpecialistResourceBundle.status,
        )
        val expectedResponse = HealthSpecialistResourceBundlePatchResponse(
            id = updated.id,
            secondaryResources = updated.secondaryResources.map { SecondaryResourcesTransport(it) },
            executionAmount = updated.executionAmount,
            executionEnvironment = updated.executionEnvironment,
            aliceDescription = updated.description,
            status = updated.status,
            serviceType = updated.serviceType,
            medicalSpecialtyIds = request.medicalSpecialtyIds ?: emptyList(),
        )

        coEvery {
            healthSpecialistResourceBundleManagementService.update(healthSpecialistResourceBundle.id, request)
        } returns expectedResponse.success()

        authenticatedAs(idToken, staff) {
            patch(
                to = "/healthSpecialistResourceBundle/${healthSpecialistResourceBundle.id}",
                body = request
            ) { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { healthSpecialistResourceBundleManagementService.update(healthSpecialistResourceBundle.id, request) }
    }

    @Test
    fun `#patch returns Not Found when health specialist resource bundle does not exist`() = runBlocking {
        val request = HealthSpecialistResourceBundlePatchRequest(
            aliceDescription = "nova descricao",
            status = healthSpecialistResourceBundle.status,
        )

        coEvery { healthSpecialistResourceBundleManagementService.update(healthSpecialistResourceBundle.id, request) } returns NotFoundException().failure()

        authenticatedAs(idToken, staff) {
            patch(
                to = "/healthSpecialistResourceBundle/${healthSpecialistResourceBundle.id}",
                body = request
            ) { response ->
                assertThat(response).isBadRequestWithErrorCode(
                    "health_specialist_resource_bundle_not_found",
                    "health_specialist_resource_bundle_not_found"
                )
            }
        }

        coVerifyOnce { healthSpecialistResourceBundleManagementService.update(healthSpecialistResourceBundle.id, request) }
    }

    @Test
    fun `#getMedicalSpecialtiesRelatedToResourceBundle should return medical specialties related to resource bundle`() = runBlocking {
        val medicalSpecialtyId = RangeUUID.generate()
        val serviceResponse =
            ResourceBundleSpecialtiesWithCount(
                specialties = listOf(ResourceBundleSpecialtyResponse(
                    id = medicalSpecialtyId,
                    name = "Cardiologia",
                    isTherapy = false,
                    pricingStatus = HealthSpecialistResourceBundlePricingStatus.PRICED,
                    currentBeginAt = LocalDate.now(),
                    currentEndAt = LocalDate.now().plusDays(1),
                    hasScheduledPriceChange = false,
                    medicalSpecialtyId = medicalSpecialtyId,
                )),
                count = 1
            )

        val bffResponse = ResourceBundleSpecialtyBffResponse(
            id = medicalSpecialtyId,
            name = "Cardiologia",
            isTherapy = false,
            pricingStatus = FriendlyEnumResponse(
                friendlyName = HealthSpecialistResourceBundlePricingStatus.PRICED.description,
                value = HealthSpecialistResourceBundlePricingStatus.PRICED,
                color = Colors.GREEN
            ),
            currentBeginAt = LocalDate.now(),
            currentEndAt = LocalDate.now().plusDays(1),
            hasScheduledPriceChange = false,
            medicalSpecialtyId = medicalSpecialtyId,
        )

        val queryParams = Parameters.build {
            append("page", "1")
            append("pageSize", "20")
        }

        val expectedResponse = HealthSpecialistResourceBundleOutputMapper.toAssociatedResourceBundleSpecialtiesPaginatedResponse(
            serviceResponse.specialties,
            1,
            queryParams
        )

        coEvery {
            healthSpecialistResourceBundleManagementService.getMedicalSpecialtiesRelatedToResourceBundle(
                healthSpecialistResourceBundle.id,
                0..19
            )
        } returns serviceResponse.success()

        authenticatedAs(idToken, staff) {
            get("/healthSpecialistResourceBundle/${healthSpecialistResourceBundle.id}/medicalSpecialties?page=1&pageSize=20") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce {
            healthSpecialistResourceBundleManagementService.getMedicalSpecialtiesRelatedToResourceBundle(
                healthSpecialistResourceBundle.id,
                any()
            )
        }
    }

    @Test
    fun `#getPendingResourceBundleSpecialtiesForPricing should return a list of UUID`() = runBlocking {
        val expectedResponse = listOf(
            RangeUUID.generate(),
            RangeUUID.generate()
        )

        val resourceBundleSpecialties = expectedResponse.map {
            ResourceBundleSpecialty(
                id = it,
                healthSpecialistResourceBundleId = RangeUUID.generate(),
                medicalSpecialtyId = RangeUUID.generate(),
                status = Status.ACTIVE,
                pricingStatus = PricingStatus.NOT_PRICED
            )
        }

        coEvery {
            healthSpecialistResourceBundleManagementService.getPendingResourceSpecialtyBundlesForPricing()
        } returns resourceBundleSpecialties.success()

        authenticatedAs(idToken, staff) {
            get("/resourceBundleSpecialty/pending") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { healthSpecialistResourceBundleManagementService.getPendingResourceSpecialtyBundlesForPricing() }
    }

    @Test
    fun `#getResourceBundleSpecialtyPricingList should return a paginated list of resource bundles with their specialties and pricing`() = runBlocking {
        val serviceResponse = PricingForHealthSpecialistResourceBundleWithCountResponse(
            count = 1,
            items = listOf(
                PricingForHealthSpecialistResourceBundleResponse(
                    healthSpecialistResourceBundleId = bundle.id,
                    primaryTuss = bundle.primaryTuss,
                    aliceCode = bundle.code,
                    description = bundle.description,
                    pendingNumber = 0,
                    serviceType = bundle.serviceType.description,
                    medicalSpecialties = listOf(
                        PricingForHealthSpecialistMedicalSpecialtyResponse(
                            medicalSpecialtyId = RangeUUID.generate(),
                            description = "Cardiologia",
                            prices = listOf(ResourceBundleSpecialtyPrice(
                                tier = SpecialistTier.TALENTED,
                                productTier = TierType.TIER_1,
                                price = BigDecimal(100)
                            )),
                            pendingNumber = 0,
                            beginAt = LocalDate.now(),
                            changeBeginAt = null,
                            resourceBundleSpecialtyId = RangeUUID.generate()
                        )
                    )
                )
            )
        )

        val expectedResponse = ListPaginatedResponse<PricingForHealthSpecialistResourceBundleResponse>(
            results = serviceResponse.items,
            pagination = PaginationResponse(
                page = 1,
                pageSize = 10,
                totalPages = 1
            )
        )
        val medicalSpecialtyId = RangeUUID.generate()

        val range = IntRange(0, 9)

        coEvery {
            healthSpecialistResourceBundleManagementService.getResourceBundleSpecialtyPricingList(
                filters = match {
                    it.query == null &&
                            it.status == null
                },
                range,
            )
        } returns serviceResponse.success()

        authenticatedAs(idToken, staff) {
            get("/healthSpecialistResourceBundle/pricing?filter={medicalSpecialtyIds:[$medicalSpecialtyId]}&page=1&pageSize=10") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }

    }

}
