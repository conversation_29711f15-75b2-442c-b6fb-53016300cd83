ktor {
    deployment {
        port = 8080
        port = ${?PORT}
        connectionGroupSize = 2
        workerGroupSize = 2
        callGroupSize = 4
    }
    application {
        modules = [ br.com.alice.api.backoffice.ApplicationKt.module ]
    }
    httpclient {
        timeout = 500
        timeout = ${?HTTP_CLIENT_TIMEOUT}
    }
}

systemEnv = "test"
systemEnv = ${?SYSTEM_ENV}

development {
  localhost = "host.docker.internal"
  localhost = ${?LOCALHOST}
  baseUrl = "http://"${development.localhost}":8062"
  buckets {
    publicAssetsBucket = "publicAssetsBucketDev"
  }
}

test {
  baseUrl = "http://localhost:9000"
  buckets {
    publicAssetsBucket = "publicAssetsBucketTest"
  }
}

production {
  host = "backoffice-bff-api.wonderland.engineering"
  host = ${?BACKOFFICE_BFF_API_HOST}
  baseUrl = "https://"${production.host}""
  buckets {
    publicAssetsBucket = "web.assets.alice.com.br"
    publicAssetsBucket = ${?PUBLIC_ASSETS_BUCKET}
  }
}
