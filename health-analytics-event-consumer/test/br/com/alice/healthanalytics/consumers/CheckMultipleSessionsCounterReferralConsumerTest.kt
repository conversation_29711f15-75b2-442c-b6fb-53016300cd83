package br.com.alice.healthanalytics.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.communication.crm.analytics.AnalyticsEvent
import br.com.alice.communication.crm.analytics.AnalyticsEventName
import br.com.alice.communication.crm.analytics.AnalyticsTrackerResult
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthPlanTaskType
import br.com.alice.data.layer.models.NextStepSpecialistInfo
import br.com.alice.data.layer.models.NextStepSpecialistType
import br.com.alice.ehr.client.CounterReferralService
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.person.client.PersonService
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.secondary.attention.events.CounterReferralCreatedEvent
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class CheckMultipleSessionsCounterReferralConsumerTest: ConsumerTest() {

    private val personService: PersonService = mockk()
    private val counterReferralService: CounterReferralService = mockk()
    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val crmAnalyticsTracker: CrmAnalyticsTracker = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()

    private val consumer: CheckMultipleSessionsCounterReferralConsumer =
        CheckMultipleSessionsCounterReferralConsumer(
            personService,
            counterReferralService,
            healthPlanTaskService,
            crmAnalyticsTracker,
            healthProfessionalService,
            medicalSpecialtyService
        )

    private val nationalId = "**********"
    private val person = TestModelFactory.buildPerson(
        nationalId = nationalId
    )
    private val healthPlanId = RangeUUID.generate()

    private val totalSessions = 3

    private val referral = TestModelFactory.buildHealthPlanTaskReferral(
        healthPlanId = healthPlanId,
        personId = person.id,
        sessionsQuantity = totalSessions
    )

    private val counterReferral = TestModelFactory.buildCounterReferral().copy(
        personId = person.id,
        referralId = referral.id,
        healthCommunitySpecialistId = RangeUUID.generate()
    )

    private val referralWithOneSession = TestModelFactory.buildHealthPlanTaskReferral(
        healthPlanId = healthPlanId,
        personId = person.id,
        sessionsQuantity = 1
    )

    private val counterReferralItem = TestModelFactory.buildCounterReferral(
        referralId = referral.id,
        personId = person.id,
    )

    private val healthSpecialist = TestModelFactory.buildHealthProfessional(
        specialtyId = RangeUUID.generate()
    )

    private val specialty = TestModelFactory.buildMedicalSpecialty()

    private val expectedResult = AnalyticsTrackerResult(true)

    @BeforeTest
    fun setup() {
        super.before()

        coEvery {
            healthProfessionalService.findByStaffId(counterReferral.staffId)
        } returns healthSpecialist.success()

        coEvery {
            medicalSpecialtyService.getById(healthSpecialist.specialtyId!!)
        } returns specialty.success()
    }

    @AfterTest
    fun confirm() = confirmVerified(
        personService,
        counterReferralService,
        healthPlanTaskService,
        crmAnalyticsTracker
    )

    @Test
    fun `#counterReferralCreatedEvent should send discharged when expected event to braze`() = mockDate { localDateTimeNow ->
        val referral = TestModelFactory.buildHealthPlanTaskReferral(
            sessionsQuantity = 2
        )
        coEvery { healthPlanTaskService.get(referral.id) } returns referral.success()

        val counterReferral = counterReferral.copy(
            nextStepSpecialistInfo = NextStepSpecialistInfo(
                type = NextStepSpecialistType.DISCHARGED
            ),
            referralId = referral.id
        )
        val event = CounterReferralCreatedEvent(counterReferral)

        coEvery {
            counterReferralService.getByReferral(referral.id)
        } returns listOf(counterReferral, counterReferralItem).success()

        coEvery { personService.get(person.id) } returns person.success()

        coEvery {
            crmAnalyticsTracker.sendEvent(
                person.nationalId,
                AnalyticsEvent(
                    name = AnalyticsEventName.THERAPY_DISCHARGED_WHEN_EXPECTED,
                    timestamp = localDateTimeNow,
                    properties = mapOf(
                        "specialty" to specialty.name,
                        "remaining_sessions" to 0,
                        "current_session" to 2,
                        "total_sessions" to 2
                    )
                )
            )
        } returns expectedResult

        val result = consumer.counterReferralCreatedEvent(event)
        assertThat(result).isSuccessWithData(expectedResult)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyOnce { counterReferralService.getByReferral(any()) }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { crmAnalyticsTracker.sendEvent(any(), any()) }
    }

    @Test
    fun `#counterReferralCreatedEvent should send discharged before expected event to braze`() = mockDate { localDateTimeNow ->
        val event = CounterReferralCreatedEvent(counterReferral.copy(
            nextStepSpecialistInfo = NextStepSpecialistInfo(
                type = NextStepSpecialistType.DISCHARGED
            )
        ))

        coEvery { healthPlanTaskService.get(referral.id) } returns referral.success()

        coEvery {
            counterReferralService.getByReferral(referral.id)
        } returns listOf(counterReferralItem).success()

        coEvery { personService.get(person.id) } returns person.success()

        coEvery {
            crmAnalyticsTracker.sendEvent(
                person.nationalId,
                AnalyticsEvent(
                    name = AnalyticsEventName.THERAPY_DISCHARGED_BEFORE_EXPECTED,
                    timestamp = localDateTimeNow,
                    properties = mapOf(
                        "specialty" to specialty.name,
                        "remaining_sessions" to totalSessions - 1,
                        "current_session" to 1,
                        "total_sessions" to totalSessions
                    )
                )
            )
        } returns expectedResult

        val result = consumer.counterReferralCreatedEvent(event)
        assertThat(result).isSuccessWithData(expectedResult)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyOnce { counterReferralService.getByReferral(any()) }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { crmAnalyticsTracker.sendEvent(any(), any()) }
    }

    @Test
    fun `#counterReferralCreatedEvent should send first session completed to braze`() = mockDate { localDateTimeNow ->
        val event = CounterReferralCreatedEvent(counterReferral)
        coEvery { healthPlanTaskService.get(referral.id) } returns referral.success()

        coEvery {
            counterReferralService.getByReferral(referral.id)
        } returns listOf(counterReferralItem).success()

        coEvery { personService.get(person.id) } returns person.success()

        coEvery {
            crmAnalyticsTracker.sendEvent(
                person.nationalId,
                AnalyticsEvent(
                    name = AnalyticsEventName.MULTIPLE_SESSIONS_REFERRAL_FIRST_COMPLETED,
                    timestamp = localDateTimeNow,
                    properties = mapOf(
                        "specialty" to specialty.name,
                        "remaining_sessions" to totalSessions - 1,
                        "current_session" to 1,
                        "total_sessions" to totalSessions
                    )
                )
            )
        } returns expectedResult

        val result = consumer.counterReferralCreatedEvent(event)
        assertThat(result).isSuccessWithData(expectedResult)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyOnce { counterReferralService.getByReferral(any()) }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { crmAnalyticsTracker.sendEvent(any(), any()) }
    }

    @Test
    fun `#counterReferralCreatedEvent should send last session available to braze`() =  mockDate { localDateTimeNow ->
        val event = CounterReferralCreatedEvent(counterReferral)

        coEvery { healthPlanTaskService.get(referral.id) } returns referral.success()

        coEvery {
            counterReferralService.getByReferral(referral.id)
        } returns listOf(counterReferralItem, counterReferralItem).success()

        coEvery { personService.get(person.id) } returns person.success()

        coEvery {
            crmAnalyticsTracker.sendEvent(
                person.nationalId,
                AnalyticsEvent(
                    name = AnalyticsEventName.MULTIPLE_SESSIONS_REFERRAL_LAST_AVAILABLE,
                    timestamp = localDateTimeNow,
                    mapOf(
                        "specialty" to specialty.name,
                        "remaining_sessions" to totalSessions - 2,
                        "current_session" to 2,
                        "total_sessions" to totalSessions
                    )
                )
            )
        } returns expectedResult

        val result = consumer.counterReferralCreatedEvent(event)
        assertThat(result).isSuccessWithData(expectedResult)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyOnce { counterReferralService.getByReferral(any()) }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { crmAnalyticsTracker.sendEvent(any(), any()) }
    }

    @Test
    fun `#counterReferralCreatedEvent should send last session completed to braze`() =  mockDate { localDateTimeNow ->
        val event = CounterReferralCreatedEvent(counterReferral)

        coEvery { healthPlanTaskService.get(referral.id) } returns referral.success()

        coEvery {
            counterReferralService.getByReferral(referral.id)
        } returns listOf(counterReferralItem, counterReferralItem, counterReferralItem).success()

        coEvery { personService.get(person.id) } returns person.success()

        coEvery {
            crmAnalyticsTracker.sendEvent(
                person.nationalId,
                AnalyticsEvent(
                    name = AnalyticsEventName.MULTIPLE_SESSIONS_REFERRAL_LAST_COMPLETED,
                    timestamp = localDateTimeNow,
                    mapOf(
                        "specialty" to specialty.name,
                        "remaining_sessions" to totalSessions - 3,
                        "current_session" to 3,
                        "total_sessions" to totalSessions
                    )
                )
            )
        } returns expectedResult

        val result = consumer.counterReferralCreatedEvent(event)
        assertThat(result).isSuccessWithData(expectedResult)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyOnce { counterReferralService.getByReferral(any()) }
        coVerifyOnce { personService.get(any()) }
        coVerifyOnce { crmAnalyticsTracker.sendEvent(any(), any()) }
    }

    @Test
    fun `#counterReferralCreatedEvent should ignore event without referral id`() = runBlocking {
        val counterReferral = TestModelFactory.buildCounterReferral().copy(
            personId = person.id,
            referralId = null
        )
        val event = CounterReferralCreatedEvent(counterReferral)

        val result = consumer.counterReferralCreatedEvent(event)
        assertThat(result).isSuccess()

        coVerifyNone { crmAnalyticsTracker.sendEvent(any(), any()) }
    }

    @Test
    fun `#counterReferralCreatedEvent should ignore when referral has one session`() = runBlocking {
        val counterReferral = TestModelFactory.buildCounterReferral().copy(
            personId = person.id,
            referralId = referralWithOneSession.id
        )
        val event = CounterReferralCreatedEvent(counterReferral)

        coEvery { healthPlanTaskService.get(referralWithOneSession.id) } returns referralWithOneSession.success()

        val result = consumer.counterReferralCreatedEvent(event)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyNone { counterReferralService.getByReferral(any()) }
        coVerifyNone { personService.get(any()) }
        coVerifyNone { crmAnalyticsTracker.sendEvent(any(), any()) }
    }

    @Test
    fun `#counterReferralCreatedEvent should ignore when is follow up request`() = runBlocking {
        val followUpRequest = TestModelFactory.buildHealthPlanTask(
            personId = person.id,
            type = HealthPlanTaskType.FOLLOW_UP_REQUEST
        )
        val counterReferral = TestModelFactory.buildCounterReferral().copy(
            personId = person.id,
            referralId = followUpRequest.id
        )
        val event = CounterReferralCreatedEvent(counterReferral)

        coEvery { healthPlanTaskService.get(followUpRequest.id) } returns followUpRequest.success()

        val result = consumer.counterReferralCreatedEvent(event)
        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { healthPlanTaskService.get(any()) }
        coVerifyNone { counterReferralService.getByReferral(any()) }
        coVerifyNone { personService.get(any()) }
        coVerifyNone { crmAnalyticsTracker.sendEvent(any(), any()) }
    }

    private fun mockDate(testFunction: suspend (LocalDateTime) -> Unit) = runBlocking {
        val localDateTimeNow = LocalDateTime.now()
        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now() } returns localDateTimeNow
            testFunction(localDateTimeNow)
        }
    }
}
