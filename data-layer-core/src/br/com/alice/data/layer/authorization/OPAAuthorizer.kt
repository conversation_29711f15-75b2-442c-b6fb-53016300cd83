package br.com.alice.data.layer.authorization

import br.com.alice.common.core.extensions.classSimpleName
import br.com.alice.common.logging.logger
import br.com.alice.common.observability.opentelemetry.Tracer
import br.com.alice.common.serialization.gson
import br.com.alice.common.service.serialization.gsonOPA
import com.google.gson.JsonObject
import com.google.gson.JsonSyntaxException
import com.google.gson.annotations.Expose
import io.ktor.client.HttpClient
import io.ktor.client.engine.apache.Apache
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.server.config.HoconApplicationConfig
import io.opentelemetry.api.trace.Span
import io.opentelemetry.api.trace.SpanKind
import kotlin.reflect.KClass
import kotlin.reflect.KType

class OPAAuthorizer(
    private val opaConfiguration: OPAConfiguration,
    private val httpClient: HttpClient = HttpClient(Apache) {
        install(ContentNegotiation)
        install(HttpTimeout) {
            requestTimeoutMillis = 1000
            socketTimeoutMillis = 1000
            connectTimeoutMillis = 10
        }
    }
) {

    suspend fun authorize(authorizationRequest: AuthorizationRequest): Authorization = span("authorize") {
        val opaRequest = authorizationRequest.toOPAAuthorizationRequest()
        val response = postToOPA(opaRequest, authorizationRequest.rootService.name)
        Authorization(response.result.contains(0), "$authorizationRequest")
    }

    suspend fun <M> authorizeBatch(batchAuthorizationRequest: BatchAuthorizationRequest): List<Pair<M, Authorization>> = span("authorizeBatch") {
        val opaRequest = batchAuthorizationRequest.toOPAAuthorizationRequest()
        val response = postToOPA(opaRequest, batchAuthorizationRequest.rootService.name)
        batchAuthorizationRequest.requests.mapIndexed { index, req ->
            req.resource as M to Authorization(response.result.contains(index), "$batchAuthorizationRequest")
        }
    }

    private suspend fun postToOPA(
        opaRequest: Map<String, JsonObject>,
        rootServiceName: String
    ): OPAAuthorizationResponse {
        logger.debug("OPA request body: $opaRequest")
        val response = httpClient.post(buildOPAUrl(rootServiceName)) {
            header("Content-Type", "application/json")
            setBody(gson.toJson(opaRequest))
        }
        logger.debug("OPA response: ${response.bodyAsText()}")
        try {
            return gsonOPA.fromJson(response.bodyAsText(), OPAAuthorizationResponse::class.java)
        } catch (e: JsonSyntaxException) {
            logger.error("Error parsing OPA response", "payload" to response.bodyAsText())
            throw e
        }
    }

    private fun buildOPAUrl(rootServiceName: String) = "${opaConfiguration.opaHost}:${opaConfiguration.opaPort}" +
            "/v1/data/${opaConfiguration.opaNamespace}/${rootServiceName.lowercase().replace("-", "_")}" +
            "/${opaConfiguration.opaDefaultDecision}"

    suspend fun <T> span(methodName: String, block: suspend (Span) -> T) : T =
        Tracer.span(this.classSimpleName() + "::" + methodName, SpanKind.INTERNAL, "DEBUG", block = block)

}

class OPAConfiguration(config: HoconApplicationConfig) {
    val opaHost = config.property("opa.host").getString()
    val opaPort = config.property("opa.port").getString()
    val opaNamespace = config.property("opa.namespace").getString()
    val opaDefaultDecision = config.property("opa.defaultDecision").getString()
}

fun AuthorizationRequest.toOPAAuthorizationRequest() = mapOf(
    "input" to gsonOPA.toJsonTree(OPAAuthorizationRequest(
        listOf(OPAAuthorizationCase(
            index = 0,
            action = action.toString(),
            subject = buildOPAObject(subject, null),
            resource = buildOPAObject(resource, resourceClass)
        ))
    )).asJsonObject
)

fun BatchAuthorizationRequest.toOPAAuthorizationRequest() = mapOf(
    "input" to gsonOPA.toJsonTree(OPAAuthorizationRequest(
        List(requests.size) { index ->
            OPAAuthorizationCase(
                index = index,
                action = requests[index].action.toString(),
                subject = buildOPAObject(requests[index].subject, null),
                resource = buildOPAObject(requests[index].resource, null)
            )
        }
    )).asJsonObject
)

private fun buildOPAObject(obj: Any?, klass: KClass<*>?): JsonObject {
    val currentClass = obj?.let { it::class } ?: klass
    val result = gsonOPA.toJsonTree(obj ?: JsonObject()).asJsonObject
    val superTypes = currentClass?.supertypes.toSuperTypeNames()

    result.addProperty("opaType", currentClass?.simpleName)

    if(!superTypes.contains("Subject")) {
        result.add("opaSuperTypes", gsonOPA.toJsonTree(superTypes))
    }

    return result
}

private fun List<KType>?.toSuperTypeNames() =
    this?.mapNotNull {
        val simpleName = (it.classifier as KClass<*>).simpleName
        if (simpleName != "Any") simpleName else null
    } ?: emptyList()

data class OPAAuthorizationRequest(
    @Expose
    val cases: List<OPAAuthorizationCase>
)

data class OPAAuthorizationCase(
    @Expose
    val index: Int,
    @Expose
    val action: String,
    @Expose
    val subject: JsonObject,
    @Expose
    val resource: JsonObject
)

data class OPAAuthorizationResponse(
    @Expose
    val result: List<Int>
)
