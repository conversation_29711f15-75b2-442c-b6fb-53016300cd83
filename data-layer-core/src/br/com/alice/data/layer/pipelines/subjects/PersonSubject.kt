package br.com.alice.data.layer.pipelines.subjects

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Subject
import br.com.alice.common.serialization.ListOfModelIdTypeAdapter
import br.com.alice.data.layer.models.MemberModel
import br.com.alice.data.layer.models.PersonModel
import com.google.gson.annotations.Expose
import com.google.gson.annotations.JsonAdapter
import io.gsonfire.annotations.ExposeMethodResult
import java.util.UUID

data class PersonSubject(
    @Expose
    val id: PersonId,
    val person: PersonModel,
    val billingAccountablePartyId: UUID? = null,
    val parentPerson: PersonModel? = null,
    @Expose
    @JsonAdapter(ListOfModelIdTypeAdapter::class)
    val dependentPersons: List<PersonModel> = emptyList(),
    @Expose
    @JsonAdapter(ListOfModelIdTypeAdapter::class)
    val members: List<MemberModel>? = null,
    @Expose
    @JsonAdapter(ListOfModelIdTypeAdapter::class)
    val legalGuardianPersons: List<PersonModel>? = null,
    @Expose
    @JsonAdapter(ListOfModelIdTypeAdapter::class)
    val dependentMembers: List<MemberModel> = emptyList()
): Subject {

    override fun toString() = "${this::class.simpleName}($id)"

    //OPA Authorization
    @ExposeMethodResult("parentPersonId")
    fun getParentPersonId() = parentPerson?.id

    @ExposeMethodResult("personLeadId")
    fun getPersonLeadId() = person.leadId

    @ExposeMethodResult("personEmail")
    fun getPersonEmail() = person.email
}
