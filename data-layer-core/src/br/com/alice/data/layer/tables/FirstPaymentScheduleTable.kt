package br.com.alice.data.layer.tables

import br.com.alice.common.RangeUUID
import br.com.alice.common.UpdatedBy
import br.com.alice.data.layer.models.FirstPaymentScheduleStatus
import br.com.alice.data.layer.models.FirstPaymentScheduleStatusHistoryEntryModel
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

internal data class FirstPaymentScheduleTable(
    override val id: UUID = RangeUUID.generate(),
    val preActivationPaymentId: UUID,
    val companyId: UUID,
    val companySubcontractId: UUID,
    val memberInvoiceGroupId: UUID? = null,
    val statusHistory: List<FirstPaymentScheduleStatusHistoryEntryModel> = emptyList(),
    val status: FirstPaymentScheduleStatus = FirstPaymentScheduleStatus.PENDING,
    val error: String? = null,
    val scheduledDate: LocalDate,
    override val version: Int = 0,
    override val createdAt: LocalDateTime = LocalDateTime.now(),
    override val updatedAt: LocalDateTime = LocalDateTime.now(),
    val updatedBy: UpdatedBy? = null
) : Table<FirstPaymentScheduleTable> {

    override fun copyTable(version: Int, updatedAt: LocalDateTime, createdAt: LocalDateTime) =
        copy(version = version, createdAt = createdAt, updatedAt = updatedAt)

}
