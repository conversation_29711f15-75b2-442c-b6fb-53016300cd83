package br.com.alice.data.layer.policies

import br.com.alice.data.layer.CLINICAL_ACCOUNT_ROOT_SERVICE_NAME
import br.com.alice.data.layer.authorization.Count
import br.com.alice.data.layer.authorization.Create
import br.com.alice.data.layer.authorization.Delete
import br.com.alice.data.layer.authorization.Update
import br.com.alice.data.layer.authorization.View
import br.com.alice.data.layer.authorization.policySet
import br.com.alice.data.layer.models.BeneficiaryModel
import br.com.alice.data.layer.models.BeneficiaryOnboardingModel
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhaseModel
import br.com.alice.data.layer.models.DeviceModel
import br.com.alice.data.layer.models.FeatureConfigModel
import br.com.alice.data.layer.models.HealthcareAdditionalTeam
import br.com.alice.data.layer.models.HealthcareTeamModel
import br.com.alice.data.layer.models.MemberModel
import br.com.alice.data.layer.models.PersonClinicalAccount
import br.com.alice.data.layer.models.PersonClinicalAccountHistory
import br.com.alice.data.layer.models.PersonInternalReference
import br.com.alice.data.layer.models.PersonModel
import br.com.alice.data.layer.models.PersonTeamAssociation
import br.com.alice.data.layer.models.TrackPersonABModel

val clinicalAccountPolicySet = policySet {
    match("at clinical-account-domain", { rootService.name == CLINICAL_ACCOUNT_ROOT_SERVICE_NAME }) {
        allows(HealthcareTeamModel::class, View, Count)
        allows(HealthcareAdditionalTeam::class, View, Count)
        allows(PersonModel::class, View, Count)
        allows(PersonInternalReference::class, View, Count, Create)
        allows(PersonClinicalAccount::class, View, Count, Create, Update, Delete)
        allows(PersonClinicalAccountHistory::class, Create)
        allows(PersonTeamAssociation::class, View)
        allows(MemberModel::class, View)
        allows(BeneficiaryModel::class, View)
        allows(BeneficiaryOnboardingModel::class, View)
        allows(BeneficiaryOnboardingPhaseModel::class, View)
        allows(TrackPersonABModel::class, View, Count, Create)
        allows(FeatureConfigModel::class, View)
        allows(DeviceModel::class, View)
    }
}
