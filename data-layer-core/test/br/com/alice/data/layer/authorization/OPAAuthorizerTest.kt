package br.com.alice.data.layer.authorization

import br.com.alice.authentication.RootService
import br.com.alice.common.RangeUUID
import br.com.alice.common.RangeUUID.PERSON_ID_RANGE
import br.com.alice.common.convertTo
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.Role
import br.com.alice.common.core.Subject
import br.com.alice.common.serialization.ListOfModelIdTypeAdapter
import br.com.alice.common.serialization.gson
import br.com.alice.common.service.serialization.gsonOPA
import br.com.alice.data.layer.EHR_API_ROOT_SERVICE_NAME
import br.com.alice.data.layer.helpers.TestModelFactory
import com.google.gson.JsonObject
import com.google.gson.annotations.Expose
import com.google.gson.annotations.JsonAdapter
import com.typesafe.config.ConfigFactory
import io.gsonfire.annotations.ExposeMethodResult
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.HttpRequestData
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.server.config.HoconApplicationConfig
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

class OPAAuthorizerTest {

    data class SomeSubject(
        val id: UUID,
        @Expose
        val email: String,
        @Expose
        val role: Role,
        @Expose
        val type: SomeType,
        @Expose
        val active: Boolean = true,
        @Expose
        @JsonAdapter(ListOfModelIdTypeAdapter::class)
        val children: List<SomeResource> = emptyList(),
        val notSent: String = "bababababa"
    ) : Subject

    data class SomeResource(
        override val id: UUID,
        @Expose
        val version: Int,
        @Expose
        val field: String,
        @Expose
        val type: SomeType,
        @Expose
        val personId: PersonId = PersonId(RangeUUID.generate(PERSON_ID_RANGE)),
        val notSent: String = "bababababa"
    ) : Model {
        @ExposeMethodResult("calculatedField")
        fun getSomeCalculatedField() = "calculatedFieldValue"
        @ExposeMethodResult("calculatedList")
        fun getSomeCalculatedList() = listOf(1, 2, 3)
    }

    enum class SomeType {
        TYPE1, TYPE2
    }

    private val someResource = SomeResource(
        RangeUUID.generate(),
        1,
        "field",
        SomeType.TYPE2
    )

    private val someSubject = SomeSubject(
        RangeUUID.generate(),
        "<EMAIL>",
        Role.MANAGER_PHYSICIAN,
        SomeType.TYPE1,
        children = listOf(someResource)
    )

    private val ehrEnvironment = RootService(EHR_API_ROOT_SERVICE_NAME)
    private val staffViewResource =
        AuthorizationRequest(someSubject, View, someResource, ehrEnvironment)
    private val staffCountResource =
        AuthorizationRequest(someSubject, Count, null, ehrEnvironment, SomeResource::class)

    @Test
    fun `#toOPAAuthorizationRequest converts AuthorizationRequest to OPA request with Exposed and UUID fields`() {
        val opaRequest = staffViewResource.toOPAAuthorizationRequest()

        val expectedJson = """
            {
            "cases": [
                {
                    "index": 0,
                    "action":"view",
                    "subject":{
                        "id":"${someSubject.id}",
                        "email":"<EMAIL>",
                        "role":"MANAGER_PHYSICIAN",
                        "type":"TYPE1",
                        "active":true,
                        "children":["${someResource.id}"],
                        "opaType":"SomeSubject"
                    },
                    "resource":{
                        "id":"${someResource.id}",
                        "version":1,
                        "field":"field",
                        "type":"TYPE2",
                        "personId":"${someResource.personId}",
                        "calculatedField":"calculatedFieldValue",
                        "calculatedList":[1,2,3],
                        "opaType":"SomeResource",
                        "opaSuperTypes": ["Model"]
                    }
                }
            ]
            }""".trimIndent()

        assertEquals(
            mapOf("input" to gsonOPA.fromJson(expectedJson, JsonObject::class.java)),
            opaRequest
        )
    }

    @Test
    fun `#toOPAAuthorizationRequest converts AuthorizationRequest to OPA request with null resource`() {
        val opaRequest = staffCountResource.toOPAAuthorizationRequest()

        val expectedJson = """
            {
            "cases": [
                {
                    "index": 0,
                    "action":"count",
                    "subject":{
                        "id":"${someSubject.id}",
                        "email":"<EMAIL>",
                        "role":"MANAGER_PHYSICIAN",
                        "type":"TYPE1",
                        "active":true,
                        "children":["${someResource.id}"],
                        "opaType":"SomeSubject"
                    },
                    "resource":{
                        "opaType":"SomeResource",
                        "opaSuperTypes": ["Model"]
                    }
                }
            ]
            }""".trimIndent()

        assertEquals(
            mapOf("input" to gsonOPA.fromJson(expectedJson, JsonObject::class.java)),
            opaRequest
        )
    }

    @Test
    fun `#toOPAAuthorizationRequest converts AuthorizationRequest to OPA request with correct  super types`() {
        val riskResource = TestModelFactory.buildRisk()
        val someSubject = someSubject.copy(children = listOf(someResource.copy(id = riskResource.id)))
        val opaRequest = AuthorizationRequest(someSubject, View, riskResource, ehrEnvironment).toOPAAuthorizationRequest()

        val expectedJson = """
            {
            "cases": [
                {
                    "index": 0,
                    "action":"view",
                    "subject":{
                        "id":"${someSubject.id}",
                        "email":"<EMAIL>",
                        "role":"MANAGER_PHYSICIAN",
                        "type":"TYPE1",
                        "active":true,
                        "children":["${riskResource.id}"],
                        "opaType":"SomeSubject"
                    },
                    "resource":{
                        "personId":"${riskResource.personId}",
                        "id":"${riskResource.id}",
                        "opaType":"Risk",
                        "opaSuperTypes": ["Model", "PersonReference", "HealthInformation"]
                    }
                }
            ]
            }""".trimIndent()

        assertEquals(
            mapOf("input" to gsonOPA.fromJson(expectedJson, JsonObject::class.java)),
            opaRequest
        )
    }

    @Test
    fun `#authorize sends authz request to OPA server with correct data`(): Unit = runBlocking {
        val httpClient = httpClientMock("{\"result\":[0]}") { request -> runBlocking {
            assertEquals(HttpMethod.Post, request.method)
            assertEquals("http://localhost:8181/v1/data/app/ehr_api/allow", request.url.toString())
            assertEquals(gson.toJson(staffViewResource.toOPAAuthorizationRequest()), request.body.toByteArray().toString(Charsets.UTF_8))
            true
        }}

        val opaAuthorizer = OPAAuthorizer(opaConfigMock(), httpClient)
        val result = opaAuthorizer.authorize(staffViewResource)

        assertThat(result).isEqualTo(Authorization(true, "$staffViewResource"))
    }

    @Test
    fun `#authorize considers authz denied if response body does not contain indexes`(): Unit = runBlocking {
        val httpClient = httpClientMock("{\"result\":[]}") { request -> runBlocking {
            assertEquals(HttpMethod.Post, request.method)
            assertEquals("http://localhost:8181/v1/data/app/ehr_api/allow", request.url.toString())
            assertEquals(gson.toJson(staffViewResource.toOPAAuthorizationRequest()), request.body.toByteArray().toString(Charsets.UTF_8))
            true
        }}

        val opaAuthorizer = OPAAuthorizer(opaConfigMock(), httpClient)
        val result = opaAuthorizer.authorize(staffViewResource)

        assertThat(result).isEqualTo(Authorization(false, "$staffViewResource"))
    }

    @Test
    fun `#authorize throws HttpClient generated exception`(): Unit = runBlocking {
        val httpClient = httpClientMock("true") { _ -> false}

        val opaAuthorizer = OPAAuthorizer(opaConfigMock(), httpClient)
        assertFailsWith(IllegalStateException::class) {opaAuthorizer.authorize(staffViewResource)}
    }

    private fun opaConfigMock() = OPAConfiguration(
        HoconApplicationConfig(
            ConfigFactory.parseString(
            """
            opa {
                host = "http://localhost"
                port = "8181"
                namespace = "app"
                defaultDecision = "allow"
            }
            """.trimIndent()
        ))
    )

    private fun httpClientMock(
        responseContent: String,
        statusCode: HttpStatusCode = HttpStatusCode.OK,
        requestMatcher: (request: HttpRequestData) -> Boolean,
    ): HttpClient {
        return HttpClient(MockEngine) {
            install(ContentNegotiation)
            engine {
                addHandler { request ->
                    if (requestMatcher(request)) {
                        respond(
                            responseContent,
                            statusCode
                        )
                    } else {
                        error("invalid request")
                    }
                }
            }
        }
    }
}
