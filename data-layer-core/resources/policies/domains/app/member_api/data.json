{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["InvoicePaymentModel", "BolepixPaymentDetailModel", "BoletoPaymentDetailModel", "PixPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "PaymentDetailModel", "BillingAccountablePartyModel", "Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "UpdateAppRuleModel", "BeneficiaryOnboardingPhaseModel", "Lead", "Opportunity", "HealthProductSimulation", "MemberProductChangeScheduleModel", "AliceAgoraWorkingHours", "StaffModel", "ZendeskExternalReference"], "actions": ["view"]}, {"resources": ["PersonModel"], "actions": ["count"]}, {"resources": ["LegalGuardianInfoTempModel", "PersonOnboardingModel"], "actions": ["view", "create", "update"]}], "branches": [{"conditions": ["${resource.id} == ${subject.key} || ${resource.nationalId} == ${subject.key}"], "allow": [{"resources": ["PersonModel"], "actions": ["view"]}]}, {"conditions": ["${resource.personId} == ${subject.key}"], "allow": [{"resources": ["MemberModel", "DeviceModel"], "actions": ["view"]}, {"resources": ["PersonInternalReference", "HealthDeclaration", "PersonRegistrationModel"], "actions": ["view", "create", "update"]}]}, {"conditions": ["${resource.channelPersonId} == ${subject.key}"], "allow": [{"resources": ["PersonInternalReference"], "actions": ["view"]}]}, {"conditions": ["${resource.nationalId} == ${subject.key} || ${resource.email} == ${subject.key} || ${resource.personId} == ${subject.key}"], "allow": [{"resources": ["PersonLoginModel", "PersonModel"], "actions": ["view", "update", "create"]}]}]}, {"conditions": ["${subject.opaType} == PersonSubject"], "allow": [{"resources": ["AppointmentScheduleOptionModel", "AppContentScreenDetail", "HealthCommunitySpecialistModel", "Beneficiary<PERSON><PERSON>l", "HealthFormSection", "HealthFormQuestion", "HealthFormQuestionAnswer", "LegalGuardianInfoTempModel", "ClinicalOutcomeRecord", "ActionPlanTask", "DemandActionPlan"], "actions": ["count"]}, {"resources": ["HealthConditionGroup", "AppContentScreenDetail", "InvoicePaymentModel", "BoletoPaymentDetailModel", "SimpleCreditCardPaymentDetailModel", "PixPaymentDetailModel", "BolepixPaymentDetailModel", "InvoiceLiquidationModel", "ChannelFup", "PromoCodeModel", "ProviderModel", "ProviderUnitModel", "ConsolidatedAccreditedNetwork", "ConsolidatedRating", "StructuredAddress", "ContactModel", "HealthCommunitySpecialistModel", "HealthcareTeamModel", "StaffModel", "HealthProfessionalModel", "CassiSpecialistModel", "ProviderUnitTestCodeModel", "MedicalSpecialtyModel", "ProductModel", "ProductBundleModel", "ProviderTestCodeModel", "AliceAgoraWorkingHours", "AppointmentScheduleOptionModel", "HealthGoalModel", "TestCodeModel", "TestPreparationModel", "HealthForm", "HealthFormSection", "HealthFormQuestion", "HealthcareTeamRecommendationModel", "HealthcareTeamRecommendationRuleModel", "HealthcareTeamRecommendationRuleToRecommendationModel", "FaqGroupModel", "FaqContentModel", "HealthCondition", "PriceListingModel", "ProductPriceListingModel", "MemberProductPriceModel", "BillingAccountablePartyModel", "HealthMeasurementTypeModel", "FeatureConfigModel", "InsurancePortabilityHealthInsuranceModel", "CompanyModel", "Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "StaffScheduleModel", "ExternalCalendarEventModel", "HealthProductSimulation", "AppointmentScheduleEventTypeModel", "EventTypeProviderUnitModel", "AppointmentScheduleEventTypeDateExceptionModel", "LegalGuardianInfoTempModel", "MemberOnboardingTemplate", "MemberOnboardingAction", "MemberOnboardingStep", "UpdateAppRuleModel", "OutcomeConf", "PersonPreferencesModel", "PersonBillingAccountablePartyModel", "MemberInvoiceModel", "EmergencyRecommendation", "CoPaymentCostInfoModel", "RefundCostInfoModel", "CompanyRefundCostInfoModel", "Generic<PERSON><PERSON><PERSON><PERSON>", "CsatTemplate", "HealthPlanTaskTemplate", "AIAssistant", "ScreenData", "ServiceScriptAction", "ServiceScriptExecution", "HealthcareResourceModel"], "actions": ["view"]}, {"resources": ["LegalGuardianInfoTempModel", "AbTestSpecialistRecommendation"], "actions": ["create"]}, {"resources": ["MemberOnboardingCheckpoint", "GenerateExternalAttendancePa", "MemberOnboarding", "ScreeningNavigation"], "actions": ["view", "count", "create", "update"]}, {"resources": ["AppointmentScheduleModel", "BudNode", "Protocol", "ServiceScriptRelationship"], "actions": ["view", "count"]}, {"resources": ["ServiceScriptNavigation", "ServiceScriptNavigationGroup", "LegalGuardianInfoTempModel", "Opportunity", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel"], "actions": ["view", "create", "update"]}], "branches": [{"conditions": ["${resource.id} == ${subject.id} || ${resource.personId} == ${subject.id}"], "allow": [{"resources": ["PersonModel", "MemberModel", "HealthInformation", "AppointmentScheduleModel", "AppointmentCoordination", "MemberInvoiceModel", "PersonClinicalAccount", "LaboratoryTestResultModel", "ExternalHealthInformation", "PersonHealthEvent", "TestResultFeedback", "AliceTestResultBundle", "Risk", "MemberOnboardingCheckpoint", "MvAuthorizedProcedureModel", "ExternalAppointmentScheduleModel", "ServiceScriptNavigationGroup", "PersonGracePeriod", "TotvsGuiaModel"], "actions": ["view"]}, {"resources": ["VideoCall", "AppContentScreenDetail", "AppointmentScheduleCheckInModel"], "actions": ["view", "update"]}, {"resources": ["FileVault", "Channel"], "actions": ["view", "create"]}, {"resources": ["PersonTaskModel", "DeviceModel", "MemberModel", "HealthDeclaration", "HealthPlanTaskGroup", "HealthPlanTask", "ActionPlanTask", "DemandActionPlan", "PersonInternalReference", "PersonOnboardingModel", "PersonRegistrationModel", "ProductOrderModel", "OnboardingContractModel", "PersonHealthGoalModel", "PersonHealthcareTeamRecommendationModel", "TestResultFileModel", "InsurancePortabilityRequestModel", "InsurancePortabilityRequestFileModel", "LegalGuardianInfoTempModel", "HealthFormQuestionAnswer", "HealthFormAnswerGroup", "HealthPlan", "ClinicalBackground", "HealthMeasurementModel", "PersonAdditionalInfoModel", "AppointmentScheduleModel", "PersonDocumentsUploadModel", "PersonBenefitModel", "PersonCalendlyModel", "TrackPersonABModel", "FollowUpHistory", "MemberOnboardingCheckpoint", "PersonEligibilityDuquesa", "Csat", "RefundModel", "RefundFileModel", "FinancialDataModel", "LegalGuardianAssociationModel", "PersonModel", "StaffModel", "HealthCommunitySpecialistModel", "PersonIdentityValidationModel", "PersonClinicalAccount"], "actions": ["view", "create", "update"]}, {"resources": ["HealthFormQuestionAnswer"], "actions": ["delete"]}, {"resources": ["AccreditedNetworkFavorite"], "actions": ["view", "create", "update", "delete"]}]}, {"conditions": ["${resource.memberId} in ${subject.members}"], "allow": [{"resources": ["CassiMemberModel"], "actions": ["view"]}, {"resources": ["MemberContractModel", "MemberContractTermModel"], "actions": ["view", "update"]}]}, {"conditions": ["${resource.id} in ${subject.dependentPersons} || ${resource.personId} in ${subject.dependentPersons} || ${resource.memberId} in ${subject.dependentMembers}"], "allow": [{"resources": ["PersonModel", "MemberModel"], "actions": ["view"]}, {"resources": ["FileVault"], "actions": ["view", "create"]}, {"resources": ["HealthDeclaration"], "actions": ["view", "create", "update"]}, {"resources": ["MemberContractModel", "MemberContractTermModel"], "actions": ["view", "update"]}]}, {"conditions": ["${resource.id} == ${subject.parentPersonId}"], "allow": [{"resources": ["PersonModel"], "actions": ["view"]}]}, {"conditions": ["${resource.personId} == ${subject.parentPersonId}"], "allow": [{"resources": ["MemberModel", "PersonModel"], "actions": ["view"]}, {"resources": ["FinancialDataModel"], "actions": ["view", "create", "update"]}]}, {"conditions": ["${resource.personId} in ${subject.legalGuardianPersons} || ${resource.id} in ${subject.legalGuardianPersons}"], "allow": [{"resources": ["PersonModel"], "actions": ["view"]}, {"resources": ["FinancialDataModel"], "actions": ["view", "create", "update"]}]}, {"conditions": ["${resource.billingAccountablePartyId} == ${subject.billingAccountablePartyId}"], "allow": [{"resources": ["MemberInvoiceGroupModel"], "actions": ["view"]}, {"resources": ["InvoicePaymentModel"], "actions": ["create", "update"]}]}, {"conditions": ["${resource.staffId} == ${subject.id}"], "allow": [{"resources": ["HealthProfessionalModel"], "actions": ["view", "create", "update"]}]}, {"conditions": ["${resource.leadId} == ${subject.personLeadId}"], "allow": [{"resources": ["ShoppingCartModel"], "actions": ["view", "create", "update"]}]}, {"conditions": ["${resource.id} == ${subject.personLeadId} || ${resource.email} == ${subject.personEmail}"], "allow": [{"resources": ["Lead"], "actions": ["view", "create", "update"]}]}, {"conditions": ["${resource.personId} == null || ${resource.personId} == ${subject.id}"], "allow": [{"resources": ["FaqFeedbackModel"], "actions": ["view", "create", "update"]}]}, {"conditions": ["${resource.staffId} == null && ${resource.personId} == ${subject.id}"], "allow": [{"resources": ["PersonHealthEvent"], "actions": ["create", "update"]}]}]}], "aggregate": ["ProviderModel"]}