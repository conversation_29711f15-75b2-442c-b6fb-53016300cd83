{"rules": [{"conditions": ["${subject.opaType} == Unauthenticated"], "allow": [{"resources": ["HealthcareTeamModel", "HealthcareAdditionalTeam", "PersonModel"], "actions": ["view", "count"]}, {"resources": ["PersonInternalReference", "TrackPersonABModel"], "actions": ["view", "count", "create"]}, {"resources": ["PersonClinicalAccount"], "actions": ["view", "count", "create", "update", "delete"]}, {"resources": ["PersonClinicalAccountHistory"], "actions": ["create"]}, {"resources": ["PersonTeamAssociation", "MemberModel", "Beneficiary<PERSON><PERSON>l", "BeneficiaryOnboardingModel", "BeneficiaryOnboardingPhaseModel", "FeatureConfigModel", "DeviceModel"], "actions": ["view"]}]}]}