package app.clinical_account_domain_service_test

import rego.v1

import data.app.clinical_account_domain_service

test_unauth_view_models_allowed if {
    { 1, 2, 3, 4, 5, 6, 7} == clinical_account_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonTeamAssociation"
                },
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "MemberModel"
                },
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BeneficiaryModel"
                },
            },
            {
                "index": 4,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BeneficiaryOnboardingModel"
                },
            },
            {
                "index": 5,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "BeneficiaryOnboardingPhaseModel"
                },
            },
            {
                "index": 6,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "FeatureConfigModel"
                },
            },
            {
                "index": 7,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "DeviceModel"
                },
            },
        ]
    }
}

test_unauth_view_and_count_models_allowed if {
    { 1, 2, 3, 4 } == clinical_account_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthcareTeamModel"
                },
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "HealthcareAdditionalTeam"
                },
            },
            {
                "index": 3,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonModel"
                },
            },
            {
                "index": 4,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonInternalReference"
                },
            }
        ]
    }
}

test_unauth_view_and_count_and_create_models_allowed if {
    { 1, 2, 3 } == clinical_account_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "TrackPersonABModel"
                },
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "TrackPersonABModel"
                },
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "TrackPersonABModel"
                },
            }
        ]
    }
}

test_unauth_crud_models_allowed if {
    { 1, 2, 3, 4, 5 } == clinical_account_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                },
            },
            {
                "index": 2,
                "action": "count",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                },
            },
            {
                "index": 3,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                },
            },
            {
                "index": 4,
                "action": "update",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                },
            },
            {
                "index": 5,
                "action": "delete",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonClinicalAccount"
                },
            }
        ]
    }
}

test_unauth_create_models_allowed if {
    { 1 } == clinical_account_domain_service.allow with input as {
        "cases": [
            {
                "index": 1,
                "action": "create",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonClinicalAccountHistory"
                },
            },
            {
                "index": 2,
                "action": "view",
                "subject": {
                    "opaType": "Unauthenticated"
                },
                "resource": {
                    "opaType": "PersonClinicalAccountHistory"
                },
            }
        ]
    }
}
