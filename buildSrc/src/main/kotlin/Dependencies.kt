import org.gradle.api.artifacts.dsl.DependencyHandler

const val kotlinVersion="1.7"
const val kotlinStdlibVersion="1.7.21"

const val aliceAkinatorDomainClientVersion="0.0.1"
const val aliceAkinatorDomainServiceVersion="0.0.1"
const val aliceAppointmentDomainClientVersion="0.0.1"
const val aliceAppointmentDomainServiceVersion="0.0.1"
const val aliceCommonVersion="0.0.4"
const val aliceDragonRadarBffApiVersion="0.0.1"
const val aliceDragonRadarDomainClientVersion="0.0.1"
const val aliceDragonRadarDomainServiceVersion="0.0.1"
const val aliceDuquesaDomainClientVersion="0.0.1"
const val aliceDuquesaDomainServiceVersion="0.0.1"
const val aliceTestResultDomainClientVersion="0.0.1"
const val aliceTestResultDomainServiceVersion="0.0.1"
const val aliceDasaIntegrationClientVersion="0.0.1"
const val aliceDasaIntegrationServiceVersion="0.0.1"
const val aliceDataLayerVersion="0.0.1"
const val aliceEhrDomainServiceVersion="0.0.1"
const val aliceEhrDomainClientVersion="0.0.1"
const val aliceEhrApiVersion="0.0.1"
const val aliceFeatureConfigDomainServiceVersion="0.0.1"
const val aliceFeatureConfigDomainClientVersion="0.0.4"
const val aliceLimboApiVersion="0.0.1"
const val aliceMemberApiVersion="0.0.1"
const val aliceCommonTestsVersion="0.0.1"
const val aliceExampleApiVersion="0.0.1"
const val aliceServerless="0.0.1"
const val aliceNotificationVersion="0.0.1"
const val aliceHealthCareOpsApiVersion="0.0.1"
const val aliceMoneyInDomainServiceVersion="0.0.1"
const val aliceHaocIntegrationClientVersion="0.0.1"
const val aliceHaocIntegrationServiceVersion="0.0.1"
const val aliceHealthLogicDomainServiceVersion="0.0.1"
const val aliceHealthLogicDomainClientVersion="0.0.1"
const val aliceFleuryIntegrationServiceVersion="0.0.1"
const val aliceFleuryIntegrationClientVersion="0.0.1"
const val aliceDBIntegrationServiceVersion="0.0.1"
const val aliceDBIntegrationClientVersion="0.0.1"
const val aliceSystemOpsApiVersion="0.0.1"
const val aliceChannelBffApiVersion="0.0.1"
const val aliceChannelDomainClientVersion="0.0.1"
const val aliceChannelDomainServiceVersion="0.0.1"
const val aliceChannelEventConsumerVersion="0.0.1"
const val aliceFileVaultClientVersion="0.0.1"
const val aliceFileVaultServiceVersion="0.0.1"
const val aliceExecIndicatorDomainClientVersion="0.0.1"
const val aliceExecIndicatorDomainServiceVersion="0.0.1"
const val aliceExecIndicatorApiVersion="0.0.1"
const val aliceMembershipDomainClientVersion="0.0.1"
const val aliceMembershipDomainServiceVersion="0.0.1"
const val aliceEinsteinBffApiVersion="0.0.1"
const val aliceEinsteinIntegrationServiceVersion="0.0.1"
const val aliceEinsteinIntegrationClientVersion="0.0.1"
const val aliceEventApiVersion="0.0.1"
const val aliceWandaBffApiVersion="0.0.1"
const val aliceWandaDomainServiceVersion="0.0.1"
const val aliceWandaDomainClientVersion = "0.0.1"
const val aliceZendeskIntegrationClientVersion = "0.0.1"
const val aliceScheduleDomainServiceVersion = "0.0.1"
const val aliceScheduleDomainClientVersion="0.0.1"
const val aliceScheduleEventConsumerVersion="0.0.1"
const val aliceScreeningDomainClientVersion="0.0.1"
const val aliceScreeningDomainServiceVersion="0.0.1"
const val aliceMoneyInBffApiVersion="0.0.1"
const val aliceMemberWannabeApiVersion="0.0.1"
const val aliceSchedulerApiVersion="0.0.1"
const val aliceOnboardingDomainClientVersion="0.0.1"
const val aliceOnboardingDomainServiceVersion="0.0.1"
const val aliceProductDomainClientVersion="0.0.1"
const val aliceProductDomainServiceVersion="0.0.1"
const val aliceProviderDomainServiceVersion="0.0.1"
const val aliceProviderDomainClientVersion="0.0.1"
const val aliceFhirDomainServiceVersion="0.0.1"
const val aliceFhirDomainClientVersion="0.0.1"
const val aliceFhirBffApiVersion="0.0.1"
const val aliceBusinessDomainClientVersion="0.0.1"
const val aliceBusinessDomainServiceVersion="0.0.1"
const val aliceBusinessPlatformBffVersion="0.0.1"
const val aliceHippocratesDomainServiceVersion="0.0.1"
const val aliceHippocratesDomainClientVersion="0.0.1"
const val aliceBatchCarvalhoDomainServiceVersion="0.0.1"
const val aliceBatchCarvalhoDomainClientVersion="0.0.1"
const val aliceQuestionnaireDomainClientVersion="0.0.1"
const val aliceQuestionnaireDomainServiceVersion="0.0.1"
const val aliceBottiniDomainClientVersion="0.0.1"
const val aliceBottiniDomainServiceVersion="0.0.1"
const val aliceHealthConditionDomainClientVersion="0.0.1"
const val aliceHealthConditionDomainServiceVersion="0.0.1"
const val aliceSherlockApiVersion="0.0.1"
const val aliceSherlockDomainServiceVersion="0.0.1"
const val aliceSherlockDomainClientVersion="0.0.1"
const val aliceSortingHatDomainServiceVersion="0.0.1"
const val aliceSortingHatDomainClientVersion="0.0.1"
const val aliceStaffDomainServiceVersion="0.0.1"
const val aliceStaffDomainClientVersion="0.0.1"
const val alicePersonDomainServiceVersion="0.0.1"
const val alicePersonDomainClientVersion="0.0.1"
const val aliceMaraudersMapDomainClientVersion="0.0.1"
const val aliceMaraudersMapDomainServiceVersion="0.0.1"
const val aliceCoverageDomainServiceVersion="0.0.1"
const val aliceCoverageDomainClientVersion="0.0.1"
const val aliceAmasDomainClientVersion="0.0.1"
const val aliceAmasDomainServiceVersion="0.0.1"
const val aliceAmasApiVersion="0.0.1"
const val aliceEitaNullvsIntegrationServiceVersion="0.0.1"
const val aliceEventinderDomainServiceVersion="0.0.1"
const val aliceEventinderDomainClientVersion="0.0.1"
const val aliceActionPlanDomainClientVersion="0.0.1"
const val aliceActionPlanDomainServiceVersion="0.0.1"
const val aliceAppContentDomainClientVersion="0.0.1"
const val aliceAppContentDomainServiceVersion="0.0.1"
const val aliceHealthLogicsApiVersion="0.0.1"
const val aliceHealthPlanDomainClientVersion="0.0.1"
const val aliceHealthPlanDomainServiceVersion="0.0.1"
const val aliceDocumentSignerVersion="0.0.1"
const val aliceSecondaryAttentionDomainClientVersion="0.0.1"
const val aliceSecondaryAttentionDomainServiceVersion="0.0.1"
const val aliceHealthBffApiVersion="0.0.1"
const val aliceClinicalAccountDomainClientVersion="0.0.1"
const val aliceClinicalAccountDomainServiceVersion="0.0.1"
const val aliceHealthAnalyticsEventConsumerVersion="0.0.1"
const val aliceMemberOnboardingDomainClientVersion="0.0.1"
const val aliceMemberOnboardingDomainServiceVersion="0.0.1"
const val aliceEitaNullvsIntegrationClientVersion="0.0.1"
const val aliceNullvsIntegrationClientVersion="0.0.1"
const val aliceNullvsIntegrationServiceVersion="0.0.1"
const val aliceItauIntegrationClientVersion="0.0.1"
const val aliceItauIntegrationServiceVersion="0.0.1"
const val aliceEitaExternalApiVersion="0.0.1"
const val aliceTissDomainClientVersion="0.0.1"
const val aliceTissDomainServiceVersion="0.0.1"
const val aliceRefundDomainClientVersion="0.0.1"
const val aliceRefundDomainServiceVersion="0.0.1"
const val aliceSalesChannelDomainClientVersion="0.0.1"
const val aliceSalesChannelDomainServiceVersion="0.0.1"
const val aliceSalesChannelApiVersion="0.0.1"
const val aliceZendeskBffApiVersion="0.0.1"
const val aliceZendeskIntegrationServiceVersion="0.0.1"
const val aliceAtlasDomainClientVersion="0.0.1"
const val aliceAtlasDomainServiceVersion="0.0.1"
const val aliceBackofficeBffApiVersion="0.0.1"
const val aliceBudDomainClientVersion="0.0.1"
const val aliceBudDomainServiceVersion="0.0.1"
const val aliceBusinessRiskDomainClientVersion="0.0.1"
const val aliceBusinessRiskDomainServiceVersion="0.0.1"
const val aliceAcquisitionDomainClientVersion="0.0.1"
const val aliceAcquisitionDomainServiceVersion="0.0.1"
const val aliceHubspotIntegrationLibVersion="0.0.1"
const val aliceHrCoreDomainClientVersion="0.0.1"
const val aliceHrCoreDomainServiceVersion="0.0.1"

const val assertjVersion="3.20.2"
const val awsSdkVersion="2.17.19"
const val awsSecretsManagerVersion = "1.0.6"
const val awsVersion = "1.12.27"
const val awsChimeVersion = "2.20.121"
const val commonsCsvVersion="1.8"
const val commonsTextVersion="1.9"
const val commonsLangVersion="3.12.0"
const val kafkaVersion="3.7.0"
const val ktor2Version="2.3.0"
const val koinVersion="2.2.2"
const val koin3Version="3.4.0"
const val logbackVersion="1.5.15"
const val firebaseAdminVersion="8.0.0"
const val firestoreVersion="2.6.1"
const val flyingSaucerVersion="9.1.21"
const val flywayVersion="6.3.0"
const val fuzzyWuzzyVersion="1.2.0"
const val googleAutoServiceVersion = "1.0"
const val grpcVersion = "1.39.0"
const val gsonExtrasVersion="2.10.1"
const val hikariVersion="4.0.3"
const val itextVersion="5.5.13"
const val thumbnailatorVersion="0.4.13"
const val jacksonFasterXmlVersion="2.12.4"
const val javaxMailVersion="1.4.7"
const val jmhVersion="1.34"
const val jdbiVersion="3.10.1"
const val jSoupVersion="1.14.1"
const val kotlinpoetVersion="1.13.0"
const val kotlinCoroutinesVersion="1.6.0"
const val logstashLogbackEncoderVersion = "6.6"
const val mockkVersion="1.13.3"
const val pdfBoxVersion="2.0.24"
const val postgresqlVersion="42.2.23"
const val resultVersion="5.5.0"
const val opentelemetryVersion="1.17.0"
const val micrometerVersion="1.1.3"
const val embeddedPostgresContaineredVersion="1.1.0"
const val embeddedPostgresVersion="2.1.0"
const val embeddedPostgresBinariesVersion="13.2.0"
const val ical4jVersion = "2.2.0"
const val googleApiClientVersion = "1.33.2"
const val googleOauthClientJettyVersion = "1.33.1"
const val googleApiServicesCalendarVersion = "v3-rev411-1.25.0"
const val resilience4jVersion = "1.7.1"
const val segmentVersion = "1.6.2"
const val hapiFhirVersion = "5.7.4"
const val junitJupiterVersion = "5.6.0"
const val googleMapsVersion = "2.2.0"
const val jedisVersion = "4.2.3"

fun DependencyHandler.ktor2Dependencies() {
    listOf(
        "io.ktor:ktor-server-core:$ktor2Version",
        "io.ktor:ktor-server-auth:$ktor2Version",
        "io.ktor:ktor-server-netty:$ktor2Version",
        "io.ktor:ktor-server-content-negotiation:$ktor2Version",
        "io.ktor:ktor-client-core:$ktor2Version",
        "io.ktor:ktor-client-auth:$ktor2Version",
        "io.ktor:ktor-client-apache:$ktor2Version",
        "org.apache.httpcomponents:httpcore-nio:4.4.16", //https://www.notion.so/alicehealth/Picos-de-CPU-no-staff-domain-service-e514a0973ffd4770bc943a2f92152a71?pvs=4
        "io.ktor:ktor-serialization-gson:$ktor2Version",
        "io.ktor:ktor-http-jvm:$ktor2Version",
        "com.google.code.gson:gson:$gsonExtrasVersion",
        "io.gsonfire:gson-fire:1.9.0",
        "io.insert-koin:koin-ktor:$koin3Version",
        "io.ktor:ktor-client-content-negotiation:$ktor2Version",
        "org.reflections:reflections:0.9.12"
    ).forEach { add("implementation", it) }
    add("api","com.github.kittinunf.result:result:$resultVersion")
}

fun DependencyHandler.test2Dependencies() {
    listOf(
        "org.jetbrains.kotlin:kotlin-test-junit5:$kotlinStdlibVersion",
        "org.junit.jupiter:junit-jupiter-params:$junitJupiterVersion",
        "io.insert-koin:koin-test:$koin3Version",
        "org.assertj:assertj-core:$assertjVersion",
        "io.mockk:mockk:$mockkVersion",
        "io.ktor:ktor-server-test-host:$ktor2Version",
        "io.ktor:ktor-client-mock:$ktor2Version"
    ).forEach { add("testImplementation", it) }
}
