package br.com.alice.wanda.services.coordination

import br.com.alice.common.MvUtil
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentCoordination.CoordinatedType.COORDINATED
import br.com.alice.data.layer.models.AppointmentCoordination.CoordinatedType.UNCOORDINATED
import br.com.alice.data.layer.models.AppointmentCoordination.Type.EXAM
import br.com.alice.data.layer.models.Council
import br.com.alice.data.layer.models.ProfessionalIdentification
import br.com.alice.data.layer.models.ProfessionalSpecialty
import br.com.alice.data.layer.services.AppointmentCoordinationDataService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.events.ProcedureExecutedPayload
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.wanda.event.AppointmentCoordinationCreatedEvent
import br.com.alice.wanda.event.AppointmentCoordinationCreatedEventPayload
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.AfterTest
import kotlin.test.Test

class TertiaryAttentionTestRequestCoordinationServiceImplTest {
    private val appointmentCoordinationDataService: AppointmentCoordinationDataService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService = mockk()

    private val service = TertiaryAttentionTestRequestCoordinationServiceImpl(
        mvAuthorizedProcedureService,
        healthProfessionalService,
        appointmentCoordinationDataService,
        kafkaProducerService,
    )

    @AfterTest
    fun clear() {
        clearAllMocks()
        confirmVerified(
            mvAuthorizedProcedureService,
            healthProfessionalService,
            appointmentCoordinationDataService,
            kafkaProducerService,
        )
    }

    private val person = TestModelFactory.buildPerson()
    private val council: Council = Council("1234", State.PR)

    private val appointmentCoordination = TestModelFactory.buildAppointmentCoordination(
        personId = person.id
    )

    private val procedureExecuted = TestModelFactory.buildMvAuthorizedProcedure(
        personId = person.id,
        executionGroupId = RangeUUID.generate(),
        requestedByProfessional = ProfessionalIdentification(
            council = CouncilType.CRM,
            councilNumber = council.number,
            councilState = council.state,
            fullName = "Caio Luiz Salgado",
            specialty = ProfessionalSpecialty(
                id = 1,
                name = "Ortopedia"
            )
        )
    )

    private val examAppointmentCoordination = appointmentCoordination.copy(
        appointmentId = procedureExecuted.id,
        appointmentType = EXAM,
        coordinated = COORDINATED,
        healthPlanTaskId = null,
        appointmentScheduleId = null
    )

    @Test
    fun `#create - should insert exam coordinated when match health professional`() = runBlocking {
        coEvery {
            appointmentCoordinationDataService.findOneOrNull(
                queryEq {
                    where {
                        appointmentId.eq(procedureExecuted.id) and
                                appointmentType.eq(EXAM.name)
                    }
                }
            )
        } returns null

        coEvery {
            mvAuthorizedProcedureService.findByExecutionGroupIdAndProcedureId(
                procedureExecuted.executionGroupId!!,
                procedureExecuted.procedureId!!
            )
        } returns procedureExecuted.success()

        coEvery {
            healthProfessionalService.existsByCouncil(council)
        } returns true.success()

        coEvery {
            appointmentCoordinationDataService.add(
                match {
                    it == examAppointmentCoordination.copy(id = it.id, createdAt = it.createdAt)
                }
            )
        } returns examAppointmentCoordination.success()

        coEvery {
            kafkaProducerService.produce(
                match { event: AppointmentCoordinationCreatedEvent ->
                    event.payload == AppointmentCoordinationCreatedEventPayload(examAppointmentCoordination)
                }
            )
        } returns mockk()

        val result = service.create(
            ProcedureExecutedPayload(
                executionGroupId = procedureExecuted.executionGroupId!!,
                procedureId = procedureExecuted.procedureId!!,
                personId = procedureExecuted.personId,
                procedureType = MvUtil.TISS.EXAM,
                executorId = 1
            )
        )

        assertThat(result).isSuccessWithData(examAppointmentCoordination)

        coVerifyOnce { appointmentCoordinationDataService.findOneOrNull(any()) }
        coVerifyOnce { mvAuthorizedProcedureService.findByExecutionGroupIdAndProcedureId(any(), any()) }
        coVerifyOnce { healthProfessionalService.existsByCouncil(any()) }
        coVerifyOnce { appointmentCoordinationDataService.add(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#create - should insert exam uncoordinated when doesnt match any ALICE health professional`() = runBlocking {
        coEvery {
            appointmentCoordinationDataService.findOneOrNull(
                queryEq {
                    where {
                        appointmentId.eq(procedureExecuted.id) and
                                appointmentType.eq(EXAM.name)
                    }
                }
            )
        } returns null

        coEvery {
            mvAuthorizedProcedureService.findByExecutionGroupIdAndProcedureId(
                procedureExecuted.executionGroupId!!,
                procedureExecuted.procedureId!!
            )
        } returns procedureExecuted.success()

        coEvery {
            healthProfessionalService.existsByCouncil(council)
        } returns false.success()

        coEvery {
            appointmentCoordinationDataService.add(
                match {
                    it == examAppointmentCoordination.copy(
                        id = it.id,
                        createdAt = it.createdAt,
                        coordinated = UNCOORDINATED
                    )
                }
            )
        } returns examAppointmentCoordination.success()

        coEvery {
            kafkaProducerService.produce(
                match { event: AppointmentCoordinationCreatedEvent ->
                    event.payload == AppointmentCoordinationCreatedEventPayload(examAppointmentCoordination)
                }
            )
        } returns mockk()

        val result = service.create(
            ProcedureExecutedPayload(
                executionGroupId = procedureExecuted.executionGroupId!!,
                procedureId = procedureExecuted.procedureId!!,
                personId = procedureExecuted.personId,
                procedureType = MvUtil.TISS.EXAM,
                executorId = 1
            )
        )

        assertThat(result).isSuccessWithData(examAppointmentCoordination)

        coVerifyOnce { appointmentCoordinationDataService.findOneOrNull(any()) }
        coVerifyOnce { mvAuthorizedProcedureService.findByExecutionGroupIdAndProcedureId(any(), any()) }
        coVerifyOnce { healthProfessionalService.existsByCouncil(any()) }
        coVerifyOnce { appointmentCoordinationDataService.add(any()) }
        coVerifyOnce { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#create - ignore exam if duplicated`() = runBlocking {

        coEvery {
            mvAuthorizedProcedureService.findByExecutionGroupIdAndProcedureId(
                procedureExecuted.executionGroupId!!,
                procedureExecuted.procedureId!!
            )
        } returns procedureExecuted.success()

        coEvery {
            appointmentCoordinationDataService.findOneOrNull(queryEq {
                where {
                    appointmentId.eq(procedureExecuted.id) and
                            appointmentType.eq(EXAM.name)
                }
            })
        } returns appointmentCoordination

        val result = service.create(
            ProcedureExecutedPayload(
                executionGroupId = procedureExecuted.executionGroupId!!,
                procedureId = procedureExecuted.procedureId!!,
                personId = procedureExecuted.personId,
                procedureType = MvUtil.TISS.EXAM,
                executorId = 1
            )
        )

        assertThat(result).isFailureOfType(DuplicatedItemException::class)

        coVerifyOnce { appointmentCoordinationDataService.findOneOrNull(any()) }
        coVerifyOnce { mvAuthorizedProcedureService.findByExecutionGroupIdAndProcedureId(any(), any()) }
        coVerifyNone { healthProfessionalService.existsByCouncil(any()) }
        coVerifyNone { appointmentCoordinationDataService.add(any()) }
        coVerifyNone { kafkaProducerService.produce(any()) }
    }

}
