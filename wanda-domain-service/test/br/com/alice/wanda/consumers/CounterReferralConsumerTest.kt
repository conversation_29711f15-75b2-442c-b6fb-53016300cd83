package br.com.alice.wanda.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.DuplicatedItemException
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.common.Brand.ALICE
import br.com.alice.common.Brand.DUQUESA
import br.com.alice.data.layer.models.CounterReferralGenericTask
import br.com.alice.data.layer.models.CounterReferralTypeOfService.OUTPATIENT_PROCEDURE
import br.com.alice.data.layer.models.CounterReferralTypeOfService.SURGICAL_PROCEDURE
import br.com.alice.data.layer.models.HealthcareAdditionalTeamType
import br.com.alice.data.layer.models.NextStepPrimaryAttentionInfo
import br.com.alice.data.layer.models.NextStepPrimaryAttentionType
import br.com.alice.data.layer.models.NotOccurredReason
import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.person.client.MemberService
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.secondary.attention.events.CounterReferralCreatedEvent
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.HealthcareAdditionalTeamService
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.wanda.logics.ReferralPersonHealthEventLogic
import br.com.alice.wanda.logics.ReferralPersonHealthEventLogic.referralCounterReferralToPersonHealthEvent
import br.com.alice.wanda.services.coordination.SecondaryAttentionCoordinationServiceImpl
import br.com.alice.wanda.services.internal.InternalPersonHealthEventService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class CounterReferralConsumerTest : ConsumerTest() {

    private val internalPersonHealthEventService: InternalPersonHealthEventService = mockk()
    private val healthcareTeamService: HealthcareTeamService = mockk()
    private val coordinationService: SecondaryAttentionCoordinationServiceImpl = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val healthcareAdditionalTeamService: HealthcareAdditionalTeamService = mockk()
    private val memberService: MemberService = mockk()
    private val consumer = CounterReferralConsumer(
        coordinationService,
        healthProfessionalService,
        medicalSpecialtyService,
        healthcareAdditionalTeamService,
        memberService,
        healthcareTeamService,
        internalPersonHealthEventService
    )

    private val personHealthEvent = TestModelFactory.buildPersonHealthEvent()

    private val personId = PersonId()
    private val medicalSpecialty = TestModelFactory.buildMedicalSpecialty()
    private val staffId = RangeUUID.generate()
    private val healthProfessional = TestModelFactory.buildHealthProfessional(
        staffId = staffId,
        specialtyId = medicalSpecialty.id
    )
    private val healthPlanTask = TestModelFactory.buildHealthPlanTaskReferral()
    private val counterReferral = TestModelFactory.buildCounterReferral(
        personId = personId,
        staffId = staffId,
        referralId = healthPlanTask.id
    ).copy(
        nextStepPrimaryAttentionInfo = NextStepPrimaryAttentionInfo(
            type = NextStepPrimaryAttentionType.YES,
            recommendations = "recomendacoes para o time de saude"
        )
    )
    private val healthcareTeam = TestModelFactory.buildHealthcareTeam(
        digitalCareNurseStaffIds = listOf(RangeUUID.generate())
    )
    private val appointmentCoordination = TestModelFactory.buildAppointmentCoordination()

    private val carecoordGroup = TestModelFactory.buildHealthcareAdditionalTeam(
        type = HealthcareAdditionalTeamType.CARE_COORDINATION_NURSE
    )

    private val member = TestModelFactory.buildMember(personId = personId, brand = ALICE)

    @BeforeTest
    fun setup() {
        mockkObject(ReferralPersonHealthEventLogic)
    }

    @Test
    fun `#createCounterReferralPersonHealthEvent - send CR to primary care coord if neither surgical nor Duquesa`() = runBlocking {
        val counterReferral = counterReferral.copy(
            referrals = listOf(
                CounterReferralGenericTask(
                    description = "especialidade"
                )
            )
        )
        coEvery {
            healthcareTeamService.getHealthcareTeamByPerson(counterReferral.personId)
        } returns healthcareTeam.success()

        coEvery {
            referralCounterReferralToPersonHealthEvent(
                counterReferral,
                healthProfessional,
                medicalSpecialty,
                healthcareTeam,
                carecoordGroup.id
            )
        } returns personHealthEvent

        coEvery {
            internalPersonHealthEventService.upsertPersonHealthEvent(personHealthEvent)
        } returns personHealthEvent.success()

        coEvery {
            healthcareAdditionalTeamService.get(
                "f0765d21-aa44-40c4-94b5-548c75a72800".toUUID()
            )
        } returns carecoordGroup.success()

        coEvery {
            healthProfessionalService.findByStaffId(counterReferral.staffId)
        } returns healthProfessional.success()

        coEvery {
            medicalSpecialtyService.getById(healthProfessional.specialtyId!!)
        } returns medicalSpecialty.success()

        coEvery { memberService.getCurrent(counterReferral.personId) } returns member.success()

        val event = CounterReferralCreatedEvent(
            counterReferral
        )

        val result = consumer.createReferralCounterReferralPersonHealthEvent(event)

        assertThat(result).isSuccessOfType(PersonHealthEvent::class)

        coVerifyOnce { healthcareTeamService.getHealthcareTeamByPerson(any()) }
        coVerifyOnce { internalPersonHealthEventService.upsertPersonHealthEvent(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
        coVerifyOnce { medicalSpecialtyService.getById(any()) }
        coVerifyOnce { healthcareAdditionalTeamService.get(any()) }
        coVerifyOnce { memberService.getCurrent(any()) }
        verifyOnce {
            referralCounterReferralToPersonHealthEvent(
                any(),
                any(),
                any(),
                any(),
                any()
            )
        }
        confirmVerified(
            healthcareTeamService,
            ReferralPersonHealthEventLogic,
            internalPersonHealthEventService
        )
    }

    @Test
    fun `#createCounterReferralPersonHealthEvent - send CR to tertiary team either surgical or Alice`() = runBlocking {
        val counterReferral = counterReferral.copy(
            typeOfService = SURGICAL_PROCEDURE
        )
        coEvery {
            healthcareTeamService.getHealthcareTeamByPerson(counterReferral.personId)
        } returns healthcareTeam.success()

        coEvery {
            referralCounterReferralToPersonHealthEvent(
                counterReferral,
                healthProfessional,
                medicalSpecialty,
                healthcareTeam,
                carecoordGroup.id
            )
        } returns personHealthEvent

        coEvery {
            internalPersonHealthEventService.upsertPersonHealthEvent(personHealthEvent)
        } returns personHealthEvent.success()

        coEvery {
            healthcareAdditionalTeamService.get(
                "27ab2edc-9048-40d7-baf3-5f412f372e00".toUUID()
            )
        } returns carecoordGroup.success()

        coEvery {
            healthProfessionalService.findByStaffId(counterReferral.staffId)
        } returns healthProfessional.success()

        coEvery {
            medicalSpecialtyService.getById(healthProfessional.specialtyId!!)
        } returns medicalSpecialty.success()

        coEvery { memberService.getCurrent(counterReferral.personId) } returns member.success()

        val event = CounterReferralCreatedEvent(
            counterReferral
        )

        val result = consumer.createReferralCounterReferralPersonHealthEvent(event)

        assertThat(result).isSuccessOfType(PersonHealthEvent::class)

        coVerifyOnce { healthcareTeamService.getHealthcareTeamByPerson(any()) }
        coVerifyOnce { internalPersonHealthEventService.upsertPersonHealthEvent(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
        coVerifyOnce { medicalSpecialtyService.getById(any()) }
        coVerifyOnce { healthcareAdditionalTeamService.get(any()) }
        coVerifyOnce { memberService.getCurrent(any()) }
        verifyOnce {
            referralCounterReferralToPersonHealthEvent(
                any(),
                any(),
                any(),
                any(),
                any()
            )
        }
        confirmVerified(
            healthcareTeamService,
            ReferralPersonHealthEventLogic,
            internalPersonHealthEventService,
        )
    }

    @Test
    fun `#createCounterReferralPersonHealthEvent - ignores Alice CR if does not have referrals and surgery`() = runBlocking {
        val counterReferral = counterReferral.copy(
            typeOfService = OUTPATIENT_PROCEDURE,
            referrals = emptyList(),
            surgicalReferrals = emptyList(),
            nextStepPrimaryAttentionInfo = null
        )
        coEvery {
            healthcareTeamService.getHealthcareTeamByPerson(counterReferral.personId)
        } returns healthcareTeam.success()

        val event = CounterReferralCreatedEvent(
            counterReferral
        )

        val result = consumer.createReferralCounterReferralPersonHealthEvent(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { healthcareTeamService.getHealthcareTeamByPerson(any()) }
        confirmVerified(
            healthcareTeamService,
            ReferralPersonHealthEventLogic,
            internalPersonHealthEventService,
        )
    }

    @Test
    fun `#createCounterReferralPersonHealthEvent - send any duquesa CR to tertiaty team`() = runBlocking {
        val counterReferral = counterReferral.copy(
            typeOfService = OUTPATIENT_PROCEDURE,
            referrals = listOf(
                CounterReferralGenericTask(
                    description = "especialidade"
                )
            ),
            surgicalReferrals = emptyList(),
            nextStepPrimaryAttentionInfo = null
        )
        coEvery {
            healthcareTeamService.getHealthcareTeamByPerson(counterReferral.personId)
        } returns healthcareTeam.success()

        coEvery {
            referralCounterReferralToPersonHealthEvent(
                counterReferral,
                healthProfessional,
                medicalSpecialty,
                healthcareTeam,
                carecoordGroup.id
            )
        } returns personHealthEvent

        coEvery {
            internalPersonHealthEventService.upsertPersonHealthEvent(personHealthEvent)
        } returns personHealthEvent.success()

        coEvery {
            healthcareAdditionalTeamService.get(
                "27ab2edc-9048-40d7-baf3-5f412f372e00".toUUID()
            )
        } returns carecoordGroup.success()

        coEvery {
            healthProfessionalService.findByStaffId(counterReferral.staffId)
        } returns healthProfessional.success()

        coEvery {
            medicalSpecialtyService.getById(healthProfessional.specialtyId!!)
        } returns medicalSpecialty.success()

        coEvery {
            memberService.getCurrent(counterReferral.personId)
        } returns member.copy(brand = DUQUESA).success()

        val event = CounterReferralCreatedEvent(
            counterReferral
        )

        val result = consumer.createReferralCounterReferralPersonHealthEvent(event)

        assertThat(result).isSuccessOfType(PersonHealthEvent::class)

        coVerifyOnce { healthcareTeamService.getHealthcareTeamByPerson(any()) }
        coVerifyOnce { internalPersonHealthEventService.upsertPersonHealthEvent(any()) }
        coVerifyOnce { healthProfessionalService.findByStaffId(any()) }
        coVerifyOnce { medicalSpecialtyService.getById(any()) }
        coVerifyOnce { healthcareAdditionalTeamService.get(any()) }
        coVerifyOnce { memberService.getCurrent(any()) }
        verifyOnce {
            referralCounterReferralToPersonHealthEvent(
                any(),
                any(),
                any(),
                any(),
                any()
            )
        }
        confirmVerified(
            healthcareTeamService,
            ReferralPersonHealthEventLogic,
            internalPersonHealthEventService,
        )
    }

    @Test
    fun `#createReferralCounterReferralPersonHealthEvent should return null when counter referral has appointmentId`() = runBlocking {
        val event = CounterReferralCreatedEvent(
            counterReferral.copy(appointmentId = RangeUUID.generate())
        )
        coEvery {
            healthcareTeamService.getHealthcareTeamByPerson(counterReferral.personId)
        } returns healthcareTeam.success()

        val result = consumer.createReferralCounterReferralPersonHealthEvent(event)
        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { healthcareTeamService.getHealthcareTeamByPerson(any()) }
    }

    @Test
    fun `#createAppointmentCoordination - should crate appointment coordination by appointment`() = runBlocking {
        val event = CounterReferralCreatedEvent(counterReferral)

        coEvery { coordinationService.create(counterReferral) } returns appointmentCoordination.success()

        val result = consumer.createAppointmentCoordination(event)

        assertThat(result).isSuccessWithData(appointmentCoordination)

        coVerifyOnce { coordinationService.create(any()) }
    }

    @Test
    fun `#createAppointmentCoordination - should return error`() = runBlocking {
        val event = CounterReferralCreatedEvent(counterReferral)

        coEvery {
            coordinationService.create(counterReferral)
        } returns DuplicatedItemException("").failure()

        val result = consumer.createAppointmentCoordination(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { coordinationService.create(any()) }

    }

    @Test
    fun `#createAppointmentCoordination - not should create appointment coordination`() = runBlocking {
        val event = CounterReferralCreatedEvent(counterReferral.copy(notOccurredReason = NotOccurredReason.NO_SHOW))

        val result = consumer.createAppointmentCoordination(event)

        assertThat(result).isSuccessWithData(true)

        coVerifyNone { coordinationService.create(any()) }
    }

}
