package br.com.alice.wanda.services.coordination

import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.foldBoolean
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.data.layer.models.AppointmentCoordination
import br.com.alice.data.layer.models.Council
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.services.AppointmentCoordinationDataService
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.events.ProcedureExecutedPayload
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import java.util.UUID

class TertiaryAttentionTestRequestCoordinationServiceImpl(
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService,
    private val healthProfessionalService: HealthProfessionalService,
    appointmentCoordinationDataService: AppointmentCoordinationDataService,
    kafkaProducerService: KafkaProducerService
): CoordinationService<ProcedureExecutedPayload>(
    appointmentCoordinationDataService,
    kafkaProducerService
) {
    override suspend fun create(
        healthEvent: ProcedureExecutedPayload
    ): Result<AppointmentCoordination, Throwable> = Result.of {
        return createExam(
            healthEvent.executionGroupId,
            healthEvent.procedureId,
            healthEvent.personId)
    }

    override suspend fun update(
        appointmentCoordination: AppointmentCoordination,
        healthEvent: ProcedureExecutedPayload
    ): Result<AppointmentCoordination, Throwable> { throw NotImplementedError() }

    private suspend fun createExam(
        executionGroupId: UUID,
        procedureId: String,
        personId: PersonId
    ): Result<AppointmentCoordination, Throwable> =
        mvAuthorizedProcedureService.findByExecutionGroupIdAndProcedureId(
            executionGroupId,
            procedureId
        ).flatMap { procedure ->
            validateDuplicate(procedure.id, AppointmentCoordination.Type.EXAM)
            add(
                AppointmentCoordination(
                    personId = personId,
                    appointmentId = procedure.id,
                    appointmentType = AppointmentCoordination.Type.EXAM,
                    coordinated = defineExamCoordination(getCouncil(procedure)),
                    healthPlanTaskId = procedure.testRequestId,
                )
            )
        }

    private fun getCouncil(procedure: MvAuthorizedProcedure) = Council(
        procedure.requestedByProfessional.councilNumber,
        procedure.requestedByProfessional.councilState
    )

    private suspend fun defineExamCoordination(council: Council): AppointmentCoordination.CoordinatedType =
        healthProfessionalService.existsByCouncil(council)
            .foldBoolean(
                { AppointmentCoordination.CoordinatedType.COORDINATED },
                { AppointmentCoordination.CoordinatedType.UNCOORDINATED }
            )

}
