package br.com.alice.nullvs.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.nullvs.SERVICE_NAME
import br.com.alice.nullvs.models.NullvsMemberWebhookReceived

data class NullvsMemberWebhookEvent(val nullvsMemberWebhookReceived: NullvsMemberWebhookReceived) :
    NotificationEvent<NullvsMemberWebhookEvent.Payload>(
        producer = SERVICE_NAME,
        name = name,
        payload = Payload(nullvsMemberWebhookReceived),
    ) {
    companion object {
        const val name = "nullvs-member-webhook"
    }

    data class Payload(
        val webhook: NullvsMemberWebhookReceived,
    )
}
