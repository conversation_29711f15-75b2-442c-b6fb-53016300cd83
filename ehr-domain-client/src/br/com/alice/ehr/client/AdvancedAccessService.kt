package br.com.alice.ehr.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import com.github.kittinunf.result.Result

@RemoteService
interface AdvancedAccessService : Service {
    override val namespace get() = "ehr"
    override val serviceName get() = "advanced_access"

    suspend fun getAdvancedAccessDurationByPersonId(
        personId: PersonId
    ): Result<Int, Throwable>

    suspend fun getAdvancedAccessData(personId: PersonId) : Result<AdvancedAccessData, Throwable>
}

data class AdvancedAccessData(
    val durationInDays: Int,
    val isPediatric: Boolean,
)
