package br.com.alice.eventinder.services

import br.com.alice.common.core.extensions.isNotNullOrEmpty
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.extensions.thenError
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.CounterReferral
import br.com.alice.data.layer.models.EventReference
import br.com.alice.data.layer.models.EventReferenceModel
import br.com.alice.data.layer.models.HealthEventLocationEnum
import br.com.alice.data.layer.models.HealthEventOriginEnum
import br.com.alice.data.layer.models.HealthEventTypeEnum
import br.com.alice.data.layer.models.HealthEventsModel
import br.com.alice.eventinder.client.HealthEventsService
import br.com.alice.eventinder.converters.toModel
import br.com.alice.eventinder.converters.toTransport
import br.com.alice.eventinder.events.ProcessedHealthEventsEvent
import br.com.alice.eventinder.events.ProcessedHealthEventsPayload
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import java.util.UUID

class CounterReferralProcessService(
    private val healthEventsService: HealthEventsService,
    private val healthPlanTaskService: HealthPlanTaskService,
    private val healthProfessionalService: HealthProfessionalService,
    private val kafkaProducerService: KafkaProducerService,
) {

    suspend fun processCreateEvent(payload: CounterReferral): Result<HealthEventsModel, Throwable> {
        logger.info(
            "Creating HealthEvent from CounterReferralProcessService",
            "counter_referral" to payload
        )

        return if (payload.referralId == null) processWithoutReferralId(payload)
        else healthPlanTaskService.get(payload.referralId!!)
            .flatMapPair { task ->
                healthEventsService.findByOriginReference(
                    EventReference(id = task.id.toString(), location = HealthEventLocationEnum.REFERRAL)
                )
            }.flatMap { (healthEvent, task) ->
                healthEventsService.updateAndPublishEvent(
                    healthEvent.copy(
                        executedAt = task.finishedAt,
                        eventType = HealthEventTypeEnum.EXAM,
                        executionReferences = listOf(
                            EventReference(
                                id = payload.id.toString(),
                                location = HealthEventLocationEnum.COUNTER_REFERRAL
                            )
                        )
                    )
                ).map { it.toModel() }
            }.coFoldNotFound { persistHealthEvent(payload, logContext = "With referralId") }
    }

    private suspend fun processWithoutReferralId(payload: CounterReferral) =
        persistHealthEvent(payload, logContext = "Without referralId").then {
            if (payload.testRequests.isNotNullOrEmpty()) {
                logger.info(
                    "CounterReferralProcessService::processCreateEvent producing ProcessedHealthEventsEvent",
                    "healthEvent" to it
                )
                val processedHealthEventsPayload = ProcessedHealthEventsPayload(listOf(it.toTransport()))
                kafkaProducerService.produce(ProcessedHealthEventsEvent(processedHealthEventsPayload))
            }
        }

    private suspend fun persistHealthEvent(
        payload: CounterReferral,
        logContext: String
    ): Result<HealthEventsModel, Throwable> {
        val eventReferences = listOf(
            EventReferenceModel(id = payload.id.toString(), location = HealthEventLocationEnum.COUNTER_REFERRAL)
        )

        val healthEvents = HealthEventsModel(
            personId = payload.personId,
            eventType = HealthEventTypeEnum.EXAM,
            requestedAt = payload.appointmentDate.atStartOfDay(),
            originReferences = eventReferences,
            executionReferences = eventReferences,
            healthProfessionalId = findHealthProfessionalId(payload.staffId),
            procedureIds = (payload.testRequests?.mapNotNull { it.code } ?: emptyList()),
            origin = HealthEventOriginEnum.COUNTER_REFERRAL
        )

        return healthEventsService.addAndPublishEvent(healthEvents.toTransport()).map { it.toModel() }
            .then {
                logger.info(
                    "Created HealthEvent from CounterReferralProcessService",
                    "context" to logContext,
                    "healthEvent" to it
                )
            }.thenError {
                logger.error(
                    "Error while creating health event from CounterReferral",
                    "context" to logContext,
                    "error" to it
                )
            }
    }

    private suspend fun findHealthProfessionalId(staffId: UUID) =
        healthProfessionalService.findByStaffId(staffId).getOrNullIfNotFound()?.id
}
