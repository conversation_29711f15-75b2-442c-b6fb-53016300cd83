package br.com.alice.eventinder.services

import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.CounterReferralGenericTask
import br.com.alice.data.layer.models.CounterReferralTypeOfService
import br.com.alice.data.layer.models.EventReference
import br.com.alice.data.layer.models.HealthEventLocationEnum
import br.com.alice.eventinder.client.HealthEventsService
import br.com.alice.eventinder.converters.toModel
import br.com.alice.eventinder.events.ProcessedHealthEventsEvent
import br.com.alice.healthplan.client.HealthPlanTaskService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.TestInstance
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.Test
import kotlin.test.assertEquals

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CounterReferralProcessServiceTest {

    private val healthEventsService: HealthEventsService = mockk()
    private val healthPlanTaskService: HealthPlanTaskService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val personInternalReferenceService: PersonInternalReferenceService = mockk()
    private val service = CounterReferralProcessService(
        healthEventsService,
        healthPlanTaskService,
        healthProfessionalService,
        kafkaProducerService
    )

    private val staffId = RangeUUID.generate()
    private val payload = TestModelFactory.buildCounterReferral(
        staffId = staffId
    )
    private val healthEvent = TestModelFactory.buildHealthEvents()
    private val healthPlanTask = TestModelFactory.buildHealthPlanTask()
    private val healthProfessional = TestModelFactory.buildHealthProfessional(
        staffId = staffId
    )

    @AfterTest
    fun clear() = clearAllMocks()

    @Test
    fun `#processCreateEvent should create health event from counter referral without reference id - OUTPATIENT_PROCEDURE`() =
        runBlocking {

            coEvery {
                healthEventsService.addAndPublishEvent(any())
            } returns healthEvent.success()
            coEvery {
                personInternalReferenceService.getForPerson(any())
            } returns TestModelFactory.buildPersonInternalReference().success()

            coEvery {
                healthProfessionalService.findByStaffId(payload.staffId)
            } returns healthProfessional.success()

            val result = service.processCreateEvent(payload.copy(referralId = null))
            ResultAssert.assertThat(result).isSuccessWithData(healthEvent.toModel())

            coVerify(exactly = 1)
            { healthEventsService.addAndPublishEvent(any()) }

        }

    @Test
    fun `#processCreateEvent should create health event from counter referral without reference id and publish ProcessedHealthEventsEvent when it have test requests`() =
        runBlocking {

            coEvery {
                healthEventsService.addAndPublishEvent(any())
            } returns healthEvent.success()
            coEvery {
                personInternalReferenceService.getForPerson(any())
            } returns TestModelFactory.buildPersonInternalReference().success()

            coEvery {
                kafkaProducerService.produce(match {
                    it::class == ProcessedHealthEventsEvent::class
                })
            } returns ProducerResult(LocalDateTime.now(), "1", 1)

            coEvery {
                healthProfessionalService.findByStaffId(payload.staffId)
            } returns healthProfessional.success()

            val result = service.processCreateEvent(
                payload.copy(
                    referralId = null,
                    testRequests = listOf(
                        CounterReferralGenericTask(
                            description = "Exame 2",
                            code = "202030"
                        )
                    )
                )
            )
            ResultAssert.assertThat(result).isSuccessWithData(healthEvent.toModel())

            coVerify(exactly = 1) { healthEventsService.addAndPublishEvent(any()) }
            coVerify(exactly = 1) { kafkaProducerService.produce(any()) }

        }

    @Test
    fun `#processCreateEvent should create health event from counter referral without reference id - SURGICAL_PROCEDURE`() =
        runBlocking {

            coEvery {
                healthEventsService.addAndPublishEvent(any())
            } returns healthEvent.success()
            coEvery {
                personInternalReferenceService.getForPerson(any())
            } returns TestModelFactory.buildPersonInternalReference().success()

            coEvery {
                healthProfessionalService.findByStaffId(payload.staffId)
            } returns healthProfessional.success()

            val result = service.processCreateEvent(
                payload.copy(
                    referralId = null,
                    typeOfService = CounterReferralTypeOfService.SURGICAL_PROCEDURE
                )
            )
            ResultAssert.assertThat(result).isSuccessWithData(healthEvent.toModel())

            coVerify(exactly = 1) { healthEventsService.addAndPublishEvent(any()) }

        }

    @Test
    fun `#processCreateEvent should create health event from counter referral without reference id - UNKNOWN`() =
        runBlocking {

            coEvery {
                healthEventsService.addAndPublishEvent(any())
            } returns healthEvent.success()
            coEvery {
                personInternalReferenceService.getForPerson(any())
            } returns TestModelFactory.buildPersonInternalReference().success()

            coEvery {
                healthProfessionalService.findByStaffId(payload.staffId)
            } returns healthProfessional.success()

            val result = service.processCreateEvent(payload.copy(referralId = null, typeOfService = null))
            ResultAssert.assertThat(result).isSuccessWithData(healthEvent.toModel())

            coVerify(exactly = 1) { healthEventsService.addAndPublishEvent(any()) }

        }

    @Test
    fun `#processCreateEvent should update to executed health event from counter referral with reference id`() =
        runBlocking {

            coEvery {
                healthPlanTaskService.get(payload.referralId!!)
            } returns healthPlanTask.success()

            coEvery {
                healthEventsService.findByOriginReference(
                    EventReference(
                        id = healthPlanTask.id.toString(),
                        location = HealthEventLocationEnum.REFERRAL
                    )
                )
            } returns healthEvent.success()

            coEvery {
                healthEventsService.updateAndPublishEvent(any())
            } returns healthEvent.success()


            val result = service.processCreateEvent(payload)
            ResultAssert.assertThat(result).isSuccessWithData(healthEvent.toModel())
            assertEquals(healthPlanTask.finishedAt, result.get().executedAt)


            coVerify(exactly = 1) {
                healthPlanTaskService.get(any())
                healthEventsService.findByOriginReference(any())
                healthEventsService.updateAndPublishEvent(any())
            }

        }

    @Test
    fun `#processCreateEvent should create health event from counter referral when dont exists health plan task`() =
        runBlocking {

            coEvery {
                healthPlanTaskService.get(payload.referralId!!)
            } returns NotFoundException().failure()

            coEvery {
                healthEventsService.addAndPublishEvent(any())
            } returns healthEvent.success()
            coEvery {
                personInternalReferenceService.getForPerson(any())
            } returns TestModelFactory.buildPersonInternalReference().success()

            coEvery {
                healthProfessionalService.findByStaffId(payload.staffId)
            } returns healthProfessional.success()

            val result = service.processCreateEvent(payload)
            ResultAssert.assertThat(result).isSuccessWithData(healthEvent.toModel())


            coVerify(exactly = 1) {
                healthPlanTaskService.get(any())
                healthEventsService.addAndPublishEvent(any())
            }

            coVerify { healthEventsService.updateAndPublishEvent(any()) wasNot called }


        }

    @Test
    fun `#processCreateEvent should create health event from counter referral when dont exists health event`() =
        runBlocking {

            coEvery {
                healthPlanTaskService.get(payload.referralId!!)
            } returns healthPlanTask.success()

            coEvery {
                healthEventsService.findByOriginReference(
                    EventReference(
                        id = healthPlanTask.id.toString(),
                        location = HealthEventLocationEnum.REFERRAL
                    )
                )
            } returns NotFoundException().failure()

            coEvery {
                healthEventsService.addAndPublishEvent(any())
            } returns healthEvent.success()
            coEvery {
                personInternalReferenceService.getForPerson(any())
            } returns TestModelFactory.buildPersonInternalReference().success()

            coEvery {
                healthProfessionalService.findByStaffId(payload.staffId)
            } returns healthProfessional.success()

            val result = service.processCreateEvent(payload)
            ResultAssert.assertThat(result).isSuccessWithData(healthEvent.toModel())


            coVerify(exactly = 1) {
                healthPlanTaskService.get(any())
                healthEventsService.findByOriginReference(any())
                healthEventsService.addAndPublishEvent(any())
            }

            coVerify { healthEventsService.updateAndPublishEvent(any()) wasNot called }

        }

}
