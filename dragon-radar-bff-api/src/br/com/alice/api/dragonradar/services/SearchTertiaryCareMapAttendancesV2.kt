package br.com.alice.api.dragonradar.services

import br.com.alice.api.dragonradar.converters.TertiaryIntentionTouchPointToAttendanceConverter
import br.com.alice.api.dragonradar.converters.TertiaryIntentionTouchPointToTertiaryCareMapConverter
import br.com.alice.api.dragonradar.models.AttendanceType
import br.com.alice.api.dragonradar.models.LastAttendanceResponse
import br.com.alice.api.dragonradar.models.PaginatedTertiaryCareMapResponse
import br.com.alice.clinicalaccount.client.PersonInternalReferenceService
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.models.Staff
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.ehr.client.TertiaryIntentionTouchPointService
import br.com.alice.ehr.model.TertiaryIntentionTouchPointFilter
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.person.client.PersonService
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.StaffService
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.time.LocalDateTime
import java.util.UUID

class SearchTertiaryCareMapAttendancesV2(
    private val tertiaryIntentionService: TertiaryIntentionTouchPointService,
    private val personInternalReferenceService: PersonInternalReferenceService,
    private val providerUnitService: ProviderUnitService,
    private val healthProfessionalService: HealthProfessionalService,
    private val medicalSpecialtyService: MedicalSpecialtyService,
    private val personService: PersonService,
    private val healthcareResourceService: HealthcareResourceService,
    private val staffService: StaffService
) {

    suspend fun findAllInCurrentAttendance(
        filters: TertiaryIntentionTouchPointFilter,
        range: IntRange,
    ): PaginatedTertiaryCareMapResponse = coroutineScope {
        val tertiaryIntentionTouchPoint = tertiaryIntentionService.findAllInCurrentAttendance(filters, range).get()

        val ages = getAgePairAsync(tertiaryIntentionTouchPoint)
        val internalCodes = getInternalCodesPairAsync(tertiaryIntentionTouchPoint)
        val providerUnits = getProviderUnitPairAsync(tertiaryIntentionTouchPoint)
        val surgerySpecialties = getSpecialties(tertiaryIntentionTouchPoint)

        val total = async { tertiaryIntentionService.countAllInCurrent(filters).get() }
        val totalMoreThanThreeDays = async {
            tertiaryIntentionService.countAllInCurrent(filters.copy(stayInDays = 3)).get()
        }
        val attendancesWithAliceRear = async {
            tertiaryIntentionService.countAllInCurrent(filters.copy(hasRear = true)).get()
        }

        val tertiaryCareMapResponse = tertiaryIntentionTouchPoint.map {
            TertiaryIntentionTouchPointToTertiaryCareMapConverter.from(
                it,
                internalCodes.await(),
                providerUnits.await(),
                surgerySpecialties,
                ages.await()
            )
        }

        return@coroutineScope PaginatedTertiaryCareMapResponse(
            data = tertiaryCareMapResponse,
            totalAttendances = total.await(),
            totalStayMoreThanThreeDays = totalMoreThanThreeDays.await(),
            attendancesWithAliceRear = attendancesWithAliceRear.await()
        )
    }

    suspend fun findTertiaryIntentionTouchPointById(id: UUID) = coroutineScope {
        val tertiaryIntentionTouchPoint = tertiaryIntentionService.get(id).get()

        val procedures = getProceduresPairAsync(listOf(tertiaryIntentionTouchPoint))
        val providerUnits = getProviderUnitPairAsync(listOf(tertiaryIntentionTouchPoint))
        val staffs = getStaffPairAsync(listOf(tertiaryIntentionTouchPoint))
        val lastAttendances = getLastAttendancesByPersonId(tertiaryIntentionTouchPoint.personId)

        return@coroutineScope TertiaryIntentionTouchPointToAttendanceConverter.from(
            tertiaryIntentionTouchPoint,
            providerUnits.await(),
            procedures.await(),
            staffs.await(),
            lastAttendances
        )
    }

    private suspend fun getAgePairAsync(
        tertiaryIntentionTouchPoint: List<TertiaryIntentionTouchPoint>
    ) = coroutineScope {
        val personIds = tertiaryIntentionTouchPoint.map { it.personId.toString() }
        return@coroutineScope async {
            personService.findByIds(personIds.distinct()).get()
                .associateBy({ it.id }, { it.age })
        }
    }

    private suspend fun getInternalCodesPairAsync(
        tertiaryIntentionTouchPoint: List<TertiaryIntentionTouchPoint>
    ) = coroutineScope {
        val personIds = tertiaryIntentionTouchPoint.map { it.personId }
        return@coroutineScope async {
            personInternalReferenceService.getByPersonIds(personIds.distinct()).get()
                .associateBy({ it.personId }, { it.internalCode })
        }
    }

    private suspend fun getProviderUnitPairAsync(
        tertiaryIntentionTouchPoint: List<TertiaryIntentionTouchPoint>
    ) = coroutineScope {
        val provideUnitIds = tertiaryIntentionTouchPoint.mapNotNull { it.providerUnitId }

        return@coroutineScope async {
            providerUnitService.getByIds(provideUnitIds.distinct()).get()
                .associateBy({ it.id }, { it.name })
        }
    }

    private suspend fun getProceduresPairAsync(
        tertiaryIntentionTouchPoint: List<TertiaryIntentionTouchPoint>
    ) = coroutineScope {
        val procedures = tertiaryIntentionTouchPoint.flatMap {
            it.hospitalizationProcedures.map { procedure -> procedure.procedureCode }
        }

        return@coroutineScope async {
            healthcareResourceService.findByCodes(procedures).get()
                .associateBy { it.code }
        }
    }

    private suspend fun getStaffPairAsync(
        tertiaryIntentionTouchPoint: List<TertiaryIntentionTouchPoint>
    ) = coroutineScope {
        val staffIdsEvolution = tertiaryIntentionTouchPoint.flatMap { it.evolutions.map { e -> e.staffId } }
        val staffIdsNotification =
            tertiaryIntentionTouchPoint.flatMap { it.notifications.mapNotNull { e -> e.seen?.staffId } }
        val staffIds = staffIdsEvolution + staffIdsNotification
        if (staffIds.isEmpty()) return@coroutineScope async { emptyMap<UUID, Staff>() }
        return@coroutineScope async {
            staffService.findByList(staffIds.distinct()).get()
                .associateBy { it.id }
        }
    }

    private suspend fun getLastAttendancesByPersonId(personId: PersonId, numberOfDays: Long = 30) = coroutineScope {
        val finalDate = LocalDateTime.now()
        val initialDate = finalDate.minusDays(numberOfDays)

        return@coroutineScope tertiaryIntentionService.findByPersonIdAndBetweenDates(personId, initialDate, finalDate)
            .get()
            .map {
                LastAttendanceResponse(
                    it.id,
                    it.type?.let { type -> AttendanceType.from(type) },
                    it.startedAt
                )
            }
    }

    private suspend fun getSpecialties(tertiaryIntentionTouchPoint: List<TertiaryIntentionTouchPoint>): Map<UUID, String> =
        coroutineScope {
            val specialistsIds = tertiaryIntentionTouchPoint.mapNotNull { it.surgeonSpecialistId }.distinct()

            val specialists = healthProfessionalService.getByStaffIds(specialistsIds).get()
                .associateBy({ it.staffId }, { it.specialtyId })

            val specialtyIds = specialists.values.filterNotNull()
            val medicalSpecialties = medicalSpecialtyService.getByIds(specialtyIds).get()
                .associateBy({ it.id }, { it.name })

            return@coroutineScope specialists.entries.associate { Pair(it.key, medicalSpecialties[it.value] ?: "") }
        }
}
