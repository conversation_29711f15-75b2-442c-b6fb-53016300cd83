package br.com.alice.schedule.consumers

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.featureflag.withFeatureFlags
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.communication.crm.analytics.AnalyticsEvent
import br.com.alice.communication.crm.analytics.AnalyticsEventName.TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_15_MINUTES_BEFORE
import br.com.alice.communication.crm.analytics.AnalyticsEventName.TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_1_HOUR_BEFORE
import br.com.alice.communication.crm.analytics.AnalyticsEventName.TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_24_HOURS_BEFORE
import br.com.alice.communication.crm.analytics.AnalyticsEventName.TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_24_HOURS_BEFORE_NOT_MT
import br.com.alice.communication.crm.analytics.AnalyticsEventName.TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_2_HOURS_BEFORE
import br.com.alice.communication.crm.analytics.AnalyticsEventName.TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_48_HOURS_BEFORE
import br.com.alice.communication.crm.analytics.AnalyticsEventName.TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_72_HOURS_BEFORE
import br.com.alice.communication.crm.analytics.AnalyticsTrackerResult
import br.com.alice.communication.crm.analytics.CrmAnalyticsTracker
import br.com.alice.communication.crm.analytics.UserProfile
import br.com.alice.communication.treble_whatsapp.TrebleDeploymentResponse
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentReminderNotificationTime.FIFTEEN_MINUTES_BEFORE
import br.com.alice.data.layer.models.AppointmentReminderNotificationTime.ONE_DAY_BEFORE
import br.com.alice.data.layer.models.AppointmentReminderNotificationTime.ONE_HOUR_BEFORE
import br.com.alice.data.layer.models.AppointmentReminderNotificationTime.THREE_DAYS_BEFORE
import br.com.alice.data.layer.models.AppointmentReminderNotificationTime.TWO_DAYS_BEFORE
import br.com.alice.data.layer.models.AppointmentReminderNotificationTime.TWO_HOURS_BEFORE
import br.com.alice.data.layer.models.AppointmentScheduleCheckIn
import br.com.alice.data.layer.models.AppointmentScheduleCheckInStatus
import br.com.alice.data.layer.models.AppointmentScheduleStatus
import br.com.alice.data.layer.models.AppointmentScheduleType
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.person.client.PersonService
import br.com.alice.schedule.client.AppointmentScheduleCheckInService
import br.com.alice.schedule.client.AppointmentScheduleFilter
import br.com.alice.schedule.client.AppointmentScheduleService
import br.com.alice.schedule.model.events.AppointmentReminderSentEvent
import br.com.alice.schedule.model.events.SendAppointmentScheduleNotificationEvent
import br.com.alice.schedule.model.events.SendAppointmentScheduleNotificationsEvent
import br.com.alice.schedule.services.internal.AppointmentReminderService
import br.com.alice.schedule.services.internal.TrebleWhatsAppService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeAll
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class AppointmentScheduleNotificationConsumerTest : ConsumerTest() {

    companion object {
        val dateTime = LocalDateTime.now()
        val uuid = RangeUUID.generate()

        @BeforeAll
        @JvmStatic
        fun classSetup() {
            unmockkAll()
        }
    }

    private val appointmentScheduleService: AppointmentScheduleService = mockk()
    private val crmAnalyticsTracker: CrmAnalyticsTracker = mockk()
    private val personService: PersonService = mockk()
    private val trebleWhatsAppService: TrebleWhatsAppService = mockk()
    private val staffService: StaffService = mockk()
    private val appointmentReminderService: AppointmentReminderService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val appointmentScheduleCheckInService: AppointmentScheduleCheckInService = mockk()

    private val consumer = AppointmentScheduleNotificationConsumer(
        appointmentScheduleService,
        crmAnalyticsTracker,
        personService,
        trebleWhatsAppService,
        staffService,
        appointmentReminderService,
        kafkaProducerService,
        appointmentScheduleCheckInService
    )

    private val personId = PersonId()
    private val person = TestModelFactory.buildPerson(personId = personId, phoneNumber = "123")
    private val staffId = RangeUUID.generate()
    private val staff = TestModelFactory.buildStaff(id = staffId)
    private val appointmentSchedule = TestModelFactory.buildAppointmentSchedule(personId = personId)

    @BeforeTest
    override fun before() {
        super.before()

        mockkStatic(LocalDateTime::class)
        mockkObject(RangeUUID)
        every { LocalDateTime.now() } returns dateTime
        every { RangeUUID.generate() } returns uuid
    }

    @AfterTest
    fun confirmMocks() = confirmVerified(
        appointmentScheduleService,
        crmAnalyticsTracker,
        personService,
        trebleWhatsAppService,
        staffService,
        appointmentReminderService,
        kafkaProducerService
    )

    fun appointmentScheduleCheckIn() = AppointmentScheduleCheckIn(
        appointmentScheduleId = appointmentSchedule.id,
        personId = personId,
        status = AppointmentScheduleCheckInStatus.SENT
    )

    fun analyticsEvent() = AnalyticsEvent(
        name = TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_1_HOUR_BEFORE,
        properties = mapOf(
            "event_type" to "HEALTH_DECLARATION",
            "event_date" to "Quarta-feira, 01 de janeiro de 2020",
            "event_time" to "07:10",
            "appointment_name" to "Primeira consulta com seu time de saúde",
            "event_location" to "Rua Rebouças, 3506",
            "event_health_professional_name" to "",
            "health_professional_profile_image_url" to ""
        )
    )

    @Test
    fun `#sendAppointmentScheduleNotifications should get appointment schedules for date and produce kafka messages to send events`() =
        runBlocking {
            val eventDate = LocalDateTime.now()

            val multiTeamAppointmentScheduleTypes = listOf(
                AppointmentScheduleType.NUTRITIONIST,
                AppointmentScheduleType.PSYCHOLOGIST,
                AppointmentScheduleType.PHYSICAL_EDUCATOR,
                AppointmentScheduleType.FOLLOW_UP_NUTRITIONIST,
                AppointmentScheduleType.FOLLOW_UP_PHYSICAL_EDUCATOR,
            )

            val appointmentScheduleOnNextDay =
                TestModelFactory.buildAppointmentSchedule(startTime = eventDate.plusDays(1))
            val appointmentScheduleOnNext2Days =
                TestModelFactory.buildAppointmentSchedule(startTime = eventDate.plusDays(2))
            val appointmentScheduleOnNext3Days =
                TestModelFactory.buildAppointmentSchedule(startTime = eventDate.plusDays(3))
            val appointmentScheduleOnNextHour =
                TestModelFactory.buildAppointmentSchedule(startTime = eventDate.plusHours(1))
            val appointmentScheduleOnNext2Hours =
                TestModelFactory.buildAppointmentSchedule(startTime = eventDate.plusHours(2))
            val appointmentScheduleOnNext15Minutes =
                TestModelFactory.buildAppointmentSchedule(startTime = eventDate.plusMinutes(15))

            val event = SendAppointmentScheduleNotificationsEvent(eventDate)

            coEvery {
                appointmentScheduleService.findBy(
                    AppointmentScheduleFilter(
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        startTimeRange = appointmentScheduleOnNextDay.startTime.minusMinutes(10) to appointmentScheduleOnNextDay.startTime.plusMinutes(
                            10
                        )
                    )
                )
            } returns listOf(appointmentScheduleOnNextDay).success()
            coEvery {
                appointmentScheduleService.findBy(
                    AppointmentScheduleFilter(
                        types = multiTeamAppointmentScheduleTypes,
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        startTimeRange = appointmentScheduleOnNext2Days.startTime.minusMinutes(10) to appointmentScheduleOnNext2Days.startTime.plusMinutes(
                            10
                        )
                    )
                )
            } returns listOf(appointmentScheduleOnNext2Days).success()
            coEvery {
                appointmentScheduleService.findBy(
                    AppointmentScheduleFilter(
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        startTimeRange = appointmentScheduleOnNextHour.startTime.minusMinutes(10) to appointmentScheduleOnNextHour.startTime.plusMinutes(
                            10
                        ),
                        providerUnitIdIsNull = true
                    )
                )
            } returns listOf(appointmentScheduleOnNextHour).success()
            coEvery {
                appointmentScheduleService.findBy(
                    AppointmentScheduleFilter(
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        startTimeRange = appointmentScheduleOnNext2Hours.startTime.minusMinutes(10) to appointmentScheduleOnNext2Hours.startTime.plusMinutes(
                            10
                        ),
                        providerUnitIdIsNull = false
                    )
                )
            } returns listOf(appointmentScheduleOnNext2Hours).success()

            coEvery {
                appointmentScheduleService.findBy(
                    AppointmentScheduleFilter(
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        startTimeRange = appointmentScheduleOnNext3Days.startTime.minusMinutes(10) to appointmentScheduleOnNext3Days.startTime.plusMinutes(
                            10
                        )
                    )
                )
            } returns listOf(appointmentScheduleOnNext3Days).success()

            coEvery {
                appointmentScheduleService.findBy(
                    AppointmentScheduleFilter(
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        startTimeRange = appointmentScheduleOnNext2Days.startTime.minusMinutes(10) to appointmentScheduleOnNext2Days.startTime.plusMinutes(
                            10
                        ),
                        providerUnitIdIsNull = true
                    )
                )
            } returns listOf(appointmentScheduleOnNext2Days).success()

            coEvery {
                appointmentScheduleService.findBy(
                    AppointmentScheduleFilter(
                        status = listOf(AppointmentScheduleStatus.SCHEDULED),
                        startTimeRange = appointmentScheduleOnNext15Minutes.startTime.minusMinutes(5) to appointmentScheduleOnNext15Minutes.startTime.plusMinutes(
                            5
                        ),
                        providerUnitIdIsNull = true
                    )
                )
            } returns listOf(appointmentScheduleOnNext15Minutes).success()

            coEvery {
                kafkaProducerService.produce(
                    SendAppointmentScheduleNotificationEvent(
                        appointmentSchedule = appointmentScheduleOnNext2Days,
                        analyticsEventName = TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_48_HOURS_BEFORE,
                        notificationTime = TWO_DAYS_BEFORE
                    )
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    SendAppointmentScheduleNotificationEvent(
                        appointmentSchedule = appointmentScheduleOnNextDay,
                        analyticsEventName = TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_24_HOURS_BEFORE,
                        notificationTime = ONE_DAY_BEFORE
                    )
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    SendAppointmentScheduleNotificationEvent(
                        appointmentSchedule = appointmentScheduleOnNextHour,
                        analyticsEventName = TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_1_HOUR_BEFORE,
                        notificationTime = ONE_HOUR_BEFORE
                    )
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    SendAppointmentScheduleNotificationEvent(
                        appointmentSchedule = appointmentScheduleOnNext2Hours,
                        analyticsEventName = TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_2_HOURS_BEFORE,
                        notificationTime = TWO_HOURS_BEFORE
                    )
                )
            } returns mockk()
            coEvery {
                kafkaProducerService.produce(
                    SendAppointmentScheduleNotificationEvent(
                        appointmentSchedule = appointmentScheduleOnNextDay,
                        analyticsEventName = TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_24_HOURS_BEFORE_NOT_MT,
                        notificationTime = ONE_DAY_BEFORE
                    )
                )
            } returns mockk()

            coEvery {
                kafkaProducerService.produce(
                    SendAppointmentScheduleNotificationEvent(
                        appointmentSchedule = appointmentScheduleOnNext3Days,
                        analyticsEventName = TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_72_HOURS_BEFORE,
                        notificationTime = THREE_DAYS_BEFORE
                    )
                )
            } returns mockk()

            coEvery {
                kafkaProducerService.produce(
                    SendAppointmentScheduleNotificationEvent(
                        appointmentSchedule = appointmentScheduleOnNext15Minutes,
                        analyticsEventName = TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_15_MINUTES_BEFORE,
                        notificationTime = FIFTEEN_MINUTES_BEFORE
                    )
                )
            } returns mockk()

            val result = consumer.sendAppointmentScheduleNotifications(event)
            assertThat(result).isSuccessWithData(true)

            coVerify(exactly = 7) { appointmentScheduleService.findBy(any()) }
            coVerify(exactly = 8) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#sendAppointmentScheduleNotification should send notification correctly`() = runBlocking {
        withFeatureFlags(
            FeatureNamespace.SCHEDULE,
            mapOf(
                "test_appointment_reminders" to mapOf("CONTROL" to 1.0, "A" to 0.0),
                "appointment_schedule_check_in_flow_enabled" to true
            )
        ) {
            val event = SendAppointmentScheduleNotificationEvent(
                appointmentSchedule,
                TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_1_HOUR_BEFORE,
                ONE_HOUR_BEFORE
            )
            val analyticsEvent = analyticsEvent()
            val appointmentScheduleCheckIn = appointmentScheduleCheckIn()

            coEvery { personService.get(id = personId, withUserType = false) } returns person.success()

            coEvery {
                crmAnalyticsTracker.updateUserProfileAndSendEvent(
                    person.nationalId,
                    UserProfile(abTestAppointmentReminder = "CONTROL"),
                    analyticsEvent
                )
            } returns AnalyticsTrackerResult(true)

            coEvery {
                appointmentScheduleCheckInService.upsert(
                    appointmentScheduleCheckIn
                )
            } returns appointmentScheduleCheckIn.success()

            coEvery {
                appointmentScheduleCheckInService.getByAppointmentScheduleId(appointmentSchedule.id)
            } returns appointmentScheduleCheckIn.success()

            val result = consumer.sendAppointmentScheduleNotification(event)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { crmAnalyticsTracker.updateUserProfileAndSendEvent(any(), any(), any()) }
            coVerifyOnce { appointmentScheduleCheckInService.upsert(any()) }
            coVerifyOnce { appointmentScheduleCheckInService.getByAppointmentScheduleId(any()) }
        }
    }

    @Test
    fun `#sendAppointmentScheduleNotification should send notification correctly when check in already confirmed`() =
        runBlocking {
            withFeatureFlags(
                FeatureNamespace.SCHEDULE,
                mapOf(
                    "test_appointment_reminders" to mapOf("CONTROL" to 1.0, "A" to 0.0),
                    "appointment_schedule_check_in_flow_enabled" to true
                )
            ) {
                val event = SendAppointmentScheduleNotificationEvent(
                    appointmentSchedule,
                    TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_1_HOUR_BEFORE,
                    ONE_HOUR_BEFORE
                )
                val analyticsEvent = analyticsEvent()
                val appointmentScheduleCheckIn = appointmentScheduleCheckIn()

                coEvery { personService.get(id = personId, withUserType = false) } returns person.success()

                coEvery {
                    crmAnalyticsTracker.updateUserProfileAndSendEvent(
                        person.nationalId,
                        UserProfile(abTestAppointmentReminder = "CONTROL"),
                        analyticsEvent
                    )
                } returns AnalyticsTrackerResult(true)

                coEvery {
                    appointmentScheduleCheckInService.getByAppointmentScheduleId(appointmentSchedule.id)
                } returns appointmentScheduleCheckIn.copy(status = AppointmentScheduleCheckInStatus.CONFIRMED).success()

                val result = consumer.sendAppointmentScheduleNotification(event)
                assertThat(result).isSuccessWithData(true)

                coVerifyOnce { personService.get(any()) }
                coVerifyOnce { crmAnalyticsTracker.updateUserProfileAndSendEvent(any(), any(), any()) }
                coVerifyOnce { appointmentScheduleCheckInService.getByAppointmentScheduleId(any()) }
            }
        }

    @Test
    fun `#sendAppointmentScheduleNotification should send notification correctly when check in FF is false`() =
        runBlocking {
            withFeatureFlags(
                FeatureNamespace.SCHEDULE,
                mapOf(
                    "test_appointment_reminders" to mapOf("CONTROL" to 1.0, "A" to 0.0),
                    "appointment_schedule_check_in_flow_enabled" to false
                )
            ) {
                val event = SendAppointmentScheduleNotificationEvent(
                    appointmentSchedule,
                    TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_1_HOUR_BEFORE,
                    ONE_HOUR_BEFORE
                )
                val analyticsEvent = analyticsEvent()
                val appointmentScheduleCheckIn = appointmentScheduleCheckIn()

                coEvery { personService.get(id = personId, withUserType = false) } returns person.success()

                coEvery {
                    crmAnalyticsTracker.updateUserProfileAndSendEvent(
                        person.nationalId,
                        UserProfile(abTestAppointmentReminder = "CONTROL"),
                        analyticsEvent
                    )
                } returns AnalyticsTrackerResult(true)

                coEvery {
                    appointmentScheduleCheckInService.getByAppointmentScheduleId(appointmentSchedule.id)
                } returns appointmentScheduleCheckIn.success()

                val result = consumer.sendAppointmentScheduleNotification(event)
                assertThat(result).isSuccessWithData(true)

                coVerifyOnce { personService.get(any()) }
                coVerifyOnce { crmAnalyticsTracker.updateUserProfileAndSendEvent(any(), any(), any()) }
                coVerifyOnce { appointmentScheduleCheckInService.getByAppointmentScheduleId(any()) }
            }
        }

    @Test
    fun `#sendAppointmentScheduleNotification should send notification correctly and whatsapp message`() = runBlocking {
        withFeatureFlags(
            FeatureNamespace.SCHEDULE,
            mapOf(
                "test_appointment_reminders" to mapOf("C" to 1.0, "A" to 0.0),
                "whatsapp_appointment_reminder_flow_enabled" to true,
                "appointment_schedule_check_in_flow_enabled" to true
            )
        ) {
            val event = SendAppointmentScheduleNotificationEvent(
                appointmentSchedule,
                TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_1_HOUR_BEFORE,
                ONE_HOUR_BEFORE
            )
            val appointmentScheduleCheckIn = appointmentScheduleCheckIn()
            val analyticsEvent = analyticsEvent()

            val trebleDeploymentResponse = TrebleDeploymentResponse(
                message = "message",
                id = "id",
                batchId = "batchId",
                newLastScheduledDeployment = "newLastScheduledDeployment",
                conversationsId = listOf("conversationsId")
            )

            coEvery { personService.get(id = personId, withUserType = false) } returns person.success()
            coEvery {
                crmAnalyticsTracker.updateUserProfileAndSendEvent(
                    person.nationalId,
                    UserProfile(abTestAppointmentReminder = "C"),
                    analyticsEvent
                )
            } returns AnalyticsTrackerResult(true)

            coEvery {
                appointmentScheduleCheckInService.upsert(
                    appointmentScheduleCheckIn
                )
            } returns appointmentScheduleCheckIn.success()

            coEvery {
                trebleWhatsAppService.sendWhatsAppMessage(
                    phoneNumber = "123",
                    userSessionKeys = mapOf(
                        "name" to "Zé",
                        "data" to "01/01/2020",
                        "horario" to "07:10",
                        "dia_da_semana" to "quarta-feira"
                    ),
                    pollId = "123",
                    countryCode = "55"
                )
            } returns trebleDeploymentResponse.success()

            coEvery {
                appointmentReminderService.getForTimeAndAppointmentSchedule(
                    appointmentScheduleId = appointmentSchedule.id,
                    notificationTime = event.payload.notificationTime,
                )
            } returns NotFoundException().failure()

            coEvery { kafkaProducerService.produce(any<AppointmentReminderSentEvent>()) } returns mockk()

            coEvery {
                appointmentScheduleCheckInService.getByAppointmentScheduleId(appointmentSchedule.id)
            } returns appointmentScheduleCheckIn.success()

            val result = consumer.sendAppointmentScheduleNotification(event)
            assertThat(result).isSuccessWithData(true)

            coVerifyOnce { personService.get(any()) }
            coVerifyOnce { crmAnalyticsTracker.updateUserProfileAndSendEvent(any(), any(), any()) }
            coVerifyOnce { trebleWhatsAppService.sendWhatsAppMessage(any(), any(), any(), any()) }
            coVerifyOnce { appointmentReminderService.getForTimeAndAppointmentSchedule(any(), any()) }
            coVerifyOnce { kafkaProducerService.produce(any()) }
            coVerifyOnce { appointmentScheduleCheckInService.upsert(any()) }
            coVerifyOnce { appointmentScheduleCheckInService.getByAppointmentScheduleId(any()) }
        }
    }

    @Test
    fun `#sendAppointmentScheduleNotification should send notification correctly and not send whatsapp message if member opted out`() =
        runBlocking {
            withFeatureFlags(
                FeatureNamespace.SCHEDULE,
                mapOf(
                    "test_appointment_reminders" to mapOf("C" to 1.0, "A" to 0.0),
                    "whatsapp_appointment_reminder_opted_out_members" to "123",
                    "appointment_schedule_check_in_flow_enabled" to true
                )
            ) {
                val event = SendAppointmentScheduleNotificationEvent(
                    appointmentSchedule,
                    TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_1_HOUR_BEFORE,
                    ONE_HOUR_BEFORE
                )
                val appointmentScheduleCheckIn = appointmentScheduleCheckIn()
                val analyticsEvent = analyticsEvent()

                coEvery { personService.get(id = personId, withUserType = false) } returns person.success()

                coEvery {
                    crmAnalyticsTracker.updateUserProfileAndSendEvent(
                        person.nationalId,
                        UserProfile(abTestAppointmentReminder = "C"),
                        analyticsEvent
                    )
                } returns AnalyticsTrackerResult(true)

                coEvery {
                    appointmentScheduleCheckInService.upsert(
                        appointmentScheduleCheckIn
                    )
                } returns appointmentScheduleCheckIn.success()

                coEvery {
                    appointmentScheduleCheckInService.getByAppointmentScheduleId(appointmentSchedule.id)
                } returns appointmentScheduleCheckIn.success()

                val result = consumer.sendAppointmentScheduleNotification(event)
                assertThat(result).isSuccessWithData(true)

                coVerifyOnce { personService.get(any()) }
                coVerifyOnce { crmAnalyticsTracker.updateUserProfileAndSendEvent(any(), any(), any()) }
                coVerifyOnce { appointmentScheduleCheckInService.upsert(any()) }
                coVerifyOnce { appointmentScheduleCheckInService.getByAppointmentScheduleId(any()) }
            }
        }

    @Test
    fun `#sendAppointmentScheduleNotification should send staff name if appointment schedule have it`() =
        runBlocking {
            withFeatureFlags(
                FeatureNamespace.SCHEDULE,
                mapOf(
                    "test_appointment_reminders" to mapOf("CONTROL" to 1.0, "A" to 0.0),
                    "appointment_schedule_check_in_flow_enabled" to true
                )
            ) {
                val event = SendAppointmentScheduleNotificationEvent(
                    appointmentSchedule.copy(staffId = staffId),
                    TRIGGER_APPOINTMENT_SCHEDULE_NOTIFICATION_1_HOUR_BEFORE,
                    ONE_HOUR_BEFORE
                )
                val appointmentScheduleCheckIn = appointmentScheduleCheckIn()
                val analyticsEvent = analyticsEvent()

                coEvery { personService.get(id = personId, withUserType = false) } returns person.success()

                coEvery { staffService.get(staffId) } returns staff.success()

                coEvery {
                    crmAnalyticsTracker.updateUserProfileAndSendEvent(
                        person.nationalId,
                        UserProfile(abTestAppointmentReminder = "CONTROL"),
                        analyticsEvent
                    )
                } returns AnalyticsTrackerResult(true)

                coEvery {
                    appointmentScheduleCheckInService.upsert(
                        appointmentScheduleCheckIn
                    )
                } returns appointmentScheduleCheckIn.success()

                coEvery {
                    appointmentScheduleCheckInService.getByAppointmentScheduleId(appointmentSchedule.id)
                } returns appointmentScheduleCheckIn.success()

                val result = consumer.sendAppointmentScheduleNotification(event)
                assertThat(result).isSuccessWithData(true)

                coVerifyOnce { personService.get(any()) }
                coVerifyOnce { staffService.get(any()) }
                coVerifyOnce { crmAnalyticsTracker.updateUserProfileAndSendEvent(any(), any(), any()) }
                coVerifyOnce { appointmentScheduleCheckInService.upsert(any()) }
                coVerifyOnce { appointmentScheduleCheckInService.getByAppointmentScheduleId(any()) }
            }
        }

}
