package br.com.alice.member.onboarding.services

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.data.dsl.matchers.queryEq
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.service.data.dsl.SortOrder
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.FeatureConfig
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.FeatureType
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType
import br.com.alice.data.layer.models.MemberOnboardingReferencedLink
import br.com.alice.data.layer.models.MemberOnboardingReferencedLinkModel
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.Step
import br.com.alice.data.layer.models.TrackPersonAB
import br.com.alice.data.layer.services.MemberOnboardingDataService
import br.com.alice.featureconfig.client.FeatureConfigService
import br.com.alice.featureconfig.client.TrackPersonABService
import br.com.alice.member.onboarding.model.OnboardingVersion
import br.com.alice.member.onboarding.notifier.MemberOnboardingCompletedEvent
import br.com.alice.member.onboarding.notifier.MemberOnboardingStepUpdatedEvent
import br.com.alice.member.onboarding.services.internal.MemberOnboardingCreationService
import br.com.alice.membership.model.events.UpdateAppStateRequestedEvent
import br.com.alice.membership.model.events.UpdateAppStateRequestedPayload
import br.com.alice.person.client.MemberService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class MemberOnboardingServiceImplTest {

    private val memberOnboardingDataService: MemberOnboardingDataService = mockk()
    private val memberService: MemberService = mockk()
    private val memberOnboardingCreationService: MemberOnboardingCreationService = mockk()
    private val kafkaProducerService: KafkaProducerService = mockk()
    private val trackPersonABService: TrackPersonABService = mockk()
    private val featureConfigService: FeatureConfigService = mockk()

    private val memberOnboardingService = MemberOnboardingServiceImpl(
        memberOnboardingDataService,
        memberService,
        memberOnboardingCreationService,
        kafkaProducerService,
        trackPersonABService,
        featureConfigService
    )

    private val mockNow = LocalDateTime.parse("2024-08-20T10:00:00")
    private val childSteps = listOf(
        Step(
            templateType = MemberOnboardingStepType.COVER,
            status = MemberOnboardingStepStatus.PENDING,
            updatedAt = mockNow
        ),
        Step(
            templateType = MemberOnboardingStepType.CONCLUSION,
            status = MemberOnboardingStepStatus.BLOCKED,
            updatedAt = mockNow
        )
    )
    private val personId = PersonId()
    private val memberOnboarding = TestModelFactory.buildMemberOnboarding(
        personId = personId,
        steps = childSteps,
        createdAt = mockNow,
        updatedAt = mockNow,
        referencedLinks = emptyList()
    )
    private val member = TestModelFactory.buildMember(personId = personId, createdAt = LocalDateTime.parse("2024-08-19T11:00:00"))
    private val activeMember = member.copy(status = MemberStatus.ACTIVE)
    private val memberOnboardingWithReferencedLink = memberOnboarding.copy(
            referencedLinks = listOf(
                MemberOnboardingReferencedLink(
                    id = RangeUUID.generate(),
                    model = MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK_TEMPLATE
                )
            )
        )
    private val trackPerson = TrackPersonAB(
        personId = personId,
        featureConfigId = RangeUUID.generate(),
        abPath = "V1",
    )
    private val appStateEvent = UpdateAppStateRequestedEvent(
        UpdateAppStateRequestedPayload(personId = personId, appState = "MEMBER_ONBOARDING_V2")
    )
    private val appStateRedesignUnifiedHealthEvent = UpdateAppStateRequestedEvent(
        UpdateAppStateRequestedPayload(personId = personId, appState = "REDESIGN_UNIFIED_HEALTH")
    )

    @BeforeTest
    fun setup() {
        clearAllMocks()
    }


    @Test
    fun `#get should return MemberOnboarding`() = runBlocking {
        coEvery { memberOnboardingDataService.get(memberOnboarding.id) } returns memberOnboarding.success()

        val result = memberOnboardingService.get(memberOnboarding.id)

        assertThat(result).isSuccessWithData(memberOnboarding)

        coVerifyOnce { memberOnboardingDataService.get(any()) }
    }

    @Test
    fun `#getByPersonId should return MemberOnboarding`() = runBlocking {
        coEvery {
            memberOnboardingDataService.findOne(
                queryEq {
                    where { this.personId.eq(memberOnboarding.personId) }
                        .orderBy { this.createdAt }
                        .sortOrder { SortOrder.Descending }
                }
            )
        } returns memberOnboarding.success()

        val result = memberOnboardingService.getByPersonId(memberOnboarding.personId)

        assertThat(result).isSuccessWithData(memberOnboarding)

        coVerifyOnce { memberOnboardingDataService.findOne(any()) }
    }

    @Test
    fun `#getByPersonIdAndCompleted should return MemberOnboarding`() = runBlocking {
        val memberOnboarding = memberOnboarding.copy(completed = true)
        coEvery {
            memberOnboardingDataService.findOne(
                queryEq {
                    where {
                        this.personId.eq(memberOnboarding.personId)
                            .and(this.completed.eq(true))
                    }
                        .orderBy { this.createdAt }
                        .sortOrder { SortOrder.Descending }

                }
            )
        } returns memberOnboarding.success()

        val result = memberOnboardingService.getByPersonIdAndCompleted(memberOnboarding.personId, true)

        assertThat(result).isSuccessWithData(memberOnboarding)

        coVerifyOnce { memberOnboardingDataService.findOne(any()) }
    }

    @Test
    fun `#updateStepStatus should set step status to pending and return MemberOnboarding`() = mockLocalDateTime { now ->
        val updatedStep = childSteps[1].copy(status = MemberOnboardingStepStatus.PENDING, updatedAt = now)

        val memberOnboardingUpdated = memberOnboarding.copy(
            steps = listOf(
                childSteps[0],
                updatedStep
            )
        )

        val event = MemberOnboardingStepUpdatedEvent(memberOnboardingUpdated)

        coEvery { memberOnboardingDataService.get(memberOnboarding.id) } returns memberOnboarding.success()
        coEvery { memberOnboardingDataService.update(memberOnboardingUpdated) } returns memberOnboardingUpdated.success()
        coEvery { kafkaProducerService.produce(event) } returns mockk()
        coEvery { kafkaProducerService.produce(appStateEvent) } returns mockk()
        coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()

        val result = memberOnboardingService.updateStepStatus(
            memberOnboarding.id,
            updatedStep.templateType,
            stepStatus = MemberOnboardingStepStatus.PENDING
        )

        assertThat(result).isSuccessWithData(memberOnboardingUpdated)

        coVerifyOnce { memberOnboardingDataService.get(any()) }
        coVerifyOnce { memberOnboardingDataService.update(any()) }
        coVerifyNone { memberService.getCurrent(any()) }
        coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#updateStepStatus should update with unlock final step when previous steps are completed`() =
        mockLocalDateTime { now ->
            val childSteps = listOf(
                Step(
                    templateType = MemberOnboardingStepType.COVER,
                    status = MemberOnboardingStepStatus.PENDING
                ),
                Step(
                    templateType = MemberOnboardingStepType.ALICE_INFO,
                    status = MemberOnboardingStepStatus.COMPLETED
                ),
                Step(
                    templateType = MemberOnboardingStepType.CONCLUSION,
                    status = MemberOnboardingStepStatus.BLOCKED
                )
            )
            val memberOnboarding = memberOnboarding.copy(steps = childSteps)
            val memberOnboardingUpdated = memberOnboarding.copy(
                steps = listOf(
                    Step(
                        templateType = MemberOnboardingStepType.COVER,
                        status = MemberOnboardingStepStatus.COMPLETED,
                        updatedAt = now
                    ),
                    childSteps[1],
                    childSteps[2]
                )
            )
            val expected = memberOnboardingUpdated.copy(
                steps = listOf(
                    memberOnboardingUpdated.steps[0],
                    memberOnboardingUpdated.steps[1],
                    Step(
                        templateType = MemberOnboardingStepType.CONCLUSION,
                        status = MemberOnboardingStepStatus.PENDING,
                        updatedAt = now
                    )
                )
            )
            val unblockEvent = MemberOnboardingStepUpdatedEvent(expected)

            coEvery { memberOnboardingDataService.get(memberOnboarding.id) } returns memberOnboarding.success()
            coEvery { memberOnboardingDataService.update(expected) } returns expected.success()
            coEvery { kafkaProducerService.produce(unblockEvent) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateEvent) } returns mockk()
        coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()

            val result = memberOnboardingService.updateStepStatus(
                memberOnboardingId = memberOnboarding.id,
                stepType = childSteps[0].templateType,
                stepStatus = MemberOnboardingStepStatus.COMPLETED
            )

            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { memberOnboardingDataService.get(any()) }
            coVerifyOnce { memberOnboardingDataService.update(any()) }
            coVerifyNone { memberService.getCurrent(any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#updateStepStatus should update and complete MemberOnboarding when all steps are finished and member is active`() =
        mockLocalDateTime { now ->
            val childSteps = listOf(
                Step(
                    templateType = MemberOnboardingStepType.COVER,
                    status = MemberOnboardingStepStatus.COMPLETED
                ),
                Step(
                    templateType = MemberOnboardingStepType.CONCLUSION,
                    status = MemberOnboardingStepStatus.PENDING
                )
            )
            val memberOnboarding = memberOnboarding.copy(steps = childSteps)
            val memberOnboardingUpdated = memberOnboarding.copy(
                steps = listOf(
                    childSteps[0],
                    Step(
                        templateType = MemberOnboardingStepType.CONCLUSION,
                        status = MemberOnboardingStepStatus.COMPLETED,
                        updatedAt = now
                    )
                )
            )
            val expected = memberOnboardingUpdated.copy(completed = true)
            val eventCompleted = MemberOnboardingCompletedEvent(expected)

            coEvery { memberOnboardingDataService.get(memberOnboarding.id) } returns memberOnboarding.success()
            coEvery { memberService.getCurrent(personId) } returns activeMember.success()
            coEvery { memberOnboardingDataService.update(expected) } returns expected.success()
            coEvery { kafkaProducerService.produce(eventCompleted) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateEvent) } returns mockk()
        coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()

            val result = memberOnboardingService.updateStepStatus(
                memberOnboardingId = memberOnboarding.id,
                stepType = childSteps[1].templateType,
                stepStatus = MemberOnboardingStepStatus.COMPLETED
            )

            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { memberOnboardingDataService.get(any()) }
            coVerifyOnce { memberService.getCurrent(any()) }
            coVerifyOnce { memberOnboardingDataService.update(any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }

    @Test
    fun `#updateStepStatus should update status and return when member is not active`() = mockLocalDateTime { now ->
        val childSteps = listOf(
            Step(
                templateType = MemberOnboardingStepType.COVER,
                status = MemberOnboardingStepStatus.COMPLETED
            ),
            Step(
                templateType = MemberOnboardingStepType.CONCLUSION,
                status = MemberOnboardingStepStatus.PENDING
            )
        )
        val memberOnboarding = memberOnboarding.copy(steps = childSteps)
        val memberOnboardingUpdated = memberOnboarding.copy(
            steps = listOf(
                childSteps[0],
                Step(
                    templateType = MemberOnboardingStepType.CONCLUSION,
                    status = MemberOnboardingStepStatus.COMPLETED,
                    updatedAt = now
                )
            )
        )
        val event = MemberOnboardingStepUpdatedEvent(memberOnboardingUpdated)

        coEvery { memberOnboardingDataService.get(memberOnboarding.id) } returns memberOnboarding.success()
        coEvery { memberOnboardingDataService.update(memberOnboardingUpdated) } returns memberOnboardingUpdated.success()
        coEvery { memberService.getCurrent(personId) } returns member.success()
        coEvery { kafkaProducerService.produce(event) } returns mockk()
        coEvery { kafkaProducerService.produce(appStateEvent) } returns mockk()
        coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()

        val result = memberOnboardingService.updateStepStatus(
            memberOnboardingId = memberOnboarding.id,
            stepType = childSteps[1].templateType,
            stepStatus = MemberOnboardingStepStatus.COMPLETED
        )

        assertThat(result).isSuccessWithData(memberOnboardingUpdated)

        coVerifyOnce { memberOnboardingDataService.get(any()) }
        coVerifyOnce { memberService.getCurrent(any()) }
        coVerifyOnce { memberOnboardingDataService.update(any()) }
        coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#addReferencedLink - should add referenced link and update`() = runBlocking {
        val referencedLinks = listOf(MemberOnboardingReferencedLink(id = RangeUUID.generate(), model = MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK))
        val expected = memberOnboardingWithReferencedLink.copy(
            referencedLinks = memberOnboardingWithReferencedLink.referencedLinks + referencedLinks
        )
        coEvery { memberOnboardingDataService.get(memberOnboardingWithReferencedLink.id) } returns memberOnboardingWithReferencedLink.success()
        coEvery { memberOnboardingDataService.update(expected) } returns expected.success()
        coEvery { kafkaProducerService.produce(appStateEvent) } returns mockk()
        coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()

        val result = memberOnboardingService.addReferencedLink(memberOnboardingWithReferencedLink.id, referencedLinks)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { memberOnboardingDataService.get(any()) }
        coVerifyOnce { memberOnboardingDataService.update(any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#addReferencedLink - should add referenced link not repeat and update`() = runBlocking {
        val referencedLinks = listOf(MemberOnboardingReferencedLink(id = RangeUUID.generate(), model = MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK))
        val expected = memberOnboarding.copy(referencedLinks = referencedLinks)
        coEvery { memberOnboardingDataService.get(memberOnboarding.id) } returns expected.success()
        coEvery { memberOnboardingDataService.update(expected) } returns expected.success()
        coEvery { kafkaProducerService.produce(appStateEvent) } returns mockk()
        coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()

        val result = memberOnboardingService.addReferencedLink(memberOnboarding.id, referencedLinks)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { memberOnboardingDataService.get(any()) }
        coVerifyOnce { memberOnboardingDataService.update(any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#addReferencedLink - should add referenced overriding referencedLinks`() = runBlocking {
        val referencedLinks = listOf(MemberOnboardingReferencedLink(id = RangeUUID.generate(), model = MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK))
        val expected = memberOnboardingWithReferencedLink.copy(
            referencedLinks = referencedLinks
        )
        coEvery { memberOnboardingDataService.get(memberOnboardingWithReferencedLink.id) } returns memberOnboardingWithReferencedLink.success()
        coEvery { memberOnboardingDataService.update(expected) } returns expected.success()
        coEvery { kafkaProducerService.produce(appStateEvent) } returns mockk()
        coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()

        val result = memberOnboardingService.addReferencedLink(memberOnboardingWithReferencedLink.id, referencedLinks, shouldOverride = true)

        assertThat(result).isSuccessWithData(expected)

        coVerifyOnce { memberOnboardingDataService.get(any()) }
        coVerifyOnce { memberOnboardingDataService.update(any()) }
        coVerify(exactly = 2) { kafkaProducerService.produce(any()) }
    }

    @Test
    fun `#findAllPaginatedByCompleted should return a list of MemberOnboarding`() = runBlocking {
        coEvery {
            memberOnboardingDataService.find(
                queryEq {
                    where {
                        this.completed.eq(false)
                    }
                        .orderBy { this.createdAt }
                        .sortOrder { SortOrder.Ascending }
                        .offset { 0 }.limit { 10 }
                }
            )
        } returns listOf(memberOnboarding).success()

        val result = memberOnboardingService.findAllPaginatedByCompleted(completed = false, offset = 0, limit = 10)

        assertThat(result).isSuccessWithData(listOf(memberOnboarding))

        coVerifyOnce { memberOnboardingDataService.find(any()) }
    }

    @Test
    fun `#findAllByReferencedLinkModelPaginated should return a list of MemberOnboarding`() = runBlocking {
        val model = MemberOnboardingReferencedLinkModel.HEALTH_PLAN_TASK
        val memberOnboarding = memberOnboarding.copy(
            referencedLinks = listOf(
                MemberOnboardingReferencedLink(
                    id = RangeUUID.generate(),
                    model = model
                )
            )
        )
        coEvery {
            memberOnboardingDataService.find(
                queryEq {
                    where {
                        this.referencedLinkModel.eq(model)
                    }
                        .orderBy { this.createdAt }
                        .sortOrder { SortOrder.Ascending }
                        .offset { 0 }.limit { 10 }
                }
            )
        } returns listOf(memberOnboarding).success()

        val result = memberOnboardingService.findAllByReferencedLinkModelPaginated(model = model, offset = 0, limit = 10)

        assertThat(result).isSuccessWithData(listOf(memberOnboarding))

        coVerifyOnce { memberOnboardingDataService.find(any()) }
    }

    @Test
    fun `#delete should delete a MemberOnboarding`() = runBlocking {
        coEvery {
            memberOnboardingDataService.delete(memberOnboarding)
        } returns true.success()

        val result = memberOnboardingService.delete(memberOnboarding)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { memberOnboardingDataService.delete(any()) }
    }

    @Test
    fun `#deleteList should delete a list of MemberOnboarding`() = runBlocking {
        val memberOnboarding2 = memberOnboarding.copy(id = RangeUUID.generate())
        val memberOnboardings = listOf(memberOnboarding, memberOnboarding2)
        coEvery {
            memberOnboardingDataService.deleteList(memberOnboardings)
        } returns listOf(true).success()

        val result = memberOnboardingService.deleteList(memberOnboardings)

        assertThat(result).isSuccessWithData(listOf(true))

        coVerifyOnce { memberOnboardingDataService.deleteList(any()) }
    }

    @Test
    fun `#findAllPaginated should return a list of MemberOnboarding`() = runBlocking {
        coEvery {
            memberOnboardingDataService.find(
                queryEq {
                    orderBy { this.createdAt }
                        .sortOrder { SortOrder.Ascending }
                        .offset { 0 }.limit { 10 }
                }
            )
        } returns listOf(memberOnboarding).success()

        val result = memberOnboardingService.findAllPaginated(offset = 0, limit = 10)

        assertThat(result).isSuccessWithData(listOf(memberOnboarding))

        coVerifyOnce { memberOnboardingDataService.find(any()) }
    }

    @Test
    fun `#unlockStepOrCompleteOnboarding should unlock final step and return MemberOnboarding updated`() = runBlocking {
        mockLocalDateTime(mockNow) {
            val memberOnboarding = memberOnboarding.copy(
                steps = listOf(
                    childSteps[0].copy(status = MemberOnboardingStepStatus.COMPLETED),
                    childSteps[1]
                )
            )
            val expected = memberOnboarding.copy(
                steps = listOf(
                    childSteps[0].copy(status = MemberOnboardingStepStatus.COMPLETED),
                    childSteps[1].copy(status = MemberOnboardingStepStatus.PENDING)
                )
            )
            val unblockEvent = MemberOnboardingStepUpdatedEvent(expected)

            coEvery { memberOnboardingDataService.update(expected) } returns expected.success()
            coEvery { kafkaProducerService.produce(unblockEvent) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateEvent) } returns mockk()
        coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()


            val result = memberOnboardingService.unlockStepOrCompleteOnboarding(
                memberOnboarding = memberOnboarding,
                isMemberActive = false
            )

            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { memberOnboardingDataService.update(any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }
    }

    @Test
    fun `#unlockStepOrCompleteOnboarding should complete onboarding and return it`() = runBlocking {
        mockLocalDateTime(mockNow) {
            val memberOnboarding = memberOnboarding.copy(
                steps = listOf(
                    childSteps[0].copy(status = MemberOnboardingStepStatus.COMPLETED),
                    childSteps[1].copy(status = MemberOnboardingStepStatus.COMPLETED)
                )
            )
            val expected = memberOnboarding.copy(completed = true)
            val eventCompleted = MemberOnboardingCompletedEvent(expected)

            coEvery { memberOnboardingDataService.update(expected) } returns expected.success()
            coEvery { kafkaProducerService.produce(eventCompleted) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateEvent) } returns mockk()
        coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()

            val result = memberOnboardingService.unlockStepOrCompleteOnboarding(
                memberOnboarding = memberOnboarding,
                isMemberActive = true
            )

            assertThat(result).isSuccessWithData(expected)

            coVerifyOnce { memberOnboardingDataService.update(any()) }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }
    }

    @Test
    fun `#unlockStepOrCompleteOnboarding should only return same MemberOnboarding when can't be unlock ou completed`() = runBlocking {
        mockLocalDateTime(mockNow) {

            val result = memberOnboardingService.unlockStepOrCompleteOnboarding(
                memberOnboarding = memberOnboarding,
                isMemberActive = false
            )

            assertThat(result).isSuccessWithData(memberOnboarding)

            coVerifyNone {
                memberOnboardingDataService.update(any())
                kafkaProducerService.produce(any())
            }
        }
    }

    @Test
    fun `#getOnboardingVersion should return default onboarding version when there is a error getting ab test`() = runBlocking {
        val featureConfig = FeatureConfig(
            namespace = FeatureNamespace.MEMBER_ONBOARDING,
            key ="onboarding_version_ab_test",
            value = "V1:0.0,V2:1.0",
            type = FeatureType.DISTRIBUTION,
            description = "",
            active = true
        )

        coEvery {
            featureConfigService.getByNamespaceAndKey(featureConfig.namespace, featureConfig.key)
        } returns featureConfig.success()

        coEvery {
            trackPersonABService.findOne(
                personId = personId,
                featureFlagId = featureConfig.id
            )
        } returns NotFoundException().failure()

        val result = memberOnboardingService.getOnboardingVersion(
            personId = memberOnboarding.personId
        )

        assertThat(result).isSuccessWithData(OnboardingVersion.V1)

        coVerifyOnce { featureConfigService.getByNamespaceAndKey(any(), any()) }
        coVerifyOnce { trackPersonABService.findOne(any(), any()) }
    }

    @Test
    fun `#getOnboardingVersion should return ab test version`() = runBlocking {
        val featureConfig = FeatureConfig(
            id = trackPerson.featureConfigId,
            namespace = FeatureNamespace.MEMBER_ONBOARDING,
            key ="onboarding_version_ab_test",
            value = "V1:0.0,V2:1.0",
            type = FeatureType.DISTRIBUTION,
            description = "",
            active = true
        )
        val trackPerson = trackPerson.copy(abPath = "V2")

        coEvery {
            featureConfigService.getByNamespaceAndKey(featureConfig.namespace, featureConfig.key)
        } returns featureConfig.success()

        coEvery {
            trackPersonABService.findOne(
                personId = personId,
                featureFlagId = featureConfig.id
            )
        } returns trackPerson.success()

        val result = memberOnboardingService.getOnboardingVersion(
            personId = memberOnboarding.personId
        )

        assertThat(result).isSuccessWithData(OnboardingVersion.V2)

        coVerifyOnce { featureConfigService.getByNamespaceAndKey(any(), any()) }
        coVerifyOnce { trackPersonABService.findOne(any(), any()) }
    }

    @Test
    fun `#getByPersonIds should return a list of MemberOnboarding`() = runBlocking {
        coEvery {
            memberOnboardingDataService.find(
                queryEq {
                    where { this.personId.inList(listOf(memberOnboarding.personId)) }
                }
            )
        } returns listOf(memberOnboarding).success()

        val result = memberOnboardingService.getByPersonIds(listOf(memberOnboarding.personId))

        assertThat(result).isSuccessWithData(listOf(memberOnboarding))

        coVerifyOnce { memberOnboardingDataService.find(any()) }
    }

    @Test
    fun `#createMemberOnboardingIfNecessary should return true when create onboarding`() = runBlocking {
        coEvery {
            memberOnboardingDataService.findOne(
                queryEq {
                    where { this.personId.eq(memberOnboarding.personId) }
                        .orderBy { this.createdAt }
                        .sortOrder { SortOrder.Descending }
                }
            )
        } returns memberOnboarding.success()

        coEvery {
            memberOnboardingCreationService.createIfNecessary(personId, memberOnboarding)
        } returns true.success()

        val result = memberOnboardingService.createMemberOnboardingIfNecessary(personId)

        assertThat(result).isSuccessWithData(true)

        coVerifyOnce { memberOnboardingDataService.findOne(any()) }
        coVerifyOnce { memberOnboardingCreationService.createIfNecessary(any(), any()) }
    }

    @Test
    fun `#createMemberOnboardingIfNecessary should return false when do not create onboarding`() = runBlocking {
        coEvery {
            memberOnboardingDataService.findOne(
                queryEq {
                    where { this.personId.eq(memberOnboarding.personId) }
                        .orderBy { this.createdAt }
                        .sortOrder { SortOrder.Descending }
                }
            )
        } returns NotFoundException().failure()

        coEvery {
            memberOnboardingCreationService.createIfNecessary(personId, null)
        } returns false.success()

        val result = memberOnboardingService.createMemberOnboardingIfNecessary(personId)

        assertThat(result).isSuccessWithData(false)

        coVerifyOnce { memberOnboardingDataService.findOne(any()) }
        coVerifyOnce { memberOnboardingCreationService.createIfNecessary(any(), any()) }
    }

    @Test
    fun `#getAliceInfoVersion should return member alice info version`() = runBlocking {
        val featureConfigId = RangeUUID.generate()
        coEvery {
            featureConfigService.getByNamespaceAndKey(
                namespace = FeatureNamespace.MEMBER_ONBOARDING,
                key = "alice_info_onboarding_version_ab_test"
            )
        } returns FeatureConfig(
            id = featureConfigId,
            namespace = FeatureNamespace.MEMBER_ONBOARDING,
            key = "alice_info_onboarding_version_ab_test",
            value = "1:0.0,2:1.0",
            type = FeatureType.DISTRIBUTION,
            description = "",
            active = true
        ).success()

        coEvery {
            trackPersonABService.findOne(personId, featureConfigId)
        } returns TrackPersonAB(
            personId = personId,
            featureConfigId = featureConfigId,
            abPath = "2"
        ).success()

        val result = memberOnboardingService.getAliceInfoVersion(personId)
        assertThat(result).isSuccessWithData(2)
    }

    @Test
    fun `#getAliceInfoVersion should return failure when feature config not found`(): Unit = runBlocking {
        coEvery {
            featureConfigService.getByNamespaceAndKey(
                namespace = FeatureNamespace.MEMBER_ONBOARDING,
                key = "alice_info_onboarding_version_ab_test"
            )
        } returns NotFoundException().failure()

        val result = memberOnboardingService.getAliceInfoVersion(personId)
        assertThat(result).isFailure()
    }

    @Test
    fun `#getAliceInfoVersion should return failure when track person ab not found`(): Unit = runBlocking {
        val featureConfigId = RangeUUID.generate()
        coEvery {
            featureConfigService.getByNamespaceAndKey(
                namespace = FeatureNamespace.MEMBER_ONBOARDING,
                key = "alice_info_onboarding_version_ab_test"
            )
        } returns FeatureConfig(
            id = featureConfigId,
            namespace = FeatureNamespace.MEMBER_ONBOARDING,
            key = "alice_info_onboarding_version_ab_test",
            value = "1:0.0,2:1.0",
            type = FeatureType.DISTRIBUTION,
            description = "",
            active = true
        ).success()

        coEvery { trackPersonABService.findOne(personId, featureConfigId) } returns NotFoundException().failure()

        val result = memberOnboardingService.getAliceInfoVersion(personId)
        assertThat(result).isFailure()
    }

    @Test
    fun `#findByIds should return a list of MemberOnboarding`() = runBlocking {
        coEvery {
            memberOnboardingDataService.find(
                queryEq {
                    where { this.id.inList(listOf(memberOnboarding.id)) }
                }
            )
        } returns listOf(memberOnboarding).success()

        val result = memberOnboardingService.findByIds(listOf(memberOnboarding.id))

        assertThat(result).isSuccessWithData(listOf(memberOnboarding))

        coVerifyOnce { memberOnboardingDataService.find(any()) }
    }

    @Test
    fun `#getByPersonIdCheckingSteps should return MemberOnboarding`() = runBlocking {
        coEvery {
            memberOnboardingDataService.findOne(
                queryEq {
                    where { this.personId.eq(memberOnboarding.personId) }
                        .orderBy { this.createdAt }
                        .sortOrder { SortOrder.Descending }
                }
            )
        } returns memberOnboarding.success()

        val result = memberOnboardingService.getByPersonIdCheckingSteps(memberOnboarding.personId, false)

        assertThat(result).isSuccessWithData(memberOnboarding)

        coVerifyOnce { memberOnboardingDataService.findOne(any()) }
        coVerifyNone { memberOnboardingDataService.update(any()) }
        coVerify { kafkaProducerService wasNot called }
    }

    @Test
    fun `#getByPersonIdCheckingSteps should return MemberOnboarding with last step unblock`() = runBlocking {
        mockLocalDateTime(mockNow) {
            val completedStep = childSteps[0].copy(status = MemberOnboardingStepStatus.COMPLETED)
            val memberOnboarding = memberOnboarding.copy(steps = listOf(completedStep, childSteps[1]))
            val memberOnboardingUnblocked = memberOnboarding.copy(
                steps = listOf(completedStep, childSteps[1].copy(status = MemberOnboardingStepStatus.PENDING))
            )
            val unblockEvent = MemberOnboardingStepUpdatedEvent(memberOnboardingUnblocked)

            coEvery {
                memberOnboardingDataService.findOne(
                    queryEq {
                        where { this.personId.eq(memberOnboarding.personId) }
                            .orderBy { this.createdAt }
                            .sortOrder { SortOrder.Descending }
                    }
                )
            } returns memberOnboarding.success()

            coEvery { memberOnboardingDataService.update(memberOnboardingUnblocked) } returns memberOnboardingUnblocked.success()
            coEvery { kafkaProducerService.produce(unblockEvent) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateEvent) } returns mockk()
        coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()

            val result = memberOnboardingService.getByPersonIdCheckingSteps(memberOnboarding.personId, true)

            assertThat(result).isSuccessWithData(memberOnboardingUnblocked)

            coVerifyOnce {
                memberOnboardingDataService.findOne(any())
                memberOnboardingDataService.update(any())
            }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }
    }

    @Test
    fun `#getByPersonIdCheckingSteps should return MemberOnboarding with last step completed`() = runBlocking {
        mockLocalDateTime(mockNow) {
            val memberOnboarding = memberOnboarding.copy(
                steps = listOf(
                    childSteps[0].copy(status = MemberOnboardingStepStatus.COMPLETED),
                    childSteps[1].copy(status = MemberOnboardingStepStatus.COMPLETED)
                )
            )
            val memberOnboardingCompleted = memberOnboarding.copy(completed = true)
            val eventCompleted = MemberOnboardingCompletedEvent(memberOnboardingCompleted)

            coEvery {
                memberOnboardingDataService.findOne(
                    queryEq {
                        where { this.personId.eq(memberOnboarding.personId) }
                            .orderBy { this.createdAt }
                            .sortOrder { SortOrder.Descending }
                    }
                )
            } returns memberOnboarding.success()

            coEvery { memberOnboardingDataService.update(memberOnboardingCompleted) } returns memberOnboardingCompleted.success()
            coEvery { kafkaProducerService.produce(eventCompleted) } returns mockk()
            coEvery { kafkaProducerService.produce(appStateEvent) } returns mockk()
        coEvery { kafkaProducerService.produce(appStateRedesignUnifiedHealthEvent) } returns mockk()

            val result = memberOnboardingService.getByPersonIdCheckingSteps(memberOnboarding.personId, true)

            assertThat(result).isSuccessWithData(memberOnboardingCompleted)

            coVerifyOnce {
                memberOnboardingDataService.findOne(any())
                memberOnboardingDataService.update(any())
            }
            coVerify(exactly = 3) { kafkaProducerService.produce(any()) }
        }
    }

    @Test
    fun `#getOrCreateAbOnboardingVersion should return onboarding version`() = runBlocking {
        val onboardingVersion = OnboardingVersion.V1

        coEvery { memberOnboardingCreationService.getOrCreateAbOnboardingVersion(personId) } returns onboardingVersion.success()

        val result = memberOnboardingService.getOrCreateAbOnboardingVersion(personId)
        assertThat(result).isSuccessWithData(onboardingVersion)

        coVerifyOnce { memberOnboardingCreationService.getOrCreateAbOnboardingVersion(any()) }
    }
}
