package br.com.alice.member.onboarding.services.internal

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.observability.Spannable
import br.com.alice.data.layer.models.FeatureConfig
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.MemberOnboarding
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepStatus
import br.com.alice.data.layer.models.MemberOnboarding.MemberOnboardingStepType
import br.com.alice.data.layer.models.MemberOnboardingFlowType
import br.com.alice.data.layer.models.Step
import br.com.alice.data.layer.services.MemberOnboardingDataService
import br.com.alice.featureconfig.client.FeatureConfigService
import br.com.alice.featureconfig.client.TrackPersonABService
import br.com.alice.featureconfig.core.FeatureService
import br.com.alice.healthlogic.client.ClinicalOutcomeService
import br.com.alice.member.onboarding.factory.OnboardingFactory
import br.com.alice.member.onboarding.model.OnboardingVersion
import br.com.alice.member.onboarding.notifier.MemberOnboardingCreatedEvent
import br.com.alice.member.onboarding.services.MEMBER_ONBOARDING_APP_STATE
import br.com.alice.member.onboarding.services.REDESIGN_UNIFIED_HEALTH_APP_STATE
import br.com.alice.membership.client.DeviceService
import br.com.alice.membership.client.onboarding.HealthDeclarationForm
import br.com.alice.membership.model.events.UpdateAppStateRequestedEvent
import br.com.alice.membership.model.events.UpdateAppStateRequestedPayload
import br.com.alice.person.br.com.alice.person.model.MemberCurrentAndPrevious
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

class MemberOnboardingCreationService(
    private val memberOnboardingDataService: MemberOnboardingDataService,
    private val memberService: MemberService,
    private val personService: PersonService,
    private val healthDeclarationService: HealthDeclarationForm,
    private val trackPersonABService: TrackPersonABService,
    private val kafkaProducerService: KafkaProducerService,
    private val aliceInfoABTestService: AliceInfoABTestService,
    private val clinicalOutcomeService: ClinicalOutcomeService,
    private val featureConfigService: FeatureConfigService,
    private val deviceService: DeviceService
): Spannable {

    suspend fun createIfNecessary(
        personId: PersonId,
        memberOnboarding: MemberOnboarding? = null
    ): Result<Boolean, Throwable> =
        memberService.findMemberCurrentAndPrevious(personId)
            .flatMap { memberCurrentAndPrevious ->
                if (!memberCurrentAndPrevious.shouldCreateOnboarding(memberOnboarding)) return@flatMap false.success()

                if (memberCurrentAndPrevious.isReactivation() && memberCurrentAndPrevious.isReactivationLessThan1Year())
                    return@flatMap createForReactivationLessThan1Year(personId, memberOnboarding)

                val completedSteps = getHealthDeclarationCompletedStep(personId)?.let { listOf(it) }
                create(personId, completedSteps).map { true }
            }

    private suspend fun createForReactivationLessThan1Year(
        personId: PersonId,
        memberOnboarding: MemberOnboarding? = null
    ): Result<Boolean, Throwable> =
        if (memberOnboarding != null && !memberOnboarding.createdAt.isOld()) {
            false.success()
        } else {
            val scoreMagenta = getScoreMagentaCompletedStep(personId)
            val healthDeclaration = getHealthDeclarationCompletedStep(personId)

            val completedSteps = listOfNotNull(healthDeclaration, scoreMagenta).ifEmpty { null }

            create(personId, completedSteps).map { true }
        }

    private suspend fun create(
        personId: PersonId,
        completedSteps: List<MemberOnboardingStepType>? = null
    ): Result<MemberOnboarding, Throwable> =
        getMemberFlowType(personId)
            .flatMap { createOnboardingAndProduceEvent(personId, it, completedSteps) }
            .also { aliceInfoABTestService.createIfNecessary(personId) }

    private suspend fun createOnboardingAndProduceEvent(
        personId: PersonId,
        flowType: MemberOnboardingFlowType,
        completedSteps: List<MemberOnboardingStepType>?
    ): Result<MemberOnboarding, Throwable> =
        personId.buildDefaultSteps(flowType = flowType, completedSteps = completedSteps).flatMap { steps ->
            memberOnboardingDataService.add(
                MemberOnboarding(personId = personId, steps = steps, flowType = flowType)
            ).then {
                produceEvents(it)
            }
        }

    suspend fun getOrCreateAbOnboardingVersion(personId: PersonId): Result<OnboardingVersion, Throwable> =
        span("getOrCreateAbOnboardingVersion") { span ->
            getOnboardingFeatureConfig()
                .flatMap { featureConfig ->
                    findOrCreateAbTest(personId, featureConfig).then {
                        span.setAttribute("version", it.toString())
                    }
                }
        }

    private suspend fun findOrCreateAbTest(
        personId: PersonId,
        featureConfig: FeatureConfig
    ): Result<OnboardingVersion, Throwable> =
        trackPersonABService.findOne(personId, featureConfig.id)
            .map { OnboardingVersion.valueOf(it.abPath) }
            .coFoldNotFound {
                createNewAbTest(personId, featureConfig)
            }

    private suspend fun createNewAbTest(
        personId: PersonId,
        featureConfig: FeatureConfig
    ): Result<OnboardingVersion, Throwable> {
        val abPath = determineAbPath(personId, featureConfig.key)

        return trackPersonABService.create(
            personId = personId,
            featureFlagId = featureConfig.id,
            abPath = abPath
        ).map { OnboardingVersion.valueOf(it.abPath) }
    }

    private suspend fun determineAbPath(personId: PersonId, featureKey: String): String =
        span("determineAbPath") { span ->
            val onboardingVersion = FeatureService.inDistribution(
                namespace = FeatureNamespace.MEMBER_ONBOARDING,
                key = featureKey,
                testValue = personId.toString(),
                defaultReturn = "V1"
            )

            if (onboardingVersion != "V3") return@span "V1"

            deviceService.getDeviceByPerson(personId.toString())
                .map { device ->
                    span.setAttribute("app_version", device.appVersion.toString())

                    device.appVersion
                        ?.takeIf { it >= getOnboardingV3MinAppVersion() }
                        ?.let { "V3" } ?: "V1"
                }.get()
        }

    private suspend fun getOnboardingFeatureConfig() =
        featureConfigService.getByNamespaceAndKey(
            namespace = FeatureNamespace.MEMBER_ONBOARDING,
            key = "onboarding_version_ab_test"
        )

    private fun getOnboardingV3MinAppVersion(): String =
        FeatureService.get(
            FeatureNamespace.MEMBER_ONBOARDING,
            "onboarding_v3_min_app_version",
            "4.36.0"
        )

    private suspend fun getHealthDeclarationCompletedStep(personId: PersonId) =
        healthDeclarationService.findFinishedByPerson(personId)
            .getOrNullIfNotFound()
            ?.takeIf { it.finishedAt?.isOld() == true }
            ?.let { MemberOnboardingStepType.COVER }

    private suspend fun getScoreMagentaCompletedStep(personId: PersonId) =
        clinicalOutcomeService.getByPersonIdAndOutcomeConfIdBeforeDate(personId, getScoreMagentaId())
            .getOrNullIfNotFound()
            ?.takeIf { it.addedAt.isOld() }
            ?.let { MemberOnboardingStepType.SCORE_MAGENTA }

    private suspend fun getMemberFlowType(personId: PersonId): Result<MemberOnboardingFlowType, Throwable> =
        personService.get(personId).map { person ->
            if (person.isMinor) MemberOnboardingFlowType.CHILD
            else MemberOnboardingFlowType.ADULT
        }

    private suspend fun PersonId.buildDefaultSteps(
        flowType: MemberOnboardingFlowType,
        completedSteps: List<MemberOnboardingStepType>?
    ) =
        getOrCreateAbOnboardingVersion(this).map { version ->
            OnboardingFactory.getStepTypes(flowType, version).map {
                Step(
                    templateType = it,
                    status = it.getStepStartedStatus(completedSteps, flowType)
                )
            }
        }

    private suspend fun produceEvents(memberOnboarding: MemberOnboarding) {
        kafkaProducerService.produce(
            UpdateAppStateRequestedEvent(
                UpdateAppStateRequestedPayload(
                    personId = memberOnboarding.personId,
                    appState = MEMBER_ONBOARDING_APP_STATE
                )
            )
        )
        kafkaProducerService.produce(
            UpdateAppStateRequestedEvent(
                UpdateAppStateRequestedPayload(
                    personId = memberOnboarding.personId,
                    appState = REDESIGN_UNIFIED_HEALTH_APP_STATE
                )
            )
        )
        kafkaProducerService.produce(MemberOnboardingCreatedEvent(memberOnboarding))
    }

    private fun List<MemberOnboardingStepType>?.getStatus(stepType: MemberOnboardingStepType) =
        this.takeIf { this != null && this.contains(stepType) }
            ?.let { MemberOnboardingStepStatus.COMPLETED }
            ?: MemberOnboardingStepStatus.PENDING

    private fun MemberOnboardingStepType.getStepStartedStatus(
        completedSteps: List<MemberOnboardingStepType>?,
        flowType: MemberOnboardingFlowType
    ) =
        takeIf { this == MemberOnboardingStepType.CONCLUSION }
            ?.let {
                completedSteps
                    ?.takeIf { it.contains(MemberOnboardingStepType.COVER) && flowType == MemberOnboardingFlowType.CHILD }
                    ?.let { MemberOnboardingStepStatus.PENDING }
                    ?: MemberOnboardingStepStatus.BLOCKED
            }
            ?: completedSteps.getStatus(this)

    private fun MemberCurrentAndPrevious.isReactivation() =
        this.previous?.canceledAt != null

    private fun MemberCurrentAndPrevious.shouldCreateBasedInInitDate() =
        this.current.createdAt >= ONBOARDING_INIT_DATE

    private fun MemberCurrentAndPrevious.isReactivationLessThan1Year() =
        this.previous!!.canceledAt!!.until(this.current.createdAt, ChronoUnit.MONTHS) < ONBOARDING_VALID_TIME_IN_MONTHS

    private fun MemberCurrentAndPrevious.shouldCreateOnboarding(memberOnboarding: MemberOnboarding?) =
        this.shouldCreateBasedInInitDate() &&
                !memberOnboarding.alreadyHasMemberOnboarding(this) &&
                this.shouldCreateBasedInBrand()

    private fun MemberOnboarding?.alreadyHasMemberOnboarding(memberCurrentAndPrevious: MemberCurrentAndPrevious) =
        this?.createdAt != null && this.createdAt > memberCurrentAndPrevious.current.createdAt

    private fun LocalDateTime.isOld() =
        this.until(LocalDateTime.now(), ChronoUnit.MONTHS) >= ONBOARDING_VALID_TIME_IN_MONTHS

    private fun MemberCurrentAndPrevious.shouldCreateBasedInBrand() =
        this.current.brand?.isAlice() ?: false

    private fun getScoreMagentaId() =
        FeatureService.get(
            namespace = FeatureNamespace.MEMBERSHIP,
            key = "score-magenta-outcome-conf-id",
            defaultValue = "bec90d96-4d94-4f66-9cb3-5a8956062600"
        ).toUUID()

    private companion object {
        const val ONBOARDING_VALID_TIME_IN_MONTHS = 12L
        val ONBOARDING_INIT_DATE: LocalDateTime = LocalDateTime.parse("2024-08-19T00:00:00")
    }
}
