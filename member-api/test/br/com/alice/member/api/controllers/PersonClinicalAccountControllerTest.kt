package br.com.alice.member.api.controllers

import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.member.api.RoutesTestHelper
import br.com.alice.member.api.ServiceConfig.Webhook.ClearSale.token
import br.com.alice.member.api.builders.MemberOnboardingsScreenBuilder
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.HealthProfessionalService.FindOptions
import br.com.alice.staff.client.HealthcareTeamService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class PersonClinicalAccountControllerTest : RoutesTestHelper() {
    private val personClinicalAccountService: PersonClinicalAccountService = mockk()
    private val healthcareTeamService: HealthcareTeamService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val personService: PersonService = mockk()
    private val memberService: MemberService = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single {
            PersonClinicalAccountController(
                personClinicalAccountService,
                healthcareTeamService,
                healthProfessionalService,
                personService,
                memberService
            )
        }
    }

    private val person = TestModelFactory.buildPerson()
    private val healthcareTeam = TestModelFactory.buildHealthcareTeam()
    private val healthProfessional = TestModelFactory.buildHealthProfessional()
    private val personClinicalAccount = TestModelFactory.buildPersonClinicalAccount()
    private val member = TestModelFactory.buildMember(status = MemberStatus.ACTIVE, personId = person.id)

    @Test
    fun `#associate - should associate and return screen`() = runBlocking {
        mockkObject(MemberOnboardingsScreenBuilder) {
            val personId = person.id
            val healthcareTeamId = healthcareTeam.id
            val expected = ScreensTransport(
                id = "test",
                layout = ScreenLayout(
                    type = "test",
                    body = emptyList()
                )
            )
            coEvery { healthcareTeamService.get(healthcareTeamId) } returns healthcareTeam.success()
            coEvery { healthProfessionalService.findByStaffId(healthcareTeam.physicianStaffId, FindOptions(withContact = true)) } returns healthProfessional.success()
            coEvery { personService.get(personId) } returns person.success()
            coEvery {
                personClinicalAccountService.associatePersonIfNotYet(
                    personId,
                    healthcareTeamId,
                    null
                )
            } returns personClinicalAccount.success()
            coEvery { memberService.getCurrent(personId) } returns member.success()
            every { MemberOnboardingsScreenBuilder.buildScreenMFCSelected(healthProfessional, person, member.active) } returns expected

            authenticatedAs(token, toTestPerson(person)) {
                post("/person_clinical_account/${healthcareTeam.id}") { response ->
                    assertThat(response).isOKWithData(expected)

                    coVerifyOnce {
                        healthcareTeamService.get(any())
                        healthProfessionalService.findByStaffId(any(), any())
                        personService.get(any())
                        personClinicalAccountService.associatePersonIfNotYet(any(), any(), any())
                        MemberOnboardingsScreenBuilder.buildScreenMFCSelected(any(), any(), any())
                    }
                }
            }
        }
    }

}
