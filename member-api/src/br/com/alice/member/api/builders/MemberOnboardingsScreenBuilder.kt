package br.com.alice.member.api.builders

import br.com.alice.app.content.model.ActionRouting
import br.com.alice.app.content.model.Alignment
import br.com.alice.app.content.model.AppBar
import br.com.alice.app.content.model.Button
import br.com.alice.app.content.model.Caption
import br.com.alice.app.content.model.CloseBehavior
import br.com.alice.app.content.model.CloseStrategy
import br.com.alice.app.content.model.Image
import br.com.alice.app.content.model.ImageSize
import br.com.alice.app.content.model.ImageType
import br.com.alice.app.content.model.Label
import br.com.alice.app.content.model.RemoteAction
import br.com.alice.app.content.model.RemoteActionType
import br.com.alice.app.content.model.ScreenAlignment
import br.com.alice.app.content.model.ScreenAlignmentType
import br.com.alice.app.content.model.ScreenBackgroundType
import br.com.alice.app.content.model.ScreenLayout
import br.com.alice.app.content.model.ScreenProperties
import br.com.alice.app.content.model.ScreensTransport
import br.com.alice.app.content.model.Section
import br.com.alice.app.content.model.SectionPadding
import br.com.alice.app.content.model.SectionTextLayout
import br.com.alice.app.content.model.SectionType
import br.com.alice.app.content.model.Variant
import br.com.alice.app.content.model.section.ButtonSection
import br.com.alice.app.content.model.section.GridGutter
import br.com.alice.app.content.model.section.GridSection
import br.com.alice.app.content.model.section.GridVariant
import br.com.alice.app.content.model.section.ImageSection
import br.com.alice.app.content.model.section.InfiniteScrollSection
import br.com.alice.app.content.model.section.ProfileCardSection
import br.com.alice.app.content.model.section.TextSection
import br.com.alice.common.DistanceUtils
import br.com.alice.common.core.extensions.toBrazilianDateFormat
import br.com.alice.common.models.Gender
import br.com.alice.coverage.converters.toConsolidatedSpecialistTransport
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.ModalityType
import br.com.alice.data.layer.models.Person
import java.time.LocalDateTime


object MemberOnboardingsScreenBuilder {

    private val DEFAULT_LOCATION = -23.55028 to -46.63389
    private const val DAYS_MAX_TO_MFC_AUTOMATIC = 60L


    fun buildScreenMFCSelected(healthProfessional: HealthProfessional, person: Person, isMemberActive: Boolean): ScreensTransport {
        val (latitude, longitude) = person.getLatAndLng()
        val addressConsolidated = healthProfessional.toConsolidatedSpecialistTransport(latitude, longitude)

        val checkIcon = buildImageSection(
            url = "https://alice-member-app-assets.s3.us-east-1.amazonaws.com/icon/chat_sent.svg",
            alignment = Alignment.LEFT,
            horizontalPadding = SectionPadding.P3,
            verticalPadding = SectionPadding.P0
        )
        val title = buildTextSection(title = "Parabéns, ${person.firstName}!")
        val subtitle = buildTextSection(
            title = "Agora você tem ${
                healthProfessional.getTextByGender(
                    "um médico",
                    "uma médica"
                )
            } de família e está perto de uma vida mais saudável",
            alignment = Alignment.LEFT,
            layout = SectionTextLayout.BODY_LARGE,
        )

        return ScreensTransport(
            id = "healthcare_team_selected",
            properties = ScreenProperties(
                alignment = ScreenAlignment(
                    vertical = ScreenAlignmentType.END,
                ),
                closeBehavior = CloseBehavior(
                    strategy = CloseStrategy.BLOCK,
                )
            ),
            layout = ScreenLayout(
                type = "single_column",
                appBar = AppBar(
                    back = ""
                ),
                backgroundType = ScreenBackgroundType.ACTIVE,
                body = listOf(
                    Section(
                        id = "${SectionType.INFINITE_SCROLL_SECTION}_healthcare_team_selected",
                        type = SectionType.INFINITE_SCROLL_SECTION,
                        minAppVersion = SectionType.INFINITE_SCROLL_SECTION.minAppVersion!!,
                        data = InfiniteScrollSection(
                            title = "",
                            body = listOf(
                                buildGridSection(
                                    id = "healthcare_team_selected_main_grid",
                                    gutter = GridGutter.GUTTER_6,
                                    gridVariant = GridVariant.FULL_WIDTH,
                                    children = listOf(
                                        buildGridSection(
                                            id = "healthcare_team_selected_grid_1",
                                            children = listOf(checkIcon, title, subtitle)
                                        ),
                                        buildGridSection(
                                            id = "healthcare_team_selected_grid_2",
                                            children = listOf(
                                                buildProfileCard(
                                                    accessoryImage = healthProfessional.profileBio
                                                        ?: (healthProfessional.imageUrl ?: ""),
                                                    title = healthProfessional.name,
                                                    subtitle = healthProfessional.getTextByGender(
                                                        "Seu Médico de Família",
                                                        "Sua Médica de Família"
                                                    ),
                                                    description = addressConsolidated.address?.formattedAddress(),
                                                    parks = listOfNotNull(
                                                        addressConsolidated.distance?.let {
                                                            Caption(
                                                                icon = "local_pin",
                                                                text = DistanceUtils.formatDistance(it)
                                                            )
                                                        },
                                                        healthProfessional.contacts?.firstOrNull { it.modality == ModalityType.PRESENTIAL }
                                                            ?.let {
                                                                Caption(
                                                                    icon = "meeting",
                                                                    text = "Presencial"
                                                                )
                                                            },
                                                        healthProfessional.contacts?.firstOrNull { it.modality == ModalityType.REMOTE }
                                                            ?.let {
                                                                Caption(
                                                                    icon = "video_on",
                                                                    text = "Vídeo"
                                                                )
                                                            }
                                                    ).ifEmpty { null }
                                                )
                                            ) + buildScreenMFCSelectedButtons(isMemberActive)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
        )
    }

    fun buildScreenEmptyMFC(activationDate: LocalDateTime): ScreensTransport =
        ScreensTransport(
            id = "empty_mfc",
            properties = ScreenProperties(
                alignment = ScreenAlignment(
                    vertical = ScreenAlignmentType.CENTER,
                    horizontal = ScreenAlignmentType.CENTER
                )
            ),
            layout = ScreenLayout(
                type = "single_column",
                appBar = AppBar(
                    back = "",
                    title = "Medicina de Família e Comunidade",
                ),
                backgroundType = ScreenBackgroundType.ACTIVE,
                body = listOf(
                    buildImageSection(
                        url = "https://alice-member-app-assets.s3.us-east-1.amazonaws.com/icon/stethoscope_gray_bg.png",
                        alignment = Alignment.CENTER,
                        horizontalPadding = null,
                        verticalPadding = null
                    ),
                    buildTextSection(
                        title = "Você ainda não escolheu seu Médico",
                        alignment = Alignment.CENTER,
                        layout = SectionTextLayout.TITLE_LARGE_HIGHLIGHT,
                    ),
                    buildTextSection(
                        title = "Após ${
                            activationDate.plusDays(DAYS_MAX_TO_MFC_AUTOMATIC).toBrazilianDateFormat()
                        } seu médico será atribuído automaticamente",
                        alignment = Alignment.CENTER,
                        layout = SectionTextLayout.BODY_LARGE,
                    ),
                ),
                footer = buildButtonSection(
                    id = "health_professional_select_cover",
                    textLabel = "Escolher meu médico",
                    variant = Variant.PRIMARY,
                    shrinkWrap = false,
                    remoteAction = RemoteAction(
                        mobileRoute = ActionRouting.MEMBER_ONBOARDING_V2_COVER,
                        params = mapOf(
                            "path" to "mfc_cover"
                        )
                    )
                )
            )
        )

    private fun buildScreenMFCSelectedButtons(isMemberActive: Boolean): List<Section> =
        isMemberActive.takeIf { it }?.let {
            listOf(
                buildButtonSection(
                    id = "health_professional_page",
                    textLabel = "Agendar consulta",
                    remoteAction = RemoteAction(
                        removeUntilRouteName = "LOGGED_IN",
                        mobileRoute = ActionRouting.SPECIALIST_DETAILS,
                        params = mapOf(
                            "method" to "GET",
                            "endpoint" to "/accredited_network/healthcare_team/physician",
                            "selected_tab" to "1"
                        )
                    )
                ),
                buildButtonSection(
                    id = "exit",
                    textLabel = "Sair",
                    variant = Variant.TERTIARY,
                    remoteAction = RemoteAction(
                        type = RemoteActionType.CLOSE,
                        params = mapOf(
                            "pop_to_root" to true,
                        )
                    )
                )
            )
        } ?: listOf(
            buildButtonSection(
                id = "continue_onboarding",
                textLabel = "Continuar",
                remoteAction = RemoteAction(
                    removeUntilRouteName = "LOGGED_IN",
                    mobileRoute = ActionRouting.MEMBER_ONBOARDING_V2
                )
            )
        )

    private fun buildGridSection(
        id: String,
        children: List<Section>,
        gutter: GridGutter = GridGutter.GUTTER_1,
        gridVariant: GridVariant = GridVariant.FULL_WIDTH,
    ) = Section(
        id = id,
        type = SectionType.GRID_SECTION,
        minAppVersion = SectionType.GRID_SECTION.minAppVersion!!,
        data = GridSection(
            gutter = gutter,
            gridVariant = gridVariant,
            children = children
        )
    )

    private fun buildButtonSection(
        id: String = "button_section",
        textLabel: String,
        variant: Variant = Variant.PRIMARY,
        shrinkWrap: Boolean = false,
        remoteAction: RemoteAction? = null,
    ) =
        Section(
            id = id,
            type = SectionType.BUTTON_SECTION,
            minAppVersion = SectionType.BUTTON_SECTION.minAppVersion!!,
            data = ButtonSection(
                button = Button(
                    id = "health_professional_page",
                    label = Label(text = textLabel),
                    variant = variant,
                    shrinkWrap = shrinkWrap,
                    onTapAction = remoteAction
                )
            )
        )

    private fun buildProfileCard(
        accessoryImage: String,
        title: String,
        subtitle: String?,
        description: String? = null,
        parks: List<Caption>? = null,
    ) = Section(
        id = "health_professional_card",
        type = SectionType.CARD_SECTION,
        minAppVersion = SectionType.CARD_SECTION.minAppVersion!!,
        data = ProfileCardSection(
            accessoryImage = accessoryImage,
            title = title,
            subtitle = subtitle,
            description = description,
            perks = parks
        )
    )

    private fun buildTextSection(
        title: String,
        alignment: Alignment = Alignment.LEFT,
        layout: SectionTextLayout = SectionTextLayout.TITLE_X_LARGE_HIGHLIGHT,
    ) = Section(
        id = "title",
        type = SectionType.TEXT_SECTION,
        minAppVersion = SectionType.TEXT_SECTION.minAppVersion!!,
        data = TextSection.Content(
            content = TextSection.Content.Value(
                title = title,
                alignment = alignment,
                layout = layout,
                enabled = true,
            )
        )
    )

    private fun buildImageSection(
        url: String,
        type: ImageType = ImageType.STATIC,
        size: ImageSize = ImageSize.SMALL,
        alignment: Alignment?,
        horizontalPadding: SectionPadding? = null,
        verticalPadding: SectionPadding? = null
    ) = Section(
        id = "check_icon",
        type = SectionType.IMAGE_SECTION,
        minAppVersion = SectionType.IMAGE_SECTION.minAppVersion!!,
        data = ImageSection(
            content = ImageSection.Content(
                alignment = alignment,
                horizontalPadding = horizontalPadding,
                verticalPadding = verticalPadding,
                image = Image(
                    url = url,
                    type = type,
                    size = size,
                )
            )
        )
    )


    private fun Person.getLatAndLng(): Pair<Double, Double> =
        this.addresses.firstOrNull { it.lat != null && it.lng != null }?.let {
            it.lat!! to it.lng!!
        } ?: DEFAULT_LOCATION //lat and lng of São Paulo, Sé

    private fun HealthProfessional.getTextByGender(textForMale: String, textForFemale: String) =
        takeIf { this.gender == Gender.MALE }?.let { textForMale } ?: textForFemale

}
