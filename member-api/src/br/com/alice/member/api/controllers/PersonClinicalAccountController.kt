package br.com.alice.member.api.controllers

import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.common.Response
import br.com.alice.common.controllers.Controller
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.foldResponse
import br.com.alice.member.api.builders.MemberOnboardingsScreenBuilder
import br.com.alice.person.client.MemberService
import br.com.alice.person.client.PersonService
import br.com.alice.staff.client.HealthProfessionalService
import br.com.alice.staff.client.HealthProfessionalService.FindOptions
import br.com.alice.staff.client.HealthcareTeamService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.map
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.util.UUID

class PersonClinicalAccountController(
    private val personClinicalAccountService: PersonClinicalAccountService,
    private val healthcareTeamService: HealthcareTeamService,
    private val healthProfessionalService: HealthProfessionalService,
    private val personService: PersonService,
    private val memberService: MemberService
) : Controller() {


    suspend fun associate(healthcareTeamId: UUID): Response = span("associate") { span ->
        coroutineScope {
            val personId = currentUid().toPersonId()

            val professionalDeferred = async {
                healthcareTeamService.get(healthcareTeamId)
                    .flatMap { healthProfessionalService.findByStaffId(it.physicianStaffId, FindOptions(withContact = true)) }.get()
            }
            val personDeferred = async {
                personService.get(personId).get()
            }

            val memberDeferred = async {
                memberService.getCurrent(personId).get()
            }

            personClinicalAccountService.associatePersonIfNotYet(
                personId = personId,
                healthCareTeamId = healthcareTeamId,
                referenceGroupId = null
            ).map {
                val person = personDeferred.await()
                val healthProfessional = professionalDeferred.await()
                val isMemberActive = memberDeferred.await().active

                span.setAttribute("healthcare_team_id", healthcareTeamId.toString())
                span.setAttribute("health_professional_id", healthProfessional.id.toString())
                span.setAttribute("health_professional_contact", healthProfessional.contacts.toString())

                MemberOnboardingsScreenBuilder.buildScreenMFCSelected(healthProfessional, person, isMemberActive)
            }
        }.foldResponse()
    }
}
