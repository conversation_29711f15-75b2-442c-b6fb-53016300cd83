package br.com.alice.member.api.ioc

import br.com.alice.common.client.DataEventClient
import br.com.alice.common.client.DefaultHttpClient
import br.com.alice.common.controllers.HealthController
import br.com.alice.common.redis.CacheFactory
import br.com.alice.common.service.serialization.simpleGson
import br.com.alice.data.layer.MEMBER_ROOT_SERVICE_NAME
import br.com.alice.data.layer.services.AbTestSpecialistRecommendationDataService
import br.com.alice.data.layer.services.AbTestSpecialistRecommendationDataServiceClient
import br.com.alice.member.api.builders.AppointmentScheduleOptionGroupsResponseBuilder
import br.com.alice.member.api.builders.SchedulingUrlBuilder
import br.com.alice.member.api.clients.LabiClient
import br.com.alice.member.api.clients.recommendedClientConfig
import br.com.alice.member.api.consumers.*
import br.com.alice.member.api.controllers.*
import br.com.alice.member.api.controllers.accreditedNetwork.AccreditedNetworkFavoriteController
import br.com.alice.member.api.controllers.accreditedNetwork.MedicalSpecialtyController
import br.com.alice.member.api.controllers.aliceAgora.AliceAgoraController
import br.com.alice.member.api.controllers.appContent.DrawerController
import br.com.alice.member.api.controllers.appContent.ScreenController
import br.com.alice.member.api.controllers.beneficiary.BeneficiaryActivationHomeScreenController
import br.com.alice.member.api.controllers.beneficiary.BeneficiaryController
import br.com.alice.member.api.controllers.bud.BudController
import br.com.alice.member.api.controllers.csat.CsatController
import br.com.alice.member.api.controllers.device.DeviceController
import br.com.alice.member.api.controllers.duquesa.DuquesaAppointmentScheduleController
import br.com.alice.member.api.controllers.duquesa.DuquesaContentController
import br.com.alice.member.api.controllers.duquesa.DuquesaPaController
import br.com.alice.member.api.controllers.duquesa.DuquesaSchedulingController
import br.com.alice.member.api.controllers.financial.FinancialDataController
import br.com.alice.member.api.controllers.onboarding.MemberOnboardingV2Controller
import br.com.alice.member.api.controllers.onboarding.v1.ContractController
import br.com.alice.member.api.controllers.onboarding.v1.HealthDeclarationAppointmentController
import br.com.alice.member.api.controllers.onboarding.v1.HealthDeclarationController
import br.com.alice.member.api.controllers.onboarding.v1.OnboardingController
import br.com.alice.member.api.controllers.onboarding.v1.PersonRegistrationController
import br.com.alice.member.api.controllers.onboarding.v1.PortabilityController
import br.com.alice.member.api.controllers.onboarding.v3.LegalGuardianController
import br.com.alice.member.api.controllers.onboarding.v3.PortabilityControllerV3
import br.com.alice.member.api.controllers.onboarding.v3.PortabilityHealthInsuranceControllerV3
import br.com.alice.member.api.controllers.referralTiimeline.ActionPlanReferralTimelineController
import br.com.alice.member.api.controllers.refund.RefundController
import br.com.alice.member.api.controllers.v2.ActionPlanControllerV2
import br.com.alice.member.api.converters.ActionPlanResponseConverter
import br.com.alice.member.api.converters.AppointmentScheduleWithStaffConverter
import br.com.alice.member.api.converters.HealthPlanResponseConverter
import br.com.alice.member.api.converters.MemberContractConverter
import br.com.alice.member.api.converters.MemberContractTermConverter
import br.com.alice.member.api.converters.actionPlanItemResponse.ActionPlanPresentationBuilder
import br.com.alice.member.api.converters.actionPlanItemResponse.v2.ActionPlanCardListConverter
import br.com.alice.member.api.converters.actionPlanItemResponse.v2.ActionPlanTaskCalendarBuilder
import br.com.alice.member.api.converters.actionPlanItemResponse.v2.ActionPlanTaskPresentationBuilder
import br.com.alice.member.api.converters.actionPlanItemResponse.v2.ActionPlanTaskSchedulingResponseConverter
import br.com.alice.member.api.converters.actionPlanItemResponse.v2.EmergencyCardListConverter
import br.com.alice.member.api.converters.actionPlanItemResponse.v2.GenericCardListConverter
import br.com.alice.member.api.converters.actionPlanItemResponse.v2.MultipleActionPlanTaskDetailsResponseConverter
import br.com.alice.member.api.converters.actionPlanItemResponse.v2.ReferralCardListConverter
import br.com.alice.member.api.converters.actionPlanItemResponse.v2.SingleActionPlanTaskDetailsResponseConverter
import br.com.alice.member.api.converters.actionPlanItemResponse.v2.TestRequestCardListConverter
import br.com.alice.member.api.converters.actionPlanItemResponse.v2.TestRequestGroupedCardListConverter
import br.com.alice.member.api.converters.appointmentSchedule.OnSiteScheduleProviderUnitScreenConverter
import br.com.alice.member.api.converters.referralTimeline.ActionPlanTaskTimelineResponseConverter
import br.com.alice.member.api.services.*
import br.com.alice.member.api.services.beneficiary.BeneficiaryActivationHomeScreenService
import br.com.alice.member.api.services.beneficiary.BeneficiaryCPTsScreenService
import br.com.alice.member.api.usecases.GetBeneficiaryUseCase
import br.com.alice.member.api.webhooks.ClearSaleWebhookReceiver
import br.com.alice.member.api.webhooks.IdwallWebhooksReceiver
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import org.koin.dsl.module
import br.com.alice.member.api.controllers.onboarding.v2.PortabilityController as PortabilityControllerV2
import br.com.alice.member.api.controllers.onboarding.v2.ShoppingController as ShoppingControllerV2

private const val timeoutInMillis = 3_000L

val ServiceModule = module(createdAtStart = true) {
    single {
        DefaultHttpClient({
            install(ContentNegotiation) { simpleGson() }
        }, timeoutInMillis = timeoutInMillis)
    }

    single<AbTestSpecialistRecommendationDataService> { AbTestSpecialistRecommendationDataServiceClient(get()) }

    // Redis
    val cache = CacheFactory.newInstance("member-api-service-cache")

    // Data Events API client
    single { DataEventClient() }

    // API internal service
    single { AuthService(get(), get(), get(), get(), get(), get()) }
    single { TestCodesService(get(), get(), get(), get(), cache) }
    single { MemberDocuments(get(), get()) }
    single { SessionNavigationService(get(), get()) }
    single { AppointmentScheduleService(get(), get(), get(), get(), get(), get(), get(), get(), get()) }
    single { ReminderService(get()) }
    single { TestResultFeedbackHealthPlanTaskService(get(), get(), get(), get(), get(), get()) }
    single { UpdateAppRulesCachedService(get()) }
    single { FindTestRequestSameScheduleOptionServiceImpl(get(), get()) }
    single { AddressInternalService(get(), get()) }
    single { MedicalSpecialtyServiceInternalService(get(), get(), get(), get()) }
    single { AccreditedNetworkFavoriteInternalService(get(), get()) }
    single {
        AccreditedNetworkInternalService(
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
        )
    }
    single {
        AccreditedNetworkSpecialistInternalService(
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
        )
    }
    single { SpecialistSchedulingInternalService(get(), get(), get(), get(), get(), get(), get(), get(), get()) }
    single { ProcedureAuthorizationService(get(), get(), get()) }
    single { ActionPlanTaskAuthorizationService(get(), get()) }
    single { MemberProductService(get(), get(), get(), get()) }
    single { MemberOnboardingConclusionService(get(), get(), get()) }
    single { BeneficiaryCPTsScreenService() }
    single { BeneficiaryActivationHomeScreenService() }
    single { LabiIntegrationService(get(), get(), get()) }

    // Controllers
    single { AppointmentScheduleEventTypeController(get(), get(), get(), get(), get(), get(), get()) }
    single { AppStateController(get()) }
    single { AuthController(get()) }
    single { AuthControllerV2(get(), get()) }
    single { ClientEventsController() }
    single { HealthController(MEMBER_ROOT_SERVICE_NAME) }
    single { HealthPlanController(get()) }
    single { HealthPlanTaskController(get(), get()) }
    single { HealthcareTeamController(get()) }
    single { MemberController(get(), get(), get(), get(), get(), get(), get()) }
    single { MemberOnboardingController(get(), get(), get(), get(), get(), get(), get(), get(), get()) }
    single { PersonRegistrationController(get(), get(), get(), get(), get()) }
    single { PersonClinicalAccountController(get(), get(), get(), get(), get()) }
    single { PersonController(get(), get(), get(), get(), get(), get(), get(), get()) }
    single {
        AppointmentScheduleController(
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            cache,
            get(),
            get(),
            get(),
            get(),
            get(),
            get()
        )
    }
    single {
        SessionsController(
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            get(),
            cache,
            get(),
            get(),
            get(),
        )
    }
    single { OnboardingController(get(), get(), get(), get(), get(), get(), get(), get()) }
    single { ProviderController(get()) }
    single { InvoiceController(get(), get(), get(), get(), get(), get()) }
    single { StaffController(get(), get()) }
    single { ContractController(get(), get(), get(), get()) }
    single { HealthDeclarationController(get(), get(), get(), get(), get()) }
    single { ChannelController(get(), get(), get(), get(), get(), get(), get(), get()) }
    single { HealthDeclarationAppointmentController(get(), get(), get(), get(), get(), get(), get()) }
    single { AppointmentScheduleOptionController(get(), get(), get(), get(), get(), get(), get(), get(), get()) }
    single { TestResultFeedbackController(get(), get(), get(), get(), get(), get()) }
    single { TestResultsController(get(), get()) }
    single { HealthGoalController(get()) }
    single { HealthDocumentController(get(), get()) }
    single { PersonTaskController(get(), get(), get()) }
    single { PromotionsController(get()) }
    single { PortabilityController(get(), get(), get(), get()) }
    single { PortabilityControllerV2(get(), get(), get(), get()) }
    single { PortabilityControllerV3(get(), get(), get(), get(), get(), get(), get(), get()) }
    single { PortabilityHealthInsuranceControllerV3(get()) }
    single { ShoppingControllerV2(get(), get(), get()) }
    single { ReminderController(get()) }
    single { HealthFormController(get(), get()) }
    single { FaqController(get(), get(), get(), get(), get()) }
    single { PartnerCampaignController(get()) }
    single { HealthScoreController(get(), get(), get()) }
    single { HealthScoreHistoryController(get()) }
    single { VideoCallController(get(), get()) }
    single { BeneficiaryController(get(), get(), get(), get(), get(), get(), get(), get(), get(), get()) }
    single { BeneficiaryActivationHomeScreenController(get(), get(), get(), get(), get()) }
    single { ExamResultController(get()) }
    single { LegalGuardianController(get(), get(), get()) }
    single { ActionPlanController(get()) }
    single { CsatController(get(), get()) }
    single { ActionPlanReferralTimelineController(get(), get(), get(), get(), get()) }
    single { DemandActionPlanController(get(), get(), get(), get()) }
    single { ScreenController(get(), get(), get(), get(), get(), get(), get(), get(), get()) }
    single { DrawerController(get(), get(), get(), get(), get()) }
    single { ActionPlanControllerV2(get(), get(), get(), get(), get(), get(), get(), get()) }
    single { EmergencyRecommendationController(get(), get(), get(), get(), get()) }
    single { LeagueMigrationController() }
    single { LeanMigrationController() }
    single { DeviceController(get()) }
    single { AppointmentScheduleContentController(get(), get()) }
    single { GloriaMariaController() }

    single { DuquesaContentController() }
    single { DuquesaPaController(get()) }
    single { DuquesaSchedulingController(get(), get()) }
    single { DuquesaAppointmentScheduleController(get(), get()) }
    single { ScreeningController(get(), get(), get(), get(), get(), get()) }
    single { RefundController(get(), get(), get(), get()) }
    single { BankController() }
    single { FinancialDataController(get()) }
    single { BudController(get()) }
    single { AddressController(get(), get()) }
    single { MapsAddressController(get()) }
    single { MedicalSpecialtyController(get()) }
    single { AccreditedNetworkFavoriteController(get()) }
    single { AccreditedNetworkProviderController(get(), get()) }
    single { ProcedureAuthorizationController(get()) }
    single { MemberOnboardingV2Controller(get(), get(), get(), get(), get(), get(), get(), get()) }
    single { PersonIdentityValidationController(get(), get()) }
    single { AliceAgoraController() }

    // Use Cases
    single { GetBeneficiaryUseCase(get(), get(), get(), get(), get(), get(), get(), get()) }

    // Consumers
    single { AppointmentScheduleEventTypeUpdatedConsumer(cache) }
    single { BeneficiaryOnboardingPhaseChangedConsumer(get()) }
    single { BeneficiaryCreatedConsumer() }
    single { ContractConsumer() }
    single { HealthScoreResultConsumer(get()) }
    single { AppContentScreenDetailUpsertedConsumer() }
    single { MemberActivatedConsumer() }
    single { MemberCancelledConsumer(get()) }
    single { HealthPlanTaskGroupPublishedConsumer(get()) }
    single { TestResultFileCreatedConsumer() }
    single { AppointmentScheduleCreatedConsumer() }
    single { AppointmentScheduleCancelledConsumer() }
    single { PersonHealthEventCreatedConsumer() }
    single { AppointmentScheduleCollisionDetectedConsumer() }
    single { AssociatedPersonConsumer() }
    single { InvoicePaymentCreatedConsumer() }
    single { PaymentDetailCreatedConsumer() }
    single { UpdateAppStateRequestedConsumer() }
    single { ActionPlanTaskUpsertedConsumer(get()) }
    single { ActionPlanTasksAcknowledgedConsumer(get()) }
    single { ExternalAppointmentScheduleConsumer() }
    single { MemberOnboardingFinishedConsumer(get(), get(), get()) }
    single { RefundEventsConsumer(get(), get()) }
    single { MoveToPhaseConsumer(get(), get(), get()) }
    single { DemandActionPlanConsumer() }
    single { PushNotificationInternalService(get(), get(), get(), get()) }

    // Clients
    single { LabiClient(DefaultHttpClient(recommendedClientConfig), cache) }

    // Webhooks
    single { IdwallWebhooksReceiver(get()) }
    single { ClearSaleWebhookReceiver(get()) }

    // Builders and converters
    single { SchedulingUrlBuilder(get(), get()) }
    single { HealthPlanResponseConverter(get()) }
    single { ActionPlanTaskSchedulingResponseConverter(get()) }
    single { SingleActionPlanTaskDetailsResponseConverter(get(), get(), get(), get()) }
    single { MultipleActionPlanTaskDetailsResponseConverter(get(), get()) }
    single { ActionPlanTaskPresentationBuilder(get(), get(), get(), get()) }
    single { ActionPlanTaskCalendarBuilder(get(), get(), get(), get(), get()) }
    single { ActionPlanPresentationBuilder(get()) }
    single { ActionPlanResponseConverter(get(), get(), get()) }
    single { ActionPlanTaskTimelineResponseConverter() }
    single { AppointmentScheduleOptionGroupsResponseBuilder(get(), get(), get()) }
    single { MemberContractTermConverter(get()) }
    single { MemberContractConverter(get()) }
    single { OnSiteScheduleProviderUnitScreenConverter(get()) }
    single { AppointmentScheduleWithStaffConverter(get()) }

    // new converters to action plan Server Driven UI model
    single { TestRequestGroupedCardListConverter(get()) }
    single { ReferralCardListConverter(get()) }
    single { TestRequestCardListConverter() }
    single { EmergencyCardListConverter() }
    single { GenericCardListConverter() }
    single { ActionPlanCardListConverter(get(), get(), get(), get(), get(), get(), get()) }

}
