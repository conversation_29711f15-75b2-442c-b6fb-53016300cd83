package br.com.alice.member.api.models.accreditedNetwork

import br.com.alice.app.content.model.BackgroundColor
import br.com.alice.app.content.model.Caption
import br.com.alice.app.content.model.Tag
import br.com.alice.app.content.model.section.CalloutSection
import br.com.alice.common.models.Gender
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkAppointmentType
import br.com.alice.data.layer.models.ConsolidatedAccreditedNetworkType
import br.com.alice.member.api.models.ContactCallOutResponse
import br.com.alice.member.api.models.MemberCardResponse
import br.com.alice.member.api.models.NavigationResponse
import br.com.alice.member.api.models.accreditedNetwork.ProviderTransport.ActionNavigation
import br.com.alice.member.api.models.appContent.Navigation
import br.com.alice.member.api.models.appContent.RemoteAction
import br.com.alice.member.api.models.channel.ChatCreateRequest
import java.time.LocalDate
import java.util.UUID

data class ProviderTransport(
    val id: UUID,
    val staffId: UUID? = null,
    val type: ConsolidatedAccreditedNetworkType,
    val name: String,
    val title: String,
    @Deprecated(
        message = "This field is for backward compatibility only (< 4.27.0).",
        replaceWith = ReplaceWith("subtitleInfo"),
    )
    val subtitle: String?,
    val subtitleInfo: SubtitleInfo? = null,
    val description: String?,
    val imageUrl: String?,
    val tag: Tag?,
    val tags: List<Tag>? = null,
    val tagsTitle: String? = null,
    val captions: List<Caption>?,
    val distance: Double?,
    val deAccreditationDate: LocalDate?,
    val hasHospitalHealthTeam: Boolean = false,
    val navigation: Navigation,
    val action: ActionNavigation? = null,
    val gender: Gender? = null,
    val scheduleAvailabilityDays: Int? = null,
    val availabilityInNextDays: List<String>? = null,
    val appointmentTypes: List<ConsolidatedAccreditedNetworkAppointmentType> = emptyList(),
    val subSpecialtyIds: List<UUID> = emptyList(),
    val isFavorite: Boolean = false,
    val isExamRedirect: Boolean? = null,
    val providerUnitId: UUID? = null
) {
    data class ActionNavigation(
        val label: String,
        val navigation: RemoteAction
    )

    data class SubtitleInfo(val text: String, val colorScheme: ColorSchema = ColorSchema.GREEN) {
        enum class ColorSchema { GREEN, MAGENTA, GRAY }
    }
}

data class ProviderDetailsTransport(
    val id: UUID,
    val type: ConsolidatedAccreditedNetworkType,
    val title: String,
    @Deprecated(
        message = "This field is for backward compatibility only (< 4.27.0).",
        replaceWith = ReplaceWith("header.name")
    )
    val name: String,
    @Deprecated(
        message = "This field is for backward compatibility only (< 4.27.0).",
        replaceWith = ReplaceWith("header.providerImageUrl"),
    )
    val providerImageUrl: String? = null,
    val header: Header,
    val cardDescription: String? = null,
    val council: String? = null,
    val specialty: String? = null,
    val subSpecialties: List<String>? = null,
    val disclaimer: ContactCallOutResponse? = null,
    val membershipCard: MemberCardResponse?,
    val addresses: List<Address>,
    val phoneNumbers: List<PhoneNumber>,
    val typeOfService: List<TypeOfService>? = null,
    val providerInformation: ProviderInformation? = null,
    val action: ActionNavigation? = null,
    val careCoordCard: CareCoordCard? = null,
    val infoCard: InfoCard? = null,
    val favoriteInfo: FavoriteInfoTransport? = null,
    val highlightInfo: List<HighlightInfo>? = null,
    val isExamRedirect: Boolean? = null,
) {
    var therapySpecialtyDisclaimer: ContactCallOutResponse? = null

    init {
        therapySpecialtyDisclaimer = disclaimer
    }

    data class ActionNavigation(
        val icon: String? = null,
        val label: String,
        val navigation: NavigationResponse
    )

    data class Address(
        val label: String,
        val address: String,
        val distance: String?
    )

    data class PhoneNumber(
        val label: String,
        val phoneNumber: String,
        val type: Type,
        val phoneUrl: String
    ) {
        enum class Type {
            PHONE, MOBILE, WHATSAPP
        }
    }

    enum class TypeOfService {
        PRESENTIAL, REMOTE
    }

    data class ProviderInformation(
        val imageUrl: String? = null,
        val curiosity: String? = null,
        val education: List<String> = emptyList(),
        val qualifications: List<String>
    )

    data class InfoCard(
        val title: String,
        val description: String,
        val imageUrl: String
    )

    data class Header(
        val name: String,
        val providerImageUrl: String? = null,
        val tag: Tag? = null,
        val description: String? = null,
    )

    data class HighlightInfo(
        val icon: String?,
        val title: String,
        val subtitle: String?
    )
}

data class SpecialistDetailsTransport(
    val id: UUID,
    val providerType: ConsolidatedAccreditedNetworkType,
    val title: String,
    val name: String,
    val imageUrl: String,
    val crm: String? = null,
    val tag: Tag? = null,
    val emptyRating: EmptySpecialistRating? = null,
    val rating: SpecialistRating? = null,
    val subspecialties: List<String> = emptyList(),
    val services: List<ServiceLocation> = emptyList(),
    val sections: List<AccreditedNetworkDetailsSection> = emptyList(),
    val cardDescription: String? = null,
    val membershipCard: MemberCardResponse? = null,
    val action: Action? = null,
    val callout: CalloutSection? = null,
    val showMembershipCardShareButton: Boolean,
    val favoriteInfo: FavoriteInfoTransport?,
) {
    data class EmptySpecialistRating(
        val value: String,
        val backgroundColor: BackgroundColor
    )

    data class SpecialistRating(
        val backgroundColor: BackgroundColor,
        val aiGeneratedComment: String? = null,
        val appointmentCount: Int,
        val evaluationsCount: Int,
        val trustScore: Double? = null,
        val recommendationScore: Double? = null
    )

    data class Action(
        val label: String,
        val icon: String? = null,
        val navigation: NavigationResponse
    )
}

enum class ProviderUnitRequestType(val title: String) {
    HOSPITAL("Hospitais"),
    HOSPITAL_CHILDREN("Hospitais Infantis"),
    LABORATORY("Laboratórios"),
    EMERGENCY_UNITY("Pronto-Socorros"),
    EMERGENCY_UNITY_CHILDREN("Pronto-Socorros Infantis"),
    MATERNITY("Maternidades"),
    VACCINE("Clínicas de vacinação");
}

data class ProviderResponse(
    val title: String,
    val providersType: String? = null,
    val providersSubtypeId: String? = null, // specialty id
    val emptyListTitle: String? = null,
    val emptyContent: EmptyContentTransport,
    val emptyListDescription: String? = null,
    val items: List<ProviderTransport> = emptyList(),
    val highlightsTitle: String? = null,
    val highlightsDescription: String? = null,
    val highlights: List<ProviderTransport> = emptyList(),
    val otherSectionTitle: String,
    val action: ActionNavigation?,
    val providers: List<ProviderSection> = emptyList(),
    val isDuquesa: Boolean = false,
    val subSpecialties: List<SubSpecialtyTransport> = emptyList(),
    val callout: CalloutSection? = null,
    val availabilityInNextDays: List<String>? = null,
) {
    data class ActionNavigation(
        val label: String,
        val navigation: NavigationResponse
    )

    data class EmptyContentTransport(
        val title: String? = null,
        val description: String? = null,
        val action: ActionNavigation? = null,
    )

    enum class Type(val title: String) {
        HOSPITAL("Hospitais"),
        HOSPITAL_CHILDREN("Hospitais Infantis"),
        LABORATORY("Laboratórios"),
        EMERGENCY_UNITY("Pronto-Socorros"),
        EMERGENCY_UNITY_CHILDREN("Pronto-Socorros Infantis"),
        SPECIALIST("Especialistas e Clínicas"),
        VACCINE("Clínicas de vacinação"),
        MATERNITY("Maternidades"),
        FAVORITE("Favoritos"),
    }
}

data class ProviderSection(
    val title: String?,
    val description: String?,
    val type: ProviderSectionType,
    val items: List<ProviderTransport>,
    val emptyCard: EmptyCardTransport? = null,
)

enum class ProviderSectionType {
    STANDARD,
    HIGHLIGHTS,
    HIGHLIGHTS_EMPTY,
}

data class CareCoordCard(
    val title: String,
    val imageUrl: String,
    val buttonLabel: String,
    val createChatParams: ChatCreateRequest
)

data class SubSpecialtyTransport(
    val id: UUID,
    val name: String,
)

data class EmptyCardTransport(
    val title: String,
    val description: String,
    val icon: String?,
    val action: ActionNavigation,
)
